/**
 * 字典数据格式化工具
 * 提供多种字典数据格式的转换和处理功能
 */

/**
 * 标准化字典数据格式
 * 将各种格式的字典数据转换为统一的 {value: label} 格式
 * @param {*} data 原始字典数据
 * @param {Object} options 格式化选项
 * @returns {Object} 标准化后的字典对象
 */
export function normalizeDictionary(data, options = {}) {
  const {
    labelKey = 'label',
    valueKey = 'value',
    childrenKey = 'children',
    flatten = false, // 是否扁平化树形结构
    separator = '_' // 扁平化时的分隔符
  } = options
  
  if (!data) return {}
  
  // 如果已经是标准格式的对象
  if (typeof data === 'object' && !Array.isArray(data) && !data[labelKey] && !data[valueKey]) {
    return data
  }
  
  const result = {}
  
  // 处理数组格式
  if (Array.isArray(data)) {
    const processItem = (item, prefix = '') => {
      if (!item || typeof item !== 'object') return
      
      const value = item[valueKey] !== undefined ? item[valueKey] : item.value
      const label = item[labelKey] !== undefined ? item[labelKey] : item.label
      
      if (value !== undefined && label !== undefined) {
        const finalValue = prefix ? `${prefix}${separator}${value}` : value
        result[finalValue] = label
      }
      
      // 处理树形结构
      if (flatten && item[childrenKey] && Array.isArray(item[childrenKey])) {
        const newPrefix = prefix ? `${prefix}${separator}${value}` : value
        item[childrenKey].forEach(child => processItem(child, newPrefix))
      }
    }
    
    data.forEach(item => processItem(item))
  }
  // 处理单个对象
  else if (typeof data === 'object') {
    const value = data[valueKey] !== undefined ? data[valueKey] : data.value
    const label = data[labelKey] !== undefined ? data[labelKey] : data.label
    
    if (value !== undefined && label !== undefined) {
      result[value] = label
    }
  }
  
  return result
}

/**
 * 格式化字典标签显示
 * @param {*} value 字典值
 * @param {Object} dictionary 字典对象
 * @param {Object} options 格式化选项
 * @returns {String} 格式化后的标签
 */
export function formatDictionaryLabel(value, dictionary, options = {}) {
  const {
    emptyText = '-',
    separator = ',',
    tagWrapper = null, // 标签包装器函数
    multipleMode = 'text', // 多选显示模式: text | tag | badge
    maxDisplay = 0, // 最大显示数量，0表示不限制
    moreText = '...' // 超出显示的文本
  } = options
  
  // 处理空值
  if (value === null || value === undefined || value === '') {
    return emptyText
  }
  
  // 确保dictionary是对象
  if (!dictionary || typeof dictionary !== 'object') {
    return String(value)
  }
  
  // 处理单个值
  if (!String(value).includes(separator)) {
    const label = dictionary[value]
    return label !== undefined ? String(label) : String(value)
  }
  
  // 处理多个值
  const values = String(value).split(separator).filter(v => v.trim())
  let labels = values.map(v => {
    const trimmedValue = v.trim()
    const label = dictionary[trimmedValue]
    return label !== undefined ? String(label) : trimmedValue
  })
  
  // 限制显示数量
  if (maxDisplay > 0 && labels.length > maxDisplay) {
    labels = labels.slice(0, maxDisplay)
    labels.push(moreText)
  }
  
  // 根据显示模式处理
  switch (multipleMode) {
    case 'tag':
      return labels.map(label => tagWrapper ? tagWrapper(label) : `<el-tag>${label}</el-tag>`)
    case 'badge':
      return labels.map(label => tagWrapper ? tagWrapper(label) : `<el-badge value="${label}"></el-badge>`)
    default:
      return labels.join(separator + ' ')
  }
}

/**
 * 转换字典数据为选项数组
 * @param {Object} dictionary 字典对象
 * @param {Object} options 转换选项
 * @returns {Array} 选项数组
 */
export function dictionaryToOptions(dictionary, options = {}) {
  const {
    labelKey = 'label',
    valueKey = 'value',
    sort = false, // 是否排序
    sortBy = 'label', // 排序字段: label | value
    sortOrder = 'asc', // 排序顺序: asc | desc
    filter = null, // 过滤函数
    transform = null // 转换函数
  } = options
  
  if (!dictionary || typeof dictionary !== 'object') {
    return []
  }
  
  let options_array = Object.entries(dictionary).map(([value, label]) => ({
    [valueKey]: value,
    [labelKey]: label
  }))
  
  // 应用过滤器
  if (typeof filter === 'function') {
    options_array = options_array.filter(filter)
  }
  
  // 应用转换器
  if (typeof transform === 'function') {
    options_array = options_array.map(transform)
  }
  
  // 排序
  if (sort) {
    options_array.sort((a, b) => {
      const aVal = a[sortBy]
      const bVal = b[sortBy]
      
      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : bVal < aVal ? -1 : 0
      } else {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
      }
    })
  }
  
  return options_array
}

/**
 * 合并多个字典
 * @param {...Object} dictionaries 字典对象
 * @returns {Object} 合并后的字典
 */
export function mergeDictionaries(...dictionaries) {
  const result = {}
  
  dictionaries.forEach(dict => {
    if (dict && typeof dict === 'object') {
      Object.assign(result, dict)
    }
  })
  
  return result
}

/**
 * 过滤字典数据
 * @param {Object} dictionary 字典对象
 * @param {Function|String|RegExp} filter 过滤条件
 * @returns {Object} 过滤后的字典
 */
export function filterDictionary(dictionary, filter) {
  if (!dictionary || typeof dictionary !== 'object') {
    return {}
  }
  
  const result = {}
  
  for (const [value, label] of Object.entries(dictionary)) {
    let shouldInclude = false
    
    if (typeof filter === 'function') {
      shouldInclude = filter(value, label)
    } else if (typeof filter === 'string') {
      shouldInclude = String(label).toLowerCase().includes(filter.toLowerCase()) ||
                     String(value).toLowerCase().includes(filter.toLowerCase())
    } else if (filter instanceof RegExp) {
      shouldInclude = filter.test(String(label)) || filter.test(String(value))
    }
    
    if (shouldInclude) {
      result[value] = label
    }
  }
  
  return result
}

/**
 * 字典数据分组
 * @param {Object} dictionary 字典对象
 * @param {Function} groupBy 分组函数
 * @returns {Object} 分组后的字典
 */
export function groupDictionary(dictionary, groupBy) {
  if (!dictionary || typeof dictionary !== 'object' || typeof groupBy !== 'function') {
    return {}
  }
  
  const groups = {}
  
  for (const [value, label] of Object.entries(dictionary)) {
    const groupKey = groupBy(value, label)
    
    if (!groups[groupKey]) {
      groups[groupKey] = {}
    }
    
    groups[groupKey][value] = label
  }
  
  return groups
}

/**
 * 验证字典数据格式
 * @param {*} data 待验证的数据
 * @param {Object} options 验证选项
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
 */
export function validateDictionary(data, options = {}) {
  const {
    allowEmpty = true,
    requiredKeys = [],
    valueType = null, // 值类型限制
    labelType = null // 标签类型限制
  } = options
  
  const errors = []
  
  // 检查数据是否存在
  if (!data) {
    if (!allowEmpty) {
      errors.push('字典数据不能为空')
    }
    return { valid: errors.length === 0, errors }
  }
  
  // 检查数据类型
  if (typeof data !== 'object') {
    errors.push('字典数据必须是对象或数组')
    return { valid: false, errors }
  }
  
  // 如果是数组，检查数组项
  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      if (!item || typeof item !== 'object') {
        errors.push(`数组项 ${index} 必须是对象`)
        return
      }
      
      // 检查必需键
      requiredKeys.forEach(key => {
        if (!(key in item)) {
          errors.push(`数组项 ${index} 缺少必需键: ${key}`)
        }
      })
    })
  }
  // 如果是对象，检查值类型
  else {
    for (const [key, value] of Object.entries(data)) {
      if (valueType && typeof key !== valueType) {
        errors.push(`键 ${key} 的类型不正确，期望 ${valueType}`)
      }
      
      if (labelType && typeof value !== labelType) {
        errors.push(`值 ${value} 的类型不正确，期望 ${labelType}`)
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 字典数据统计
 * @param {Object} dictionary 字典对象
 * @returns {Object} 统计信息
 */
export function getDictionaryStats(dictionary) {
  if (!dictionary || typeof dictionary !== 'object') {
    return {
      count: 0,
      isEmpty: true,
      keys: [],
      values: [],
      duplicateValues: []
    }
  }
  
  const keys = Object.keys(dictionary)
  const values = Object.values(dictionary)
  const valueCount = {}
  const duplicateValues = []
  
  // 统计重复值
  values.forEach(value => {
    valueCount[value] = (valueCount[value] || 0) + 1
    if (valueCount[value] === 2) {
      duplicateValues.push(value)
    }
  })
  
  return {
    count: keys.length,
    isEmpty: keys.length === 0,
    keys,
    values,
    duplicateValues,
    uniqueValueCount: Object.keys(valueCount).length,
    averageKeyLength: keys.length > 0 ? keys.reduce((sum, key) => sum + String(key).length, 0) / keys.length : 0,
    averageValueLength: values.length > 0 ? values.reduce((sum, value) => sum + String(value).length, 0) / values.length : 0
  }
}
