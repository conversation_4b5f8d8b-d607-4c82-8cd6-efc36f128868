/**
 * 字典缓存管理器
 * 提供更高级的缓存策略和管理功能
 */

import { reactive, ref } from 'vue'

// 缓存配置
const CACHE_CONFIG = {
  maxSize: 100, // 最大缓存数量
  ttl: 30 * 60 * 1000, // 缓存过期时间 (30分钟)
  cleanupInterval: 5 * 60 * 1000 // 清理间隔 (5分钟)
}

// 缓存项结构
class CacheItem {
  constructor(data, ttl = CACHE_CONFIG.ttl) {
    this.data = data
    this.timestamp = Date.now()
    this.ttl = ttl
    this.accessCount = 1
    this.lastAccess = Date.now()
  }
  
  // 检查是否过期
  isExpired() {
    return Date.now() - this.timestamp > this.ttl
  }
  
  // 更新访问信息
  updateAccess() {
    this.accessCount++
    this.lastAccess = Date.now()
  }
  
  // 获取数据
  getData() {
    this.updateAccess()
    return this.data
  }
}

// 缓存存储
const cache = reactive(new Map())

// 缓存统计
const stats = reactive({
  hits: 0,
  misses: 0,
  evictions: 0,
  cleanups: 0
})

// 定时清理器
let cleanupTimer = null

/**
 * 启动缓存清理器
 */
function startCleanup() {
  if (cleanupTimer) return
  
  cleanupTimer = setInterval(() => {
    cleanup()
  }, CACHE_CONFIG.cleanupInterval)
}

/**
 * 停止缓存清理器
 */
function stopCleanup() {
  if (cleanupTimer) {
    clearInterval(cleanupTimer)
    cleanupTimer = null
  }
}

/**
 * 清理过期缓存
 */
function cleanup() {
  const now = Date.now()
  let cleanedCount = 0
  
  for (const [key, item] of cache.entries()) {
    if (item.isExpired()) {
      cache.delete(key)
      cleanedCount++
    }
  }
  
  if (cleanedCount > 0) {
    stats.cleanups++
    console.debug(`字典缓存清理: 清除了 ${cleanedCount} 个过期项`)
  }
}

/**
 * LRU淘汰策略
 */
function evictLRU() {
  if (cache.size <= CACHE_CONFIG.maxSize) return
  
  // 找到最少使用的缓存项
  let lruKey = null
  let lruTime = Date.now()
  
  for (const [key, item] of cache.entries()) {
    if (item.lastAccess < lruTime) {
      lruTime = item.lastAccess
      lruKey = key
    }
  }
  
  if (lruKey) {
    cache.delete(lruKey)
    stats.evictions++
    console.debug(`字典缓存淘汰: 移除了最少使用的缓存项 ${lruKey}`)
  }
}

/**
 * 设置缓存
 * @param {String} key 缓存键
 * @param {*} data 缓存数据
 * @param {Number} ttl 过期时间
 */
export function setCache(key, data, ttl = CACHE_CONFIG.ttl) {
  // 检查缓存大小，必要时进行淘汰
  if (cache.size >= CACHE_CONFIG.maxSize) {
    evictLRU()
  }
  
  const item = new CacheItem(data, ttl)
  cache.set(key, item)
  
  // 启动清理器
  startCleanup()
}

/**
 * 获取缓存
 * @param {String} key 缓存键
 * @returns {*} 缓存数据，不存在或过期返回null
 */
export function getCache(key) {
  const item = cache.get(key)
  
  if (!item) {
    stats.misses++
    return null
  }
  
  if (item.isExpired()) {
    cache.delete(key)
    stats.misses++
    return null
  }
  
  stats.hits++
  return item.getData()
}

/**
 * 检查缓存是否存在且未过期
 * @param {String} key 缓存键
 * @returns {Boolean} 是否存在
 */
export function hasCache(key) {
  const item = cache.get(key)
  
  if (!item) return false
  
  if (item.isExpired()) {
    cache.delete(key)
    return false
  }
  
  return true
}

/**
 * 删除缓存
 * @param {String} key 缓存键
 * @returns {Boolean} 是否删除成功
 */
export function deleteCache(key) {
  return cache.delete(key)
}

/**
 * 清空所有缓存
 */
export function clearCache() {
  cache.clear()
  stopCleanup()
  
  // 重置统计
  stats.hits = 0
  stats.misses = 0
  stats.evictions = 0
  stats.cleanups = 0
}

/**
 * 获取缓存大小
 * @returns {Number} 缓存项数量
 */
export function getCacheSize() {
  return cache.size
}

/**
 * 获取缓存统计信息
 * @returns {Object} 统计信息
 */
export function getCacheStats() {
  const hitRate = stats.hits + stats.misses > 0 
    ? (stats.hits / (stats.hits + stats.misses) * 100).toFixed(2)
    : 0
  
  return {
    size: cache.size,
    maxSize: CACHE_CONFIG.maxSize,
    hits: stats.hits,
    misses: stats.misses,
    hitRate: `${hitRate}%`,
    evictions: stats.evictions,
    cleanups: stats.cleanups,
    ttl: CACHE_CONFIG.ttl,
    cleanupInterval: CACHE_CONFIG.cleanupInterval
  }
}

/**
 * 获取所有缓存键
 * @returns {Array} 缓存键数组
 */
export function getCacheKeys() {
  return Array.from(cache.keys())
}

/**
 * 获取缓存详细信息
 * @returns {Array} 缓存详细信息数组
 */
export function getCacheDetails() {
  const details = []
  
  for (const [key, item] of cache.entries()) {
    details.push({
      key,
      timestamp: item.timestamp,
      ttl: item.ttl,
      accessCount: item.accessCount,
      lastAccess: item.lastAccess,
      isExpired: item.isExpired(),
      age: Date.now() - item.timestamp,
      remainingTtl: Math.max(0, item.ttl - (Date.now() - item.timestamp))
    })
  }
  
  return details.sort((a, b) => b.lastAccess - a.lastAccess)
}

/**
 * 更新缓存配置
 * @param {Object} config 新配置
 */
export function updateCacheConfig(config) {
  Object.assign(CACHE_CONFIG, config)
  
  // 如果修改了清理间隔，重启清理器
  if (config.cleanupInterval && cleanupTimer) {
    stopCleanup()
    startCleanup()
  }
}

/**
 * 获取缓存配置
 * @returns {Object} 当前配置
 */
export function getCacheConfig() {
  return { ...CACHE_CONFIG }
}

/**
 * 预热缓存
 * @param {Array} items 预热数据数组 [{key, data, ttl}]
 */
export function warmupCache(items) {
  items.forEach(({ key, data, ttl }) => {
    setCache(key, data, ttl)
  })
}

// 导出缓存实例（用于调试）
export { cache, stats }
