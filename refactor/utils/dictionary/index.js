/**
 * 字典处理工具
 * 提供统一的字典数据获取、缓存、格式化功能
 */

import { ref, reactive } from 'vue'
import { getGlobalConfig } from '../config/index.js'

// 字典缓存存储
const dictionaryCache = reactive(new Map())

// 字典加载状态管理
const loadingStates = reactive(new Map())

// 字典错误状态管理
const errorStates = reactive(new Map())

/**
 * 获取字典数据
 * @param {String|Function} dicId 字典ID或获取函数
 * @param {Object} options 选项配置
 * @returns {Promise} 字典数据Promise
 */
export async function getDictionary(dicId, options = {}) {
  const {
    cache = true,
    format = true,
    keys = ['label', 'value']
  } = options
  
  // 生成缓存键
  const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
  
  // 如果启用缓存且存在缓存数据，直接返回
  if (cache && dictionaryCache.has(cacheKey)) {
    return dictionaryCache.get(cacheKey)
  }
  
  // 如果正在加载中，返回加载Promise
  if (loadingStates.has(cacheKey)) {
    return loadingStates.get(cacheKey)
  }
  
  // 获取全局字典获取方法
  const globalGetDictionary = getGlobalConfig('getDictionary')
  
  if (!globalGetDictionary && typeof dicId !== 'function') {
    throw new Error('未配置全局字典获取方法，且dicId不是函数')
  }
  
  // 创建加载Promise
  const loadingPromise = (async () => {
    try {
      // 清除错误状态
      errorStates.delete(cacheKey)
      
      let rawData
      if (typeof dicId === 'function') {
        // 如果dicId是函数，直接调用
        rawData = await dicId()
      } else {
        // 使用全局字典获取方法
        rawData = await globalGetDictionary(dicId)
      }
      
      // 格式化数据
      const formattedData = format ? formatDictionaryData(rawData, keys) : rawData
      
      // 缓存数据
      if (cache) {
        dictionaryCache.set(cacheKey, formattedData)
      }
      
      return formattedData
    } catch (error) {
      // 记录错误状态
      errorStates.set(cacheKey, error)
      throw error
    } finally {
      // 清除加载状态
      loadingStates.delete(cacheKey)
    }
  })()
  
  // 记录加载状态
  loadingStates.set(cacheKey, loadingPromise)
  
  return loadingPromise
}

/**
 * 批量获取字典数据
 * @param {Array} dicIds 字典ID数组
 * @param {Object} options 选项配置
 * @returns {Promise} 字典数据Map
 */
export async function getBatchDictionaries(dicIds, options = {}) {
  const promises = dicIds.map(dicId => 
    getDictionary(dicId, options).then(data => [dicId, data]).catch(error => [dicId, null])
  )
  
  const results = await Promise.all(promises)
  return new Map(results)
}

/**
 * 格式化字典数据
 * @param {Array|Object} data 原始字典数据
 * @param {Array} keys 键名映射 [labelKey, valueKey]
 * @returns {Object} 格式化后的字典对象 {value: label}
 */
export function formatDictionaryData(data, keys = ['label', 'value']) {
  if (!data) return {}
  
  const [labelKey, valueKey] = keys
  const result = {}
  
  // 如果是数组格式
  if (Array.isArray(data)) {
    data.forEach(item => {
      if (item && typeof item === 'object') {
        const value = item[valueKey] !== undefined ? item[valueKey] : item.value
        const label = item[labelKey] !== undefined ? item[labelKey] : item.label
        if (value !== undefined && label !== undefined) {
          result[value] = label
        }
      }
    })
  }
  // 如果是对象格式，直接返回
  else if (typeof data === 'object') {
    return data
  }
  
  return result
}

/**
 * 根据值获取字典标签
 * @param {*} value 字典值
 * @param {Object|String|Function} dictionary 字典数据或字典ID
 * @param {Object} options 选项配置
 * @returns {String} 字典标签
 */
export function getDictionaryLabel(value, dictionary, options = {}) {
  const { emptyText = '-', separator = ',' } = options
  
  if (value === null || value === undefined || value === '') {
    return emptyText
  }
  
  // 如果dictionary是对象，直接使用
  if (typeof dictionary === 'object' && dictionary !== null) {
    // 处理多选情况
    if (typeof value === 'string' && value.includes(separator)) {
      const values = value.split(separator).filter(v => v.trim())
      const labels = values.map(v => dictionary[v.trim()] || v.trim())
      return labels.join(separator + ' ')
    }
    
    return dictionary[value] || value
  }
  
  // 如果dictionary是字符串或函数，需要异步获取
  // 这种情况下返回原值，实际使用中应该通过响应式数据处理
  return value
}

/**
 * 检查字典是否正在加载
 * @param {String} dicId 字典ID
 * @returns {Boolean} 是否正在加载
 */
export function isDictionaryLoading(dicId) {
  const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
  return loadingStates.has(cacheKey)
}

/**
 * 检查字典是否加载失败
 * @param {String} dicId 字典ID
 * @returns {Boolean} 是否加载失败
 */
export function isDictionaryError(dicId) {
  const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
  return errorStates.has(cacheKey)
}

/**
 * 获取字典错误信息
 * @param {String} dicId 字典ID
 * @returns {Error|null} 错误信息
 */
export function getDictionaryError(dicId) {
  const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
  return errorStates.get(cacheKey) || null
}

/**
 * 清除字典缓存
 * @param {String} dicId 字典ID，不传则清除所有缓存
 */
export function clearDictionaryCache(dicId) {
  if (dicId) {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    dictionaryCache.delete(cacheKey)
    errorStates.delete(cacheKey)
  } else {
    dictionaryCache.clear()
    errorStates.clear()
  }
}

/**
 * 预加载字典数据
 * @param {Array} dicIds 字典ID数组
 * @param {Object} options 选项配置
 */
export async function preloadDictionaries(dicIds, options = {}) {
  const promises = dicIds.map(dicId => 
    getDictionary(dicId, options).catch(error => {
      console.warn(`预加载字典失败 [${dicId}]:`, error)
      return null
    })
  )
  
  await Promise.all(promises)
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
export function getCacheStats() {
  return {
    cacheSize: dictionaryCache.size,
    loadingCount: loadingStates.size,
    errorCount: errorStates.size,
    cacheKeys: Array.from(dictionaryCache.keys()),
    loadingKeys: Array.from(loadingStates.keys()),
    errorKeys: Array.from(errorStates.keys())
  }
}

// 导出缓存实例（用于调试）
export { dictionaryCache, loadingStates, errorStates }
