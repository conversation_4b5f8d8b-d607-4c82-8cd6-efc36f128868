/**
 * 类型定义和检查工具
 * 提供类型检查、类型转换和类型验证功能
 */

/**
 * 获取值的精确类型
 * @param {*} value 值
 * @returns {String} 类型字符串
 */
export function getType(value) {
  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase()
}

/**
 * 检查是否为字符串
 * @param {*} value 值
 * @returns {Boolean} 是否为字符串
 */
export function isString(value) {
  return typeof value === 'string'
}

/**
 * 检查是否为数字
 * @param {*} value 值
 * @returns {Boolean} 是否为数字
 */
export function isNumber(value) {
  return typeof value === 'number' && !isNaN(value)
}

/**
 * 检查是否为整数
 * @param {*} value 值
 * @returns {Boolean} 是否为整数
 */
export function isInteger(value) {
  return Number.isInteger(value)
}

/**
 * 检查是否为浮点数
 * @param {*} value 值
 * @returns {Boolean} 是否为浮点数
 */
export function isFloat(value) {
  return isNumber(value) && !isInteger(value)
}

/**
 * 检查是否为布尔值
 * @param {*} value 值
 * @returns {Boolean} 是否为布尔值
 */
export function isBoolean(value) {
  return typeof value === 'boolean'
}

/**
 * 检查是否为数组
 * @param {*} value 值
 * @returns {Boolean} 是否为数组
 */
export function isArray(value) {
  return Array.isArray(value)
}

/**
 * 检查是否为对象
 * @param {*} value 值
 * @returns {Boolean} 是否为对象
 */
export function isObject(value) {
  return value !== null && typeof value === 'object' && !isArray(value)
}

/**
 * 检查是否为纯对象
 * @param {*} value 值
 * @returns {Boolean} 是否为纯对象
 */
export function isPlainObject(value) {
  if (!isObject(value)) return false
  
  // 检查是否有原型
  if (Object.getPrototypeOf(value) === null) return true
  
  // 检查是否是Object构造函数创建的
  let proto = value
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto)
  }
  
  return Object.getPrototypeOf(value) === proto
}

/**
 * 检查是否为函数
 * @param {*} value 值
 * @returns {Boolean} 是否为函数
 */
export function isFunction(value) {
  return typeof value === 'function'
}

/**
 * 检查是否为日期
 * @param {*} value 值
 * @returns {Boolean} 是否为日期
 */
export function isDate(value) {
  return value instanceof Date && !isNaN(value.getTime())
}

/**
 * 检查是否为正则表达式
 * @param {*} value 值
 * @returns {Boolean} 是否为正则表达式
 */
export function isRegExp(value) {
  return value instanceof RegExp
}

/**
 * 检查是否为错误对象
 * @param {*} value 值
 * @returns {Boolean} 是否为错误对象
 */
export function isError(value) {
  return value instanceof Error
}

/**
 * 检查是否为Promise
 * @param {*} value 值
 * @returns {Boolean} 是否为Promise
 */
export function isPromise(value) {
  return value instanceof Promise || (
    value !== null &&
    typeof value === 'object' &&
    typeof value.then === 'function'
  )
}

/**
 * 检查是否为Symbol
 * @param {*} value 值
 * @returns {Boolean} 是否为Symbol
 */
export function isSymbol(value) {
  return typeof value === 'symbol'
}

/**
 * 检查是否为undefined
 * @param {*} value 值
 * @returns {Boolean} 是否为undefined
 */
export function isUndefined(value) {
  return value === undefined
}

/**
 * 检查是否为null
 * @param {*} value 值
 * @returns {Boolean} 是否为null
 */
export function isNull(value) {
  return value === null
}

/**
 * 检查是否为null或undefined
 * @param {*} value 值
 * @returns {Boolean} 是否为null或undefined
 */
export function isNullOrUndefined(value) {
  return value === null || value === undefined
}

/**
 * 检查是否为空值（null、undefined、空字符串、空数组、空对象）
 * @param {*} value 值
 * @returns {Boolean} 是否为空值
 */
export function isEmpty(value) {
  if (isNullOrUndefined(value)) return true
  if (isString(value)) return value.trim() === ''
  if (isArray(value)) return value.length === 0
  if (isObject(value)) return Object.keys(value).length === 0
  return false
}

/**
 * 检查是否为非空值
 * @param {*} value 值
 * @returns {Boolean} 是否为非空值
 */
export function isNotEmpty(value) {
  return !isEmpty(value)
}

/**
 * 检查是否为有效的数字字符串
 * @param {*} value 值
 * @returns {Boolean} 是否为有效的数字字符串
 */
export function isNumericString(value) {
  return isString(value) && !isNaN(Number(value)) && !isNaN(parseFloat(value))
}

/**
 * 检查是否为有效的JSON字符串
 * @param {*} value 值
 * @returns {Boolean} 是否为有效的JSON字符串
 */
export function isJsonString(value) {
  if (!isString(value)) return false
  try {
    JSON.parse(value)
    return true
  } catch {
    return false
  }
}

/**
 * 检查是否为有效的URL
 * @param {*} value 值
 * @returns {Boolean} 是否为有效的URL
 */
export function isUrl(value) {
  if (!isString(value)) return false
  try {
    new URL(value)
    return true
  } catch {
    return false
  }
}

/**
 * 检查是否为有效的邮箱地址
 * @param {*} value 值
 * @returns {Boolean} 是否为有效的邮箱地址
 */
export function isEmail(value) {
  if (!isString(value)) return false
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(value)
}

/**
 * 类型转换为字符串
 * @param {*} value 值
 * @param {String} defaultValue 默认值
 * @returns {String} 字符串
 */
export function toString(value, defaultValue = '') {
  if (isNullOrUndefined(value)) return defaultValue
  if (isString(value)) return value
  if (isNumber(value) || isBoolean(value)) return String(value)
  if (isObject(value) || isArray(value)) return JSON.stringify(value)
  return String(value)
}

/**
 * 类型转换为数字
 * @param {*} value 值
 * @param {Number} defaultValue 默认值
 * @returns {Number} 数字
 */
export function toNumber(value, defaultValue = 0) {
  if (isNumber(value)) return value
  if (isNumericString(value)) return Number(value)
  return defaultValue
}

/**
 * 类型转换为整数
 * @param {*} value 值
 * @param {Number} defaultValue 默认值
 * @returns {Number} 整数
 */
export function toInteger(value, defaultValue = 0) {
  const num = toNumber(value, defaultValue)
  return Math.floor(num)
}

/**
 * 类型转换为布尔值
 * @param {*} value 值
 * @param {Boolean} defaultValue 默认值
 * @returns {Boolean} 布尔值
 */
export function toBoolean(value, defaultValue = false) {
  if (isBoolean(value)) return value
  if (isString(value)) {
    const lower = value.toLowerCase()
    if (lower === 'true' || lower === '1') return true
    if (lower === 'false' || lower === '0') return false
  }
  if (isNumber(value)) return value !== 0
  if (isNullOrUndefined(value)) return defaultValue
  return Boolean(value)
}

/**
 * 类型转换为数组
 * @param {*} value 值
 * @param {Array} defaultValue 默认值
 * @returns {Array} 数组
 */
export function toArray(value, defaultValue = []) {
  if (isArray(value)) return value
  if (isNullOrUndefined(value)) return defaultValue
  return [value]
}

/**
 * 类型转换为对象
 * @param {*} value 值
 * @param {Object} defaultValue 默认值
 * @returns {Object} 对象
 */
export function toObject(value, defaultValue = {}) {
  if (isObject(value)) return value
  if (isJsonString(value)) {
    try {
      return JSON.parse(value)
    } catch {
      return defaultValue
    }
  }
  return defaultValue
}

/**
 * 类型转换为日期
 * @param {*} value 值
 * @param {Date} defaultValue 默认值
 * @returns {Date} 日期
 */
export function toDate(value, defaultValue = null) {
  if (isDate(value)) return value
  if (isString(value) || isNumber(value)) {
    const date = new Date(value)
    return isDate(date) ? date : defaultValue
  }
  return defaultValue
}

/**
 * 安全的类型转换
 * @param {*} value 值
 * @param {String} type 目标类型
 * @param {*} defaultValue 默认值
 * @returns {*} 转换后的值
 */
export function safeCast(value, type, defaultValue) {
  try {
    switch (type.toLowerCase()) {
      case 'string':
        return toString(value, defaultValue)
      case 'number':
        return toNumber(value, defaultValue)
      case 'integer':
        return toInteger(value, defaultValue)
      case 'boolean':
        return toBoolean(value, defaultValue)
      case 'array':
        return toArray(value, defaultValue)
      case 'object':
        return toObject(value, defaultValue)
      case 'date':
        return toDate(value, defaultValue)
      default:
        return value
    }
  } catch {
    return defaultValue
  }
}

// 导出所有类型检查和转换函数
export default {
  getType,
  isString,
  isNumber,
  isInteger,
  isFloat,
  isBoolean,
  isArray,
  isObject,
  isPlainObject,
  isFunction,
  isDate,
  isRegExp,
  isError,
  isPromise,
  isSymbol,
  isUndefined,
  isNull,
  isNullOrUndefined,
  isEmpty,
  isNotEmpty,
  isNumericString,
  isJsonString,
  isUrl,
  isEmail,
  toString,
  toNumber,
  toInteger,
  toBoolean,
  toArray,
  toObject,
  toDate,
  safeCast
}
