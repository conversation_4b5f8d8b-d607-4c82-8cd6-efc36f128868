/**
 * 通用工具函数
 * 提供项目中常用的工具函数和辅助方法
 */

import helpers from './helpers.js'
import constants from './constants.js'
import types from './types.js'

/**
 * 深度克隆对象
 * @param {*} obj 要克隆的对象
 * @returns {*} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (obj instanceof RegExp) {
    return new RegExp(obj)
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 深度比较两个对象是否相等
 * @param {*} obj1 对象1
 * @param {*} obj2 对象2
 * @returns {Boolean} 是否相等
 */
export function isDeepEqual(obj1, obj2) {
  if (obj1 === obj2) {
    return true
  }
  
  if (obj1 == null || obj2 == null) {
    return obj1 === obj2
  }
  
  if (typeof obj1 !== typeof obj2) {
    return false
  }
  
  if (typeof obj1 !== 'object') {
    return obj1 === obj2
  }
  
  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false
  }
  
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  
  if (keys1.length !== keys2.length) {
    return false
  }
  
  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false
    }
    
    if (!isDeepEqual(obj1[key], obj2[key])) {
      return false
    }
  }
  
  return true
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} wait 等待时间（毫秒）
 * @param {Boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    
    const callNow = immediate && !timeout
    
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 生成唯一ID
 * @param {String} prefix 前缀
 * @returns {String} 唯一ID
 */
export function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 生成UUID
 * @returns {String} UUID
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 格式化文件大小
 * @param {Number} bytes 字节数
 * @param {Number} decimals 小数位数
 * @returns {String} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {Number} num 数字
 * @param {Object} options 格式化选项
 * @returns {String} 格式化后的数字
 */
export function formatNumber(num, options = {}) {
  const {
    decimals = 2,
    thousandsSeparator = ',',
    decimalSeparator = '.',
    prefix = '',
    suffix = ''
  } = options
  
  if (isNaN(num)) return ''
  
  const number = Number(num).toFixed(decimals)
  const parts = number.split('.')
  
  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator)
  
  let result = parts.join(decimalSeparator)
  
  return prefix + result + suffix
}

/**
 * 获取对象属性值（支持嵌套路径）
 * @param {Object} obj 对象
 * @param {String} path 属性路径，如 'user.name'
 * @param {*} defaultValue 默认值
 * @returns {*} 属性值
 */
export function getNestedValue(obj, path, defaultValue = undefined) {
  if (!obj || !path) return defaultValue
  
  const keys = path.split('.')
  let result = obj
  
  for (const key of keys) {
    if (result === null || result === undefined) {
      return defaultValue
    }
    result = result[key]
  }
  
  return result !== undefined ? result : defaultValue
}

/**
 * 设置对象属性值（支持嵌套路径）
 * @param {Object} obj 对象
 * @param {String} path 属性路径，如 'user.name'
 * @param {*} value 属性值
 * @returns {Object} 修改后的对象
 */
export function setNestedValue(obj, path, value) {
  if (!obj || !path) return obj
  
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
  return obj
}

/**
 * 数组去重
 * @param {Array} arr 数组
 * @param {String|Function} key 去重依据的键名或函数
 * @returns {Array} 去重后的数组
 */
export function uniqueArray(arr, key) {
  if (!Array.isArray(arr)) return []
  
  if (!key) {
    return [...new Set(arr)]
  }
  
  const seen = new Set()
  return arr.filter(item => {
    const keyValue = typeof key === 'function' ? key(item) : item[key]
    if (seen.has(keyValue)) {
      return false
    }
    seen.add(keyValue)
    return true
  })
}

/**
 * 数组分组
 * @param {Array} arr 数组
 * @param {String|Function} key 分组依据的键名或函数
 * @returns {Object} 分组后的对象
 */
export function groupArray(arr, key) {
  if (!Array.isArray(arr)) return {}
  
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key]
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {})
}

/**
 * 数组排序
 * @param {Array} arr 数组
 * @param {String|Function} key 排序依据的键名或函数
 * @param {String} order 排序顺序 'asc' | 'desc'
 * @returns {Array} 排序后的数组
 */
export function sortArray(arr, key, order = 'asc') {
  if (!Array.isArray(arr)) return []
  
  return [...arr].sort((a, b) => {
    let aVal = typeof key === 'function' ? key(a) : a[key]
    let bVal = typeof key === 'function' ? key(b) : b[key]
    
    // 处理null和undefined
    if (aVal == null && bVal == null) return 0
    if (aVal == null) return order === 'asc' ? -1 : 1
    if (bVal == null) return order === 'asc' ? 1 : -1
    
    // 数字比较
    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return order === 'asc' ? aVal - bVal : bVal - aVal
    }
    
    // 字符串比较
    aVal = String(aVal).toLowerCase()
    bVal = String(bVal).toLowerCase()
    
    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * 检查值是否为空
 * @param {*} value 值
 * @returns {Boolean} 是否为空
 */
export function isEmpty(value) {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 检查值是否不为空
 * @param {*} value 值
 * @returns {Boolean} 是否不为空
 */
export function isNotEmpty(value) {
  return !isEmpty(value)
}

/**
 * 延迟执行
 * @param {Number} ms 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试函数
 * @param {Function} fn 要重试的函数
 * @param {Number} times 重试次数
 * @param {Number} delay 重试间隔（毫秒）
 * @returns {Promise} Promise对象
 */
export async function retry(fn, times = 3, delay = 1000) {
  let lastError
  
  for (let i = 0; i < times; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      if (i < times - 1) {
        await sleep(delay)
      }
    }
  }
  
  throw lastError
}

// 导出子模块
export { helpers, constants, types }

// 默认导出
export default {
  deepClone,
  isDeepEqual,
  debounce,
  throttle,
  generateId,
  generateUUID,
  formatFileSize,
  formatNumber,
  getNestedValue,
  setNestedValue,
  uniqueArray,
  groupArray,
  sortArray,
  isEmpty,
  isNotEmpty,
  sleep,
  retry,
  helpers,
  constants,
  types
}
