/**
 * 常量定义
 * 定义项目中使用的各种常量
 */

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
}

// 组件尺寸
export const COMPONENT_SIZES = {
  LARGE: 'large',
  DEFAULT: 'default',
  SMALL: 'small'
}

// 组件类型
export const COMPONENT_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  DEFAULT: 'default'
}

// 表单控件类型
export const FORM_CONTROL_TYPES = {
  INPUT: 'input',
  INPUT_TEXT: 'inputText',
  TEXTAREA: 'textarea',
  INPUT_NUMBER: 'inputNumber',
  PASSWORD: 'password',
  SELECT: 'select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  SWITCH: 'switch',
  SLIDER: 'slider',
  DATE: 'date',
  DATETIME: 'datetime',
  TIME: 'time',
  DATE_RANGE: 'daterange',
  DATETIME_RANGE: 'datetimerange',
  MONTH_RANGE: 'monthrange',
  YEAR_RANGE: 'yearrange',
  UPLOAD_FILE: 'uploadFile',
  UPLOAD_PIC: 'uploadPic',
  UPLOAD_FILE_ALL: 'uploadFileAll',
  IMAGE: 'image',
  CASCADER: 'cascader',
  TREE_SELECT: 'treeSelect',
  COLOR_PICKER: 'colorPicker',
  RATE: 'rate',
  DIVIDER: 'divider',
  TEXT: 'text',
  HTML: 'html',
  SLOT: 'slot',
  CHILDREN: 'children'
}

// 日期类型
export const DATE_TYPES = [
  'time',
  'timeselect',
  'year',
  'years',
  'month',
  'dates',
  'date',
  'week',
  'months',
  'datetime',
  'datetimerange',
  'daterange',
  'monthrange',
  'yearrange'
]

// 验证触发方式
export const VALIDATION_TRIGGERS = {
  BLUR: 'blur',
  CHANGE: 'change',
  INPUT: 'input',
  FOCUS: 'focus'
}

// 表格选择类型
export const TABLE_SELECTION_TYPES = {
  CHECKBOX: 'checkbox',
  RADIO: 'radio'
}

// 表格操作位置
export const TABLE_OPERATION_POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom',
  BOTH: 'both'
}

// 对齐方式
export const ALIGN_TYPES = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right'
}

// 文件类型
export const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  DOCUMENT: 'document',
  ARCHIVE: 'archive',
  OTHER: 'other'
}

// 文件扩展名映射
export const FILE_EXTENSIONS = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'],
  AUDIO: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz']
}

// 文件大小限制（MB）
export const FILE_SIZE_LIMITS = {
  IMAGE: 10,
  VIDEO: 100,
  AUDIO: 20,
  DOCUMENT: 50,
  DEFAULT: 10
}

// 缓存配置
export const CACHE_CONFIG = {
  MAX_SIZE: 100,
  TTL: 30 * 60 * 1000, // 30分钟
  CLEANUP_INTERVAL: 5 * 60 * 1000 // 5分钟
}

// 分页配置
export const PAGINATION_CONFIG = {
  PAGE_SIZES: [10, 20, 50, 100],
  DEFAULT_PAGE_SIZE: 20,
  DEFAULT_LAYOUT: 'total, sizes, prev, pager, next, jumper'
}

// 响应式断点
export const BREAKPOINTS = {
  XS: 768,
  SM: 992,
  MD: 1200,
  LG: 1920
}

// 主题色彩
export const THEME_COLORS = {
  PRIMARY: '#409EFF',
  SUCCESS: '#67C23A',
  WARNING: '#E6A23C',
  DANGER: '#F56C6C',
  INFO: '#909399',
  TEXT_PRIMARY: '#303133',
  TEXT_REGULAR: '#606266',
  TEXT_SECONDARY: '#909399',
  TEXT_PLACEHOLDER: '#C0C4CC',
  BORDER_BASE: '#DCDFE6',
  BORDER_LIGHT: '#E4E7ED',
  BORDER_LIGHTER: '#EBEEF5',
  BORDER_EXTRA_LIGHT: '#F2F6FC',
  BACKGROUND_BASE: '#F5F7FA'
}

// 动画持续时间
export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500
}

// 层级（z-index）
export const Z_INDEX = {
  DROPDOWN: 1000,
  TOOLTIP: 2000,
  MODAL: 3000,
  MESSAGE: 4000,
  LOADING: 5000
}

// 键盘按键码
export const KEY_CODES = {
  ENTER: 13,
  ESC: 27,
  SPACE: 32,
  LEFT: 37,
  UP: 38,
  RIGHT: 39,
  DOWN: 40,
  DELETE: 46,
  BACKSPACE: 8,
  TAB: 9
}

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  MOBILE: /^1[3-9]\d{9}$/,
  ID_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,6}$/,
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
  STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z][a-zA-Z0-9_]{2,15}$/,
  QQ: /^[1-9][0-9]{4,10}$/,
  WECHAT: /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/,
  IP: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  PORT: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
  BANK_CARD: /^[1-9]\d{12,18}$/,
  POSTCODE: /^[1-9]\d{5}$/,
  LICENSE_PLATE: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
}

// 错误消息
export const ERROR_MESSAGES = {
  REQUIRED: '此字段为必填项',
  EMAIL: '请输入正确的邮箱地址',
  MOBILE: '请输入正确的手机号码',
  ID_CARD: '请输入正确的身份证号码',
  PASSWORD: '密码至少8位，包含字母和数字',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  PERMISSION_DENIED: '权限不足，无法执行此操作',
  FILE_TOO_LARGE: '文件大小超出限制',
  FILE_TYPE_ERROR: '文件类型不支持',
  UPLOAD_FAILED: '文件上传失败'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVE: '保存成功',
  UPDATE: '更新成功',
  DELETE: '删除成功',
  UPLOAD: '上传成功',
  COPY: '复制成功',
  EXPORT: '导出成功',
  IMPORT: '导入成功'
}

// 确认消息
export const CONFIRM_MESSAGES = {
  DELETE: '确定要删除这条记录吗？',
  BATCH_DELETE: '确定要删除选中的记录吗？',
  SAVE: '确定要保存当前修改吗？',
  CANCEL: '确定要取消当前操作吗？',
  RESET: '确定要重置表单吗？'
}

// 本地存储键名
export const STORAGE_KEYS = {
  USER_INFO: 'user_info',
  TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  LANGUAGE: 'language',
  THEME: 'theme',
  SETTINGS: 'settings'
}

// 事件名称
export const EVENT_NAMES = {
  RESIZE: 'resize',
  SCROLL: 'scroll',
  CLICK: 'click',
  CHANGE: 'change',
  INPUT: 'input',
  FOCUS: 'focus',
  BLUR: 'blur',
  KEYDOWN: 'keydown',
  KEYUP: 'keyup'
}

// 默认导出所有常量
export default {
  HTTP_STATUS,
  COMPONENT_SIZES,
  COMPONENT_TYPES,
  FORM_CONTROL_TYPES,
  DATE_TYPES,
  VALIDATION_TRIGGERS,
  TABLE_SELECTION_TYPES,
  TABLE_OPERATION_POSITIONS,
  ALIGN_TYPES,
  FILE_TYPES,
  FILE_EXTENSIONS,
  FILE_SIZE_LIMITS,
  CACHE_CONFIG,
  PAGINATION_CONFIG,
  BREAKPOINTS,
  THEME_COLORS,
  ANIMATION_DURATION,
  Z_INDEX,
  KEY_CODES,
  REGEX_PATTERNS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  CONFIRM_MESSAGES,
  STORAGE_KEYS,
  EVENT_NAMES
}
