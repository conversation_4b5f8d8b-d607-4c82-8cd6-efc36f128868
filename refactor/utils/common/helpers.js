/**
 * 辅助函数集合
 * 提供各种实用的辅助函数
 */

/**
 * 日期格式化
 * @param {Date|String|Number} date 日期
 * @param {String} format 格式字符串
 * @returns {String} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds)
}

/**
 * 相对时间格式化
 * @param {Date|String|Number} date 日期
 * @returns {String} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const minute = 60 * 1000
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const year = day * 365
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 字符串首字母大写
 * @param {String} str 字符串
 * @returns {String} 首字母大写的字符串
 */
export function capitalize(str) {
  if (!str || typeof str !== 'string') return ''
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 字符串转驼峰命名
 * @param {String} str 字符串
 * @returns {String} 驼峰命名的字符串
 */
export function toCamelCase(str) {
  if (!str || typeof str !== 'string') return ''
  return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
}

/**
 * 字符串转短横线命名
 * @param {String} str 字符串
 * @returns {String} 短横线命名的字符串
 */
export function toKebabCase(str) {
  if (!str || typeof str !== 'string') return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

/**
 * 字符串转下划线命名
 * @param {String} str 字符串
 * @returns {String} 下划线命名的字符串
 */
export function toSnakeCase(str) {
  if (!str || typeof str !== 'string') return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
}

/**
 * 截断字符串
 * @param {String} str 字符串
 * @param {Number} length 最大长度
 * @param {String} suffix 后缀
 * @returns {String} 截断后的字符串
 */
export function truncate(str, length = 50, suffix = '...') {
  if (!str || typeof str !== 'string') return ''
  if (str.length <= length) return str
  return str.substring(0, length) + suffix
}

/**
 * 移除HTML标签
 * @param {String} html HTML字符串
 * @returns {String} 纯文本字符串
 */
export function stripHtml(html) {
  if (!html || typeof html !== 'string') return ''
  return html.replace(/<[^>]*>/g, '')
}

/**
 * 转义HTML字符
 * @param {String} str 字符串
 * @returns {String} 转义后的字符串
 */
export function escapeHtml(str) {
  if (!str || typeof str !== 'string') return ''
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  }
  return str.replace(/[&<>"']/g, char => map[char])
}

/**
 * 反转义HTML字符
 * @param {String} str 字符串
 * @returns {String} 反转义后的字符串
 */
export function unescapeHtml(str) {
  if (!str || typeof str !== 'string') return ''
  const map = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'"
  }
  return str.replace(/&(amp|lt|gt|quot|#39);/g, entity => map[entity])
}

/**
 * 生成随机字符串
 * @param {Number} length 长度
 * @param {String} chars 字符集
 * @returns {String} 随机字符串
 */
export function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @param {Boolean} integer 是否为整数
 * @returns {Number} 随机数
 */
export function randomNumber(min = 0, max = 100, integer = true) {
  const random = Math.random() * (max - min) + min
  return integer ? Math.floor(random) : random
}

/**
 * 数组随机排序
 * @param {Array} arr 数组
 * @returns {Array} 随机排序后的数组
 */
export function shuffleArray(arr) {
  if (!Array.isArray(arr)) return []
  const result = [...arr]
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[result[i], result[j]] = [result[j], result[i]]
  }
  return result
}

/**
 * 数组随机取样
 * @param {Array} arr 数组
 * @param {Number} count 取样数量
 * @returns {Array} 取样结果
 */
export function sampleArray(arr, count = 1) {
  if (!Array.isArray(arr) || count <= 0) return []
  if (count >= arr.length) return [...arr]
  
  const shuffled = shuffleArray(arr)
  return shuffled.slice(0, count)
}

/**
 * 颜色格式转换
 * @param {String} color 颜色值
 * @param {String} format 目标格式 'hex' | 'rgb' | 'hsl'
 * @returns {String} 转换后的颜色值
 */
export function convertColor(color, format = 'hex') {
  if (!color || typeof color !== 'string') return ''
  
  // 简单的颜色转换实现
  // 这里只是示例，实际项目中可能需要更完整的颜色转换库
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx.fillStyle = color
  const computedColor = ctx.fillStyle
  
  if (format === 'hex') {
    return computedColor
  }
  
  // 其他格式转换需要更复杂的实现
  return computedColor
}

/**
 * 获取文件扩展名
 * @param {String} filename 文件名
 * @returns {String} 扩展名
 */
export function getFileExtension(filename) {
  if (!filename || typeof filename !== 'string') return ''
  const lastDot = filename.lastIndexOf('.')
  return lastDot === -1 ? '' : filename.substring(lastDot + 1).toLowerCase()
}

/**
 * 获取文件名（不含扩展名）
 * @param {String} filename 文件名
 * @returns {String} 文件名
 */
export function getFileName(filename) {
  if (!filename || typeof filename !== 'string') return ''
  const lastDot = filename.lastIndexOf('.')
  const lastSlash = Math.max(filename.lastIndexOf('/'), filename.lastIndexOf('\\'))
  const start = lastSlash + 1
  const end = lastDot === -1 ? filename.length : lastDot
  return filename.substring(start, end)
}

/**
 * 检查是否为移动设备
 * @returns {Boolean} 是否为移动设备
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 检查是否为iOS设备
 * @returns {Boolean} 是否为iOS设备
 */
export function isIOS() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检查是否为Android设备
 * @returns {Boolean} 是否为Android设备
 */
export function isAndroid() {
  return /Android/.test(navigator.userAgent)
}

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器信息
 */
export function getBrowserInfo() {
  const ua = navigator.userAgent
  const browsers = {
    chrome: /Chrome/.test(ua) && !/Edge/.test(ua),
    firefox: /Firefox/.test(ua),
    safari: /Safari/.test(ua) && !/Chrome/.test(ua),
    edge: /Edge/.test(ua),
    ie: /MSIE|Trident/.test(ua)
  }
  
  const browser = Object.keys(browsers).find(key => browsers[key]) || 'unknown'
  
  return {
    name: browser,
    userAgent: ua,
    isMobile: isMobile(),
    isIOS: isIOS(),
    isAndroid: isAndroid()
  }
}

/**
 * 复制文本到剪贴板
 * @param {String} text 要复制的文本
 * @returns {Promise} Promise对象
 */
export async function copyToClipboard(text) {
  if (!text || typeof text !== 'string') {
    throw new Error('复制内容不能为空')
  }
  
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text)
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    return new Promise((resolve, reject) => {
      if (document.execCommand('copy')) {
        resolve()
      } else {
        reject(new Error('复制失败'))
      }
      document.body.removeChild(textArea)
    })
  }
}

/**
 * 下载文件
 * @param {String} url 文件URL
 * @param {String} filename 文件名
 */
export function downloadFile(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename || 'download'
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 导出所有辅助函数
export default {
  formatDate,
  formatRelativeTime,
  capitalize,
  toCamelCase,
  toKebabCase,
  toSnakeCase,
  truncate,
  stripHtml,
  escapeHtml,
  unescapeHtml,
  randomString,
  randomNumber,
  shuffleArray,
  sampleArray,
  convertColor,
  getFileExtension,
  getFileName,
  isMobile,
  isIOS,
  isAndroid,
  getBrowserInfo,
  copyToClipboard,
  downloadFile
}
