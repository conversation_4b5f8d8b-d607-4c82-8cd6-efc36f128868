/**
 * 表格组件默认配置
 * 基于原有co-table配置进行优化和扩展
 */

// 日期类型定义
export const dateTypes = [
  'time', 'timeselect', 'year', 'years', 'month', 'dates', 
  'date', 'week', 'months', 'datetime', 'datetimerange', 
  'daterange', 'monthrange', 'yearrange'
]

// 表格默认配置
const tableConfig = {
  // 权限配置
  permission: {
    key: 'perms', // 权限字段名
    enabled: true // 是否启用权限控制
  },
  
  // 搜索配置
  search: {
    enabled: true, // 是否启用搜索
    style: null, // 搜索区域样式
    buttons: {
      search: { 
        type: 'primary', 
        icon: 'Search', 
        text: '搜索' 
      },
      reset: { 
        type: 'default', 
        icon: 'Refresh', 
        text: '重置' 
      }
    },
    // 搜索表单配置
    form: {
      inline: true,
      labelWidth: '80px',
      size: 'default'
    }
  },
  
  // 加载配置
  loading: {
    enabled: true, // 是否启用加载状态
    text: '加载中...', // 加载文本
    spinner: null // 自定义加载图标
  },
  
  // 表格属性配置
  table: {
    // Element Plus表格默认属性
    border: true,
    stripe: false,
    size: 'default',
    fit: true,
    showHeader: true,
    highlightCurrentRow: false,
    rowKey: '_uuid',
    // 表头样式
    headerCellStyle: { 
      backgroundColor: '#f5f7fa', 
      color: '#303133',
      fontWeight: 'bold'
    },
    // 行样式
    rowStyle: null,
    rowClassName: null,
    cellStyle: null,
    cellClassName: null
  },
  
  // 字典配置
  dictionary: {
    enabled: true, // 是否启用字典功能
    cache: true, // 是否缓存字典数据
    keys: ['label', 'value'], // 字典数据键名映射
    loadingText: '加载中...', // 字典加载时显示文本
    errorText: '加载失败' // 字典加载失败时显示文本
  },
  
  // 分页配置
  pagination: {
    enabled: true, // 是否启用分页
    layout: 'total, sizes, prev, pager, next, jumper',
    pageSizes: [10, 20, 50, 100],
    pageSize: 20,
    // 请求参数映射
    request: {
      current: 'current', // 当前页参数名
      size: 'size' // 页大小参数名
    },
    // 响应数据映射
    response: {
      current: 'current', // 当前页字段名
      pages: 'pages', // 总页数字段名
      size: 'size', // 页大小字段名
      total: 'total', // 总记录数字段名
      records: 'list' // 数据列表字段名
    }
  },
  
  // 操作按钮配置
  operations: {
    enabled: true, // 是否启用操作功能
    position: 'top', // 操作按钮位置: top | bottom | both
    align: 'left', // 按钮对齐方式: left | center | right
    // 默认操作按钮
    buttons: {
      add: {
        text: '新增',
        type: 'primary',
        icon: 'Plus',
        permission: null
      },
      edit: {
        text: '编辑',
        type: 'default',
        icon: 'Edit',
        permission: null
      },
      delete: {
        text: '删除',
        type: 'danger',
        icon: 'Delete',
        permission: null,
        confirm: true,
        confirmText: '确定删除选中的数据吗？'
      }
    }
  },
  
  // 选择配置
  selection: {
    enabled: false, // 是否启用多选
    type: 'checkbox', // 选择类型: checkbox | radio
    reserveSelection: false, // 是否保留选择状态
    selectable: null, // 自定义选择判断函数
    // 选择文本配置
    text: {
      selectAll: '全选',
      selected: '已选择 {count} 项'
    }
  },
  
  // 文件处理配置
  file: {
    upload: null, // 文件上传处理函数
    download: null, // 文件下载处理函数
    preview: null // 文件预览处理函数
  },
  
  // 表单编辑配置
  editing: {
    enabled: false, // 是否启用表格内编辑
    trigger: 'click', // 编辑触发方式: click | dblclick
    validateOnChange: true, // 是否在值改变时验证
    // 支持的编辑类型
    types: ['input', 'select', 'switch', 'inputNumber', 'date', 'datetime']
  },
  
  // 样式配置
  style: {
    height: null, // 表格高度
    maxHeight: null, // 表格最大高度
    highlightColor: '#409eff', // 高亮颜色
    // CSS变量
    cssVars: {
      '--table-border-color': '#ebeef5',
      '--table-header-bg': '#f5f7fa',
      '--table-row-hover-bg': '#f5f7fa'
    }
  },
  
  // 扩展配置
  extensions: {
    // 树形数据配置
    tree: {
      enabled: false,
      childrenKey: 'children',
      hasChildrenKey: 'hasChildren',
      indent: 16,
      lazy: false,
      load: null
    },
    // 拖拽配置
    draggable: {
      enabled: false,
      handle: '.drag-handle'
    },
    // 导出配置
    export: {
      enabled: false,
      filename: 'table-data',
      format: 'xlsx' // xlsx | csv | json
    }
  }
}

export default tableConfig
