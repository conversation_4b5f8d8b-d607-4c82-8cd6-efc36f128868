/**
 * 表单组件默认配置
 * 基于原有co-form配置进行优化和扩展
 */

// 表单控件类型定义
export const formControlTypes = [
  'input', 'inputText', 'textarea', 'inputNumber', 'password',
  'select', 'checkbox', 'radio', 'switch', 'slider',
  'date', 'datetime', 'time', 'daterange', 'datetimerange', 'monthrange', 'yearrange',
  'uploadFile', 'uploadPic', 'uploadFileAll', 'image',
  'cascader', 'treeSelect', 'colorPicker', 'rate',
  'divider', 'text', 'html', 'slot', 'children'
]

// 表单默认配置
const formConfig = {
  // 表单基础配置
  form: {
    labelWidth: '145px', // 标签宽度
    labelPosition: 'right', // 标签位置: right | left | top
    size: 'default', // 表单尺寸: large | default | small
    inline: false, // 是否行内表单
    disabled: false, // 是否禁用整个表单
    showLabel: true, // 是否显示标签
    validateOnRuleChange: false, // 是否在规则改变时验证
    hideRequiredAsterisk: false, // 是否隐藏必填星号
    showMessage: true, // 是否显示验证错误信息
    inlineMessage: false, // 是否以行内形式展示验证信息
    statusIcon: false, // 是否在输入框中显示校验结果反馈图标
    scrollToError: false // 是否滚动到第一个错误字段
  },
  
  // 表单项默认配置
  formItem: {
    labelWidth: null, // 继承表单配置
    required: false, // 是否必填
    rules: [], // 验证规则
    error: '', // 表单域验证错误信息
    showMessage: true, // 是否显示校验错误信息
    inlineMessage: false, // 是否以行内形式展示校验信息
    size: null // 继承表单配置
  },
  
  // 控件默认属性配置
  controls: {
    // 输入框
    input: {
      type: 'text',
      placeholder: '请输入',
      clearable: true,
      showPassword: false,
      showWordLimit: false,
      prefixIcon: null,
      suffixIcon: null
    },
    
    // 文本域
    textarea: {
      placeholder: '请输入',
      rows: 4,
      autosize: false,
      resize: 'vertical',
      showWordLimit: false
    },
    
    // 数字输入框
    inputNumber: {
      min: -Infinity,
      max: Infinity,
      step: 1,
      precision: null,
      size: 'default',
      controls: true,
      controlsPosition: 'right',
      placeholder: '请输入数字'
    },
    
    // 选择器
    select: {
      placeholder: '请选择',
      clearable: true,
      multiple: false,
      multipleLimit: 0,
      filterable: false,
      allowCreate: false,
      remote: false,
      remoteMethod: null,
      loading: false,
      loadingText: '加载中',
      noMatchText: '无匹配数据',
      noDataText: '无数据',
      popperClass: 'zIndex-5000',
      reserveKeyword: false,
      defaultFirstOption: false,
      automaticDropdown: false,
      fitInputWidth: false
    },
    
    // 开关
    switch: {
      disabled: false,
      width: 40,
      activeText: '',
      inactiveText: '',
      activeValue: true,
      inactiveValue: false,
      activeColor: '#409EFF',
      inactiveColor: '#C0CCDA',
      borderColor: '',
      name: '',
      validateEvent: true,
      beforeChange: null
    },
    
    // 日期选择器
    date: {
      type: 'date',
      placeholder: '请选择日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      clearable: true,
      disabled: false,
      editable: true,
      size: 'default',
      popperClass: 'zIndex-5000',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      defaultValue: null,
      defaultTime: null,
      disabledDate: null,
      shortcuts: [],
      cellClassName: null,
      teleported: true
    },
    
    // 复选框
    checkbox: {
      disabled: false,
      border: false,
      size: 'default',
      name: '',
      trueLabel: null,
      falseLabel: null,
      checked: false,
      indeterminate: false,
      validateEvent: true
    },
    
    // 单选框
    radio: {
      disabled: false,
      border: false,
      size: 'default',
      name: '',
      label: null
    },
    
    // 级联选择器
    cascader: {
      placeholder: '请选择',
      disabled: false,
      clearable: true,
      showAllLevels: true,
      collapseTags: false,
      collapseTagsTooltip: false,
      separator: ' / ',
      filterable: false,
      filterMethod: null,
      debounce: 300,
      beforeFilter: null,
      popperClass: 'zIndex-5000',
      teleported: true
    },
    
    // 文件上传
    upload: {
      action: '',
      headers: {},
      method: 'post',
      multiple: false,
      data: {},
      name: 'file',
      withCredentials: false,
      showFileList: true,
      drag: false,
      accept: '',
      onPreview: null,
      onRemove: null,
      onSuccess: null,
      onError: null,
      onProgress: null,
      onChange: null,
      beforeUpload: null,
      beforeRemove: null,
      listType: 'text',
      autoUpload: true,
      fileList: [],
      httpRequest: null,
      disabled: false,
      limit: null,
      onExceed: null
    }
  },
  
  // 验证配置
  validation: {
    // 内置验证规则
    rules: {
      required: { required: true, message: '此字段为必填项', trigger: 'blur' },
      email: { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
      url: { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' },
      number: { type: 'number', message: '请输入数字', trigger: 'blur' },
      integer: { type: 'integer', message: '请输入整数', trigger: 'blur' },
      float: { type: 'float', message: '请输入浮点数', trigger: 'blur' },
      array: { type: 'array', message: '请选择至少一项', trigger: 'change' },
      date: { type: 'date', message: '请选择日期', trigger: 'change' },
      boolean: { type: 'boolean', message: '请选择', trigger: 'change' }
    },
    
    // 验证触发方式
    triggers: {
      input: 'blur',
      select: 'change',
      checkbox: 'change',
      radio: 'change',
      switch: 'change',
      date: 'change',
      upload: 'change'
    },
    
    // 自定义验证器
    validators: {},
    
    // 验证消息配置
    messages: {
      required: '此字段为必填项',
      min: '长度不能少于 {min} 个字符',
      max: '长度不能超过 {max} 个字符',
      pattern: '格式不正确',
      email: '请输入正确的邮箱地址',
      url: '请输入正确的URL地址',
      number: '请输入数字',
      integer: '请输入整数',
      float: '请输入浮点数'
    }
  },
  
  // 文件处理配置
  file: {
    // 文件源处理函数
    fileSrc: null,
    // 文件上传函数
    fileUpload: null,
    // 支持的文件类型
    accept: {
      image: 'image/*',
      document: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx',
      video: 'video/*',
      audio: 'audio/*'
    },
    // 文件大小限制 (MB)
    maxSize: {
      image: 10,
      document: 50,
      video: 100,
      audio: 20
    }
  },
  
  // 字典配置
  dictionary: {
    enabled: true, // 是否启用字典功能
    cache: true, // 是否缓存字典数据
    keys: ['label', 'value'], // 字典数据键名映射
    loadingText: '加载中...', // 字典加载时显示文本
    errorText: '加载失败' // 字典加载失败时显示文本
  },
  
  // 布局配置
  layout: {
    // 栅格配置
    grid: {
      gutter: 20, // 栅格间隔
      span: 24, // 栅格占据的列数
      offset: 0, // 栅格左侧的间隔格数
      push: 0, // 栅格向右移动格数
      pull: 0, // 栅格向左移动格数
      xs: null, // <768px 响应式栅格
      sm: null, // ≥768px 响应式栅格
      md: null, // ≥992px 响应式栅格
      lg: null, // ≥1200px 响应式栅格
      xl: null // ≥1920px 响应式栅格
    },
    
    // 表单项布局
    formItem: {
      span: 24, // 默认占满一行
      labelCol: { span: 6 }, // 标签列配置
      wrapperCol: { span: 18 } // 控件列配置
    }
  },
  
  // 第三方组件配置
  components: {},
  
  // 不生成验证规则的类型
  noValidationTypes: ['divider', 'text', 'html', 'slot'],
  
  // 不返回值的类型
  noValueTypes: ['divider', 'text', 'html'],
  
  // 扩展配置
  extensions: {
    // 动态表单配置
    dynamic: {
      enabled: false,
      addText: '添加',
      removeText: '删除',
      maxItems: 10,
      minItems: 1
    },
    
    // 分步表单配置
    steps: {
      enabled: false,
      current: 0,
      direction: 'horizontal',
      labelPlacement: 'horizontal',
      size: 'default'
    }
  }
}

export default formConfig
