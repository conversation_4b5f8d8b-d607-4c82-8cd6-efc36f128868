/**
 * 统一配置管理系统
 * 提供组件配置的统一入口和管理机制
 */

import tableConfig from './table-config.js'
import formConfig from './form-config.js'
import detailConfig from './detail-config.js'

// 全局配置存储
const globalConfig = {
  // 字典获取方法
  getDictionary: null,
  // 文件处理方法
  getFiles: null,
  // 权限字段配置
  permissionKey: 'perms',
  // 默认尺寸
  size: 'default',
  // 默认语言
  locale: 'zh-cn'
}

/**
 * 设置全局配置
 * @param {Object} config 配置对象
 */
export function setGlobalConfig(config) {
  Object.assign(globalConfig, config)
}

/**
 * 获取全局配置
 * @param {String} key 配置键名
 * @returns {*} 配置值
 */
export function getGlobalConfig(key) {
  return key ? globalConfig[key] : globalConfig
}

/**
 * 合并配置
 * @param {Object} defaultConfig 默认配置
 * @param {Object} userConfig 用户配置
 * @returns {Object} 合并后的配置
 */
export function mergeConfig(defaultConfig, userConfig) {
  if (!userConfig) return defaultConfig
  
  const merged = { ...defaultConfig }
  
  for (const key in userConfig) {
    if (userConfig[key] !== undefined) {
      if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key]) && userConfig[key] !== null) {
        merged[key] = mergeConfig(merged[key] || {}, userConfig[key])
      } else {
        merged[key] = userConfig[key]
      }
    }
  }
  
  return merged
}

/**
 * 深度克隆配置对象
 * @param {Object} obj 要克隆的对象
 * @returns {Object} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
}

/**
 * 验证配置对象
 * @param {Object} config 配置对象
 * @param {Object} schema 验证模式
 * @returns {Object} 验证结果 { valid: boolean, errors: Array }
 */
export function validateConfig(config, schema) {
  const errors = []
  
  // 简单的配置验证逻辑
  for (const key in schema) {
    const rule = schema[key]
    const value = config[key]
    
    if (rule.required && (value === undefined || value === null)) {
      errors.push(`配置项 ${key} 是必需的`)
    }
    
    if (value !== undefined && rule.type && typeof value !== rule.type) {
      errors.push(`配置项 ${key} 类型错误，期望 ${rule.type}，实际 ${typeof value}`)
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 导出各组件配置
export {
  tableConfig,
  formConfig,
  detailConfig
}

// 导出默认配置
export default {
  table: tableConfig,
  form: formConfig,
  detail: detailConfig,
  global: globalConfig
}
