/**
 * 详情组件默认配置
 * 基于原有pro-detail配置进行优化和扩展
 */

// 详情字段类型定义
export const detailFieldTypes = [
  'text', 'html', 'image', 'video', 'file', 'link',
  'tag', 'badge', 'progress', 'rate', 'switch',
  'descrip', 'fileList', 'records', 'customDec'
]

// 详情组件默认配置
const detailConfig = {
  // 基础配置
  basic: {
    border: true, // 是否显示边框
    size: 'default', // 组件尺寸: large | default | small
    direction: 'vertical', // 布局方向: vertical | horizontal
    column: 2, // 每行显示的字段数量
    labelWidth: 140, // 标签宽度
    labelAlign: 'right', // 标签对齐方式: left | center | right
    colon: true, // 是否显示冒号
    layout: 'horizontal' // 布局模式: horizontal | vertical | inline
  },
  
  // 折叠面板配置
  collapse: {
    enabled: true, // 是否启用折叠功能
    accordion: false, // 是否手风琴模式
    expandAll: true, // 是否默认展开所有面板
    // 面板标题配置
    title: {
      showIcon: true, // 是否显示图标
      fontSize: '18px', // 标题字体大小
      fontWeight: '900', // 标题字体粗细
      color: null, // 标题颜色
      showBlock: true, // 是否显示标题前的色块
      blockColor: 'var(--el-color-primary)' // 色块颜色
    }
  },
  
  // 字段显示配置
  field: {
    // 空值显示
    emptyText: '-',
    emptyColor: '#909399',
    
    // 标签配置
    label: {
      width: 140, // 标签宽度
      align: 'right', // 对齐方式
      color: '#606266', // 标签颜色
      fontWeight: 'bold', // 字体粗细
      backgroundColor: '#f5f7fa', // 背景色（边框模式下）
      required: false, // 是否显示必填标记
      colon: true // 是否显示冒号
    },
    
    // 内容配置
    content: {
      color: '#303133', // 内容颜色
      fontSize: '14px', // 字体大小
      lineHeight: '1.5', // 行高
      wordBreak: 'break-all', // 换行方式
      whiteSpace: 'normal' // 空白处理
    },
    
    // 范围值配置
    range: {
      separator: ' ~ ', // 分隔符
      unit: '', // 单位
      emptyStartText: '', // 起始值为空时的文本
      emptyEndText: '' // 结束值为空时的文本
    }
  },
  
  // 文件展示配置
  file: {
    // 图片配置
    image: {
      width: 160, // 图片宽度
      height: 'auto', // 图片高度
      fit: 'cover', // 图片适应方式
      preview: true, // 是否支持预览
      lazy: true, // 是否懒加载
      placeholder: '暂无图片', // 占位文本
      errorText: '图片加载失败', // 错误文本
      // 预览配置
      previewSrcList: [], // 预览图片列表
      initialIndex: 0, // 初始预览索引
      hideOnClickModal: false, // 点击遮罩是否关闭预览
      appendToBody: true, // 是否插入到body
      zIndex: 2000, // 层级
      closeOnPressEscape: true // 按ESC是否关闭
    },
    
    // 视频配置
    video: {
      width: '100%', // 视频宽度
      height: 'auto', // 视频高度
      controls: true, // 是否显示控制条
      autoplay: false, // 是否自动播放
      loop: false, // 是否循环播放
      muted: false, // 是否静音
      poster: null, // 封面图
      preload: 'metadata', // 预加载: none | metadata | auto
      placeholder: '暂无视频', // 占位文本
      errorText: '视频加载失败', // 错误文本
      // 下载配置
      downloadable: true, // 是否支持下载
      downloadText: '下载', // 下载按钮文本
      previewText: '预览' // 预览按钮文本
    },
    
    // 文件列表配置
    fileList: {
      showUserName: false, // 是否显示用户名
      showOperation: true, // 是否显示操作按钮
      downloadable: true, // 是否支持下载
      previewable: true, // 是否支持预览
      // 文件图标配置
      icons: {
        pdf: 'document',
        doc: 'document',
        docx: 'document',
        xls: 'document',
        xlsx: 'document',
        ppt: 'document',
        pptx: 'document',
        txt: 'document',
        zip: 'folder',
        rar: 'folder',
        default: 'document'
      }
    }
  },
  
  // 字典配置
  dictionary: {
    enabled: true, // 是否启用字典功能
    cache: true, // 是否缓存字典数据
    keys: ['label', 'value'], // 字典数据键名映射
    loadingText: '加载中...', // 字典加载时显示文本
    errorText: '加载失败', // 字典加载失败时显示文本
    // 多选显示配置
    multiple: {
      separator: ',', // 分隔符
      tagType: 'default', // 标签类型
      tagSize: 'default', // 标签大小
      maxTags: 0, // 最大显示标签数，0表示不限制
      collapseTags: false, // 是否折叠标签
      collapseTagsTooltip: false // 是否显示折叠标签的提示
    }
  },
  
  // 审核记录配置
  audit: {
    enabled: true, // 是否启用审核记录
    title: '审核记录', // 标题
    showAvatar: true, // 是否显示头像
    showTime: true, // 是否显示时间
    timeFormat: 'YYYY-MM-DD HH:mm:ss', // 时间格式
    // 状态配置
    status: {
      pending: { text: '待审核', color: '#E6A23C' },
      approved: { text: '已通过', color: '#67C23A' },
      rejected: { text: '已拒绝', color: '#F56C6C' },
      cancelled: { text: '已取消', color: '#909399' }
    }
  },
  
  // 标签配置
  tag: {
    type: 'default', // 标签类型: success | info | warning | danger
    size: 'default', // 标签大小: large | default | small
    effect: 'light', // 标签效果: dark | light | plain
    closable: false, // 是否可关闭
    disableTransitions: false, // 是否禁用渐变动画
    hit: false, // 是否有边框描边
    color: null, // 背景色
    round: false // 是否圆形
  },
  
  // 徽章配置
  badge: {
    value: null, // 显示值
    max: 99, // 最大值
    isDot: false, // 是否显示小圆点
    hidden: false, // 是否隐藏
    type: 'danger', // 类型: primary | success | warning | danger | info
    showZero: false, // 当数值为0时是否显示
    color: null, // 自定义颜色
    offset: [0, 0] // 偏移量
  },
  
  // 进度条配置
  progress: {
    type: 'line', // 类型: line | circle | dashboard
    percentage: 0, // 百分比
    status: null, // 状态: success | exception | warning
    strokeWidth: 6, // 进度条的宽度
    textInside: false, // 进度条显示文字内置在进度条内
    showText: true, // 是否显示进度数值或状态图标
    color: null, // 进度条背景色
    width: 126, // 环形进度条画布宽度
    format: null, // 指定进度条文字内容
    indeterminate: false, // 是否为动画进度条
    duration: 3 // 控制动画进度条速度
  },
  
  // 评分配置
  rate: {
    max: 5, // 最大分值
    disabled: true, // 是否为只读
    allowHalf: false, // 是否允许半选
    lowThreshold: 2, // 低分和中等分数的界限值
    highThreshold: 4, // 高分和中等分数的界限值
    colors: ['#F7BA2A', '#F7BA2A', '#F7BA2A'], // icon 的颜色
    voidColor: '#C6D1DE', // 未选中 icon 的颜色
    disabledVoidColor: '#EFF2F7', // 只读时未选中 icon 的颜色
    iconClasses: ['el-icon-star-on', 'el-icon-star-on', 'el-icon-star-on'], // icon 的类名
    voidIconClass: 'el-icon-star-off', // 未选中 icon 的类名
    disabledVoidIconClass: 'el-icon-star-on', // 只读时未选中 icon 的类名
    showText: false, // 是否显示辅助文字
    showScore: false, // 是否显示当前分数
    textColor: '#1F2D3D', // 辅助文字的颜色
    texts: ['极差', '失望', '一般', '满意', '惊喜'], // 辅助文字数组
    scoreTemplate: '{value}' // 分数显示模板
  },
  
  // 样式配置
  style: {
    // 容器样式
    container: {
      padding: '15px',
      backgroundColor: '#fff',
      borderRadius: '8px',
      marginBottom: '15px'
    },
    
    // 标题样式
    title: {
      fontSize: '18px',
      fontWeight: '900',
      color: '#303133',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center'
    },
    
    // 字段样式
    field: {
      marginBottom: '16px',
      display: 'flex',
      alignItems: 'flex-start'
    },
    
    // 边框样式
    border: {
      enabled: true,
      color: '#ebeef5',
      width: '1px',
      style: 'solid'
    }
  },
  
  // 响应式配置
  responsive: {
    enabled: true, // 是否启用响应式
    breakpoints: {
      xs: 768, // 超小屏幕
      sm: 992, // 小屏幕
      md: 1200, // 中等屏幕
      lg: 1920 // 大屏幕
    },
    // 不同屏幕下的列数配置
    columns: {
      xs: 1,
      sm: 1,
      md: 2,
      lg: 2,
      xl: 3
    }
  },
  
  // 扩展配置
  extensions: {
    // 打印配置
    print: {
      enabled: false,
      title: '详情信息',
      showDate: true,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    
    // 导出配置
    export: {
      enabled: false,
      format: 'pdf', // pdf | word | excel
      filename: 'detail',
      includeImages: true
    }
  }
}

export default detailConfig
