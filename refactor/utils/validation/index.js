/**
 * 验证工具系统
 * 提供统一的表单验证、数据验证和规则管理功能
 */

import { ref, reactive, computed } from 'vue'
import rules from './rules.js'
import validators from './validators.js'

// 验证状态管理
const validationStates = reactive(new Map())

// 验证错误信息管理
const validationErrors = reactive(new Map())

/**
 * 创建验证实例
 * @param {String} formId 表单ID
 * @param {Object} options 验证选项
 * @returns {Object} 验证实例
 */
export function createValidator(formId, options = {}) {
  const {
    rules: customRules = {},
    messages: customMessages = {},
    validateOnChange = true,
    validateOnBlur = true,
    stopOnFirstError = false
  } = options
  
  // 合并规则和消息
  const mergedRules = { ...rules, ...customRules }
  const mergedMessages = { ...rules.messages, ...customMessages }
  
  // 验证状态
  const state = reactive({
    isValidating: false,
    isValid: true,
    errors: {},
    touched: {},
    dirty: {}
  })
  
  // 存储验证状态
  validationStates.set(formId, state)
  
  /**
   * 验证单个字段
   * @param {String} field 字段名
   * @param {*} value 字段值
   * @param {Array|Object} fieldRules 字段验证规则
   * @returns {Promise} 验证结果
   */
  const validateField = async (field, value, fieldRules) => {
    if (!fieldRules) return { valid: true, errors: [] }
    
    const errors = []
    const rulesToValidate = Array.isArray(fieldRules) ? fieldRules : [fieldRules]
    
    for (const rule of rulesToValidate) {
      try {
        const result = await validateRule(field, value, rule, mergedMessages)
        if (!result.valid) {
          errors.push(result.message)
          if (stopOnFirstError) break
        }
      } catch (error) {
        errors.push(`验证规则执行错误: ${error.message}`)
        if (stopOnFirstError) break
      }
    }
    
    // 更新字段验证状态
    state.errors[field] = errors
    state.touched[field] = true
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证整个表单
   * @param {Object} data 表单数据
   * @param {Object} formRules 表单验证规则
   * @returns {Promise} 验证结果
   */
  const validateForm = async (data, formRules) => {
    state.isValidating = true
    const fieldResults = {}
    const allErrors = {}
    
    try {
      // 并行验证所有字段
      const validationPromises = Object.entries(formRules).map(async ([field, fieldRules]) => {
        const value = getFieldValue(data, field)
        const result = await validateField(field, value, fieldRules)
        fieldResults[field] = result
        if (!result.valid) {
          allErrors[field] = result.errors
        }
      })
      
      await Promise.all(validationPromises)
      
      // 更新整体验证状态
      state.errors = allErrors
      state.isValid = Object.keys(allErrors).length === 0
      
      return {
        valid: state.isValid,
        errors: allErrors,
        fieldResults
      }
    } finally {
      state.isValidating = false
    }
  }
  
  /**
   * 清除验证状态
   * @param {String} field 字段名，不传则清除所有
   */
  const clearValidation = (field) => {
    if (field) {
      delete state.errors[field]
      delete state.touched[field]
      delete state.dirty[field]
    } else {
      state.errors = {}
      state.touched = {}
      state.dirty = {}
      state.isValid = true
    }
  }
  
  /**
   * 标记字段为脏数据
   * @param {String} field 字段名
   */
  const markFieldDirty = (field) => {
    state.dirty[field] = true
  }
  
  /**
   * 标记字段为已触摸
   * @param {String} field 字段名
   */
  const markFieldTouched = (field) => {
    state.touched[field] = true
  }
  
  /**
   * 获取字段错误信息
   * @param {String} field 字段名
   * @returns {Array} 错误信息数组
   */
  const getFieldErrors = (field) => {
    return state.errors[field] || []
  }
  
  /**
   * 检查字段是否有错误
   * @param {String} field 字段名
   * @returns {Boolean} 是否有错误
   */
  const hasFieldError = (field) => {
    return !!(state.errors[field] && state.errors[field].length > 0)
  }
  
  /**
   * 检查字段是否已触摸
   * @param {String} field 字段名
   * @returns {Boolean} 是否已触摸
   */
  const isFieldTouched = (field) => {
    return !!state.touched[field]
  }
  
  /**
   * 检查字段是否为脏数据
   * @param {String} field 字段名
   * @returns {Boolean} 是否为脏数据
   */
  const isFieldDirty = (field) => {
    return !!state.dirty[field]
  }
  
  // 返回验证实例
  return {
    state: readonly(state),
    validateField,
    validateForm,
    clearValidation,
    markFieldDirty,
    markFieldTouched,
    getFieldErrors,
    hasFieldError,
    isFieldTouched,
    isFieldDirty,
    
    // 计算属性
    isValidating: computed(() => state.isValidating),
    isValid: computed(() => state.isValid),
    hasErrors: computed(() => Object.keys(state.errors).length > 0),
    errorCount: computed(() => Object.keys(state.errors).length),
    touchedFields: computed(() => Object.keys(state.touched)),
    dirtyFields: computed(() => Object.keys(state.dirty))
  }
}

/**
 * 验证单个规则
 * @param {String} field 字段名
 * @param {*} value 字段值
 * @param {Object} rule 验证规则
 * @param {Object} messages 错误消息
 * @returns {Promise} 验证结果
 */
async function validateRule(field, value, rule, messages) {
  // 处理必填验证
  if (rule.required) {
    if (isEmpty(value)) {
      return {
        valid: false,
        message: rule.message || messages.required || `${field}是必填项`
      }
    }
  }
  
  // 如果值为空且不是必填，跳过其他验证
  if (isEmpty(value) && !rule.required) {
    return { valid: true }
  }
  
  // 处理类型验证
  if (rule.type) {
    const typeValidator = validators[rule.type]
    if (typeValidator && !typeValidator(value)) {
      return {
        valid: false,
        message: rule.message || messages[rule.type] || `${field}格式不正确`
      }
    }
  }
  
  // 处理长度验证
  if (rule.min !== undefined || rule.max !== undefined) {
    const length = getValueLength(value)
    
    if (rule.min !== undefined && length < rule.min) {
      return {
        valid: false,
        message: rule.message || `${field}长度不能少于${rule.min}个字符`
      }
    }
    
    if (rule.max !== undefined && length > rule.max) {
      return {
        valid: false,
        message: rule.message || `${field}长度不能超过${rule.max}个字符`
      }
    }
  }
  
  // 处理正则验证
  if (rule.pattern) {
    const regex = rule.pattern instanceof RegExp ? rule.pattern : new RegExp(rule.pattern)
    if (!regex.test(String(value))) {
      return {
        valid: false,
        message: rule.message || messages.pattern || `${field}格式不正确`
      }
    }
  }
  
  // 处理自定义验证器
  if (rule.validator && typeof rule.validator === 'function') {
    try {
      const result = await rule.validator(value, rule)
      if (result !== true) {
        return {
          valid: false,
          message: typeof result === 'string' ? result : (rule.message || `${field}验证失败`)
        }
      }
    } catch (error) {
      return {
        valid: false,
        message: rule.message || `${field}验证出错: ${error.message}`
      }
    }
  }
  
  return { valid: true }
}

/**
 * 获取字段值
 * @param {Object} data 数据对象
 * @param {String} field 字段路径
 * @returns {*} 字段值
 */
function getFieldValue(data, field) {
  if (!field || !data) return undefined
  
  // 支持嵌套字段路径，如 'user.name'
  const keys = field.split('.')
  let value = data
  
  for (const key of keys) {
    if (value === null || value === undefined) return undefined
    value = value[key]
  }
  
  return value
}

/**
 * 检查值是否为空
 * @param {*} value 值
 * @returns {Boolean} 是否为空
 */
function isEmpty(value) {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 获取值的长度
 * @param {*} value 值
 * @returns {Number} 长度
 */
function getValueLength(value) {
  if (value === null || value === undefined) return 0
  if (typeof value === 'string') return value.length
  if (Array.isArray(value)) return value.length
  if (typeof value === 'object') return Object.keys(value).length
  return String(value).length
}

/**
 * 销毁验证实例
 * @param {String} formId 表单ID
 */
export function destroyValidator(formId) {
  validationStates.delete(formId)
  validationErrors.delete(formId)
}

/**
 * 获取验证实例状态
 * @param {String} formId 表单ID
 * @returns {Object} 验证状态
 */
export function getValidatorState(formId) {
  return validationStates.get(formId)
}

// 导出内置规则和验证器
export { rules, validators }
