/**
 * 验证规则定义
 * 提供常用的验证规则和错误消息
 */

// 内置验证规则
const rules = {
  // 必填规则
  required: {
    required: true,
    message: '此字段为必填项',
    trigger: 'blur'
  },
  
  // 邮箱规则
  email: {
    type: 'email',
    message: '请输入正确的邮箱地址',
    trigger: 'blur'
  },
  
  // URL规则
  url: {
    type: 'url',
    message: '请输入正确的URL地址',
    trigger: 'blur'
  },
  
  // 数字规则
  number: {
    type: 'number',
    message: '请输入数字',
    trigger: 'blur'
  },
  
  // 整数规则
  integer: {
    type: 'integer',
    message: '请输入整数',
    trigger: 'blur'
  },
  
  // 浮点数规则
  float: {
    type: 'float',
    message: '请输入浮点数',
    trigger: 'blur'
  },
  
  // 数组规则
  array: {
    type: 'array',
    message: '请选择至少一项',
    trigger: 'change'
  },
  
  // 日期规则
  date: {
    type: 'date',
    message: '请选择日期',
    trigger: 'change'
  },
  
  // 布尔值规则
  boolean: {
    type: 'boolean',
    message: '请选择',
    trigger: 'change'
  },
  
  // 手机号规则
  mobile: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码',
    trigger: 'blur'
  },
  
  // 身份证号规则
  idCard: {
    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    message: '请输入正确的身份证号码',
    trigger: 'blur'
  },
  
  // 中文姓名规则
  chineseName: {
    pattern: /^[\u4e00-\u9fa5]{2,6}$/,
    message: '请输入正确的中文姓名（2-6个汉字）',
    trigger: 'blur'
  },
  
  // 密码规则（至少8位，包含字母和数字）
  password: {
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
    message: '密码至少8位，包含字母和数字',
    trigger: 'blur'
  },
  
  // 强密码规则（至少8位，包含大小写字母、数字和特殊字符）
  strongPassword: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    message: '密码至少8位，包含大小写字母、数字和特殊字符',
    trigger: 'blur'
  },
  
  // 用户名规则（字母开头，字母数字下划线，3-16位）
  username: {
    pattern: /^[a-zA-Z][a-zA-Z0-9_]{2,15}$/,
    message: '用户名以字母开头，3-16位字母数字下划线',
    trigger: 'blur'
  },
  
  // QQ号规则
  qq: {
    pattern: /^[1-9][0-9]{4,10}$/,
    message: '请输入正确的QQ号码',
    trigger: 'blur'
  },
  
  // 微信号规则
  wechat: {
    pattern: /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/,
    message: '请输入正确的微信号',
    trigger: 'blur'
  },
  
  // IP地址规则
  ip: {
    pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
    message: '请输入正确的IP地址',
    trigger: 'blur'
  },
  
  // 端口号规则
  port: {
    pattern: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
    message: '请输入正确的端口号（0-65535）',
    trigger: 'blur'
  },
  
  // 银行卡号规则
  bankCard: {
    pattern: /^[1-9]\d{12,18}$/,
    message: '请输入正确的银行卡号',
    trigger: 'blur'
  },
  
  // 邮政编码规则
  postcode: {
    pattern: /^[1-9]\d{5}$/,
    message: '请输入正确的邮政编码',
    trigger: 'blur'
  },
  
  // 车牌号规则
  licensePlate: {
    pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/,
    message: '请输入正确的车牌号',
    trigger: 'blur'
  }
}

// 长度验证规则生成器
export const lengthRules = {
  /**
   * 最小长度规则
   * @param {Number} min 最小长度
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  min: (min, message) => ({
    min,
    message: message || `长度不能少于${min}个字符`,
    trigger: 'blur'
  }),
  
  /**
   * 最大长度规则
   * @param {Number} max 最大长度
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  max: (max, message) => ({
    max,
    message: message || `长度不能超过${max}个字符`,
    trigger: 'blur'
  }),
  
  /**
   * 长度范围规则
   * @param {Number} min 最小长度
   * @param {Number} max 最大长度
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  range: (min, max, message) => ({
    min,
    max,
    message: message || `长度必须在${min}-${max}个字符之间`,
    trigger: 'blur'
  })
}

// 数值验证规则生成器
export const numberRules = {
  /**
   * 最小值规则
   * @param {Number} min 最小值
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  min: (min, message) => ({
    type: 'number',
    min,
    message: message || `数值不能小于${min}`,
    trigger: 'blur'
  }),
  
  /**
   * 最大值规则
   * @param {Number} max 最大值
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  max: (max, message) => ({
    type: 'number',
    max,
    message: message || `数值不能大于${max}`,
    trigger: 'blur'
  }),
  
  /**
   * 数值范围规则
   * @param {Number} min 最小值
   * @param {Number} max 最大值
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  range: (min, max, message) => ({
    type: 'number',
    min,
    max,
    message: message || `数值必须在${min}-${max}之间`,
    trigger: 'blur'
  }),
  
  /**
   * 正数规则
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  positive: (message) => ({
    validator: (value) => Number(value) > 0,
    message: message || '请输入正数',
    trigger: 'blur'
  }),
  
  /**
   * 非负数规则
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  nonNegative: (message) => ({
    validator: (value) => Number(value) >= 0,
    message: message || '请输入非负数',
    trigger: 'blur'
  })
}

// 自定义验证规则生成器
export const customRules = {
  /**
   * 确认密码规则
   * @param {String} passwordField 密码字段名
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  confirmPassword: (passwordField, message) => ({
    validator: (value, rule, callback, source) => {
      if (value !== source[passwordField]) {
        return message || '两次输入的密码不一致'
      }
      return true
    },
    trigger: 'blur'
  }),
  
  /**
   * 唯一性验证规则
   * @param {Function} checkUnique 唯一性检查函数
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  unique: (checkUnique, message) => ({
    validator: async (value) => {
      if (typeof checkUnique === 'function') {
        const isUnique = await checkUnique(value)
        if (!isUnique) {
          return message || '该值已存在，请输入其他值'
        }
      }
      return true
    },
    trigger: 'blur'
  }),
  
  /**
   * 自定义正则规则
   * @param {RegExp|String} pattern 正则表达式
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  pattern: (pattern, message) => ({
    pattern: pattern instanceof RegExp ? pattern : new RegExp(pattern),
    message: message || '格式不正确',
    trigger: 'blur'
  }),
  
  /**
   * 自定义验证函数规则
   * @param {Function} validator 验证函数
   * @param {String} message 自定义消息
   * @returns {Object} 验证规则
   */
  custom: (validator, message) => ({
    validator,
    message: message || '验证失败',
    trigger: 'blur'
  })
}

// 错误消息模板
export const messages = {
  required: '此字段为必填项',
  email: '请输入正确的邮箱地址',
  url: '请输入正确的URL地址',
  number: '请输入数字',
  integer: '请输入整数',
  float: '请输入浮点数',
  array: '请选择至少一项',
  date: '请选择日期',
  boolean: '请选择',
  pattern: '格式不正确',
  min: '长度不能少于 {min} 个字符',
  max: '长度不能超过 {max} 个字符',
  range: '长度必须在 {min}-{max} 个字符之间'
}

// 验证触发方式配置
export const triggers = {
  input: 'blur',
  textarea: 'blur',
  select: 'change',
  checkbox: 'change',
  radio: 'change',
  switch: 'change',
  date: 'change',
  time: 'change',
  upload: 'change',
  cascader: 'change',
  slider: 'change',
  rate: 'change',
  colorPicker: 'change'
}

// 添加消息到规则对象
rules.messages = messages
rules.triggers = triggers

export default rules
