/**
 * 验证器函数集合
 * 提供各种类型的数据验证函数
 */

/**
 * 邮箱验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function email(value) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(String(value))
}

/**
 * URL验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function url(value) {
  try {
    new URL(String(value))
    return true
  } catch {
    return false
  }
}

/**
 * 数字验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function number(value) {
  return !isNaN(Number(value)) && isFinite(Number(value))
}

/**
 * 整数验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function integer(value) {
  return Number.isInteger(Number(value))
}

/**
 * 浮点数验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function float(value) {
  const num = Number(value)
  return !isNaN(num) && isFinite(num) && !Number.isInteger(num)
}

/**
 * 数组验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function array(value) {
  return Array.isArray(value)
}

/**
 * 日期验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function date(value) {
  if (value instanceof Date) {
    return !isNaN(value.getTime())
  }
  const dateValue = new Date(value)
  return !isNaN(dateValue.getTime())
}

/**
 * 布尔值验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function boolean(value) {
  return typeof value === 'boolean'
}

/**
 * 字符串验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function string(value) {
  return typeof value === 'string'
}

/**
 * 对象验证器
 * @param {*} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function object(value) {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

/**
 * 手机号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function mobile(value) {
  const mobileRegex = /^1[3-9]\d{9}$/
  return mobileRegex.test(String(value))
}

/**
 * 身份证号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function idCard(value) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  const str = String(value)
  
  if (!idCardRegex.test(str)) {
    return false
  }
  
  // 18位身份证号码验证
  if (str.length === 18) {
    const coefficients = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(str[i]) * coefficients[i]
    }
    
    const checkCode = checkCodes[sum % 11]
    return str[17].toUpperCase() === checkCode
  }
  
  return true
}

/**
 * 中文姓名验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function chineseName(value) {
  const chineseNameRegex = /^[\u4e00-\u9fa5]{2,6}$/
  return chineseNameRegex.test(String(value))
}

/**
 * 密码强度验证器
 * @param {String} value 待验证的值
 * @param {Object} options 验证选项
 * @returns {Boolean} 验证结果
 */
export function password(value, options = {}) {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false
  } = options
  
  const str = String(value)
  
  if (str.length < minLength) {
    return false
  }
  
  if (requireUppercase && !/[A-Z]/.test(str)) {
    return false
  }
  
  if (requireLowercase && !/[a-z]/.test(str)) {
    return false
  }
  
  if (requireNumbers && !/\d/.test(str)) {
    return false
  }
  
  if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(str)) {
    return false
  }
  
  return true
}

/**
 * 用户名验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function username(value) {
  const usernameRegex = /^[a-zA-Z][a-zA-Z0-9_]{2,15}$/
  return usernameRegex.test(String(value))
}

/**
 * QQ号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function qq(value) {
  const qqRegex = /^[1-9][0-9]{4,10}$/
  return qqRegex.test(String(value))
}

/**
 * 微信号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function wechat(value) {
  const wechatRegex = /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/
  return wechatRegex.test(String(value))
}

/**
 * IP地址验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function ip(value) {
  const ipRegex = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/
  return ipRegex.test(String(value))
}

/**
 * 端口号验证器
 * @param {String|Number} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function port(value) {
  const portNum = Number(value)
  return Number.isInteger(portNum) && portNum >= 0 && portNum <= 65535
}

/**
 * 银行卡号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function bankCard(value) {
  const bankCardRegex = /^[1-9]\d{12,18}$/
  const str = String(value).replace(/\s/g, '')
  
  if (!bankCardRegex.test(str)) {
    return false
  }
  
  // Luhn算法验证
  let sum = 0
  let isEven = false
  
  for (let i = str.length - 1; i >= 0; i--) {
    let digit = parseInt(str[i])
    
    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    isEven = !isEven
  }
  
  return sum % 10 === 0
}

/**
 * 邮政编码验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function postcode(value) {
  const postcodeRegex = /^[1-9]\d{5}$/
  return postcodeRegex.test(String(value))
}

/**
 * 车牌号验证器
 * @param {String} value 待验证的值
 * @returns {Boolean} 验证结果
 */
export function licensePlate(value) {
  const licensePlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
  return licensePlateRegex.test(String(value))
}

/**
 * 长度验证器
 * @param {*} value 待验证的值
 * @param {Number} min 最小长度
 * @param {Number} max 最大长度
 * @returns {Boolean} 验证结果
 */
export function length(value, min, max) {
  const len = getLength(value)
  
  if (min !== undefined && len < min) {
    return false
  }
  
  if (max !== undefined && len > max) {
    return false
  }
  
  return true
}

/**
 * 范围验证器
 * @param {Number} value 待验证的值
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Boolean} 验证结果
 */
export function range(value, min, max) {
  const num = Number(value)
  
  if (isNaN(num)) {
    return false
  }
  
  if (min !== undefined && num < min) {
    return false
  }
  
  if (max !== undefined && num > max) {
    return false
  }
  
  return true
}

/**
 * 正则表达式验证器
 * @param {*} value 待验证的值
 * @param {RegExp|String} pattern 正则表达式
 * @returns {Boolean} 验证结果
 */
export function pattern(value, pattern) {
  const regex = pattern instanceof RegExp ? pattern : new RegExp(pattern)
  return regex.test(String(value))
}

/**
 * 获取值的长度
 * @param {*} value 值
 * @returns {Number} 长度
 */
function getLength(value) {
  if (value === null || value === undefined) {
    return 0
  }
  
  if (typeof value === 'string' || Array.isArray(value)) {
    return value.length
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length
  }
  
  return String(value).length
}

// 导出所有验证器
const validators = {
  email,
  url,
  number,
  integer,
  float,
  array,
  date,
  boolean,
  string,
  object,
  mobile,
  idCard,
  chineseName,
  password,
  username,
  qq,
  wechat,
  ip,
  port,
  bankCard,
  postcode,
  licensePlate,
  length,
  range,
  pattern
}

export default validators
