<!--
  Co-Table 表格组件 - 重构版本
  基于Composition API的现代化表格组件
  支持搜索、分页、操作、选择、验证等功能
-->
<template>
  <div 
    class="co-table-container"
    :style="containerStyle"
  >
    <!-- 搜索区域 -->
    <template v-if="searchModule.isEnabled.value">
      <co-table-search
        ref="searchRef"
        v-model="searchModule.searchFormData.value"
        :config="searchModule.searchConfig.value"
        :dictionary="dictionary.state"
        :loading="searchModule.isLoading.value"
        @search="searchModule.handleSearch"
        @reset="searchModule.reset"
        @change="searchModule.handleSearchChange"
      >
        <template #search_operation="slotProps">
          <slot name="search_operation" v-bind="slotProps" />
        </template>
      </co-table-search>
    </template>

    <!-- 表格容器 -->
    <div 
      v-loading="isLoading"
      class="co-table-wrapper"
    >
      <!-- 顶部操作区域 -->
      <div 
        v-if="operationsModule.topOperations.value.length > 0"
        class="co-table-operations"
      >
        <slot name="topOperation" :operations="operationsModule.topOperations.value">
          <co-table-button
            v-for="operation in operationsModule.topOperations.value"
            :key="operation.mark"
            :config="operation"
            @click="operationsModule.handleOperation(operation)"
          />
        </slot>
        <slot name="topOperationText" />
      </div>

      <!-- 表格主体 -->
      <co-table-body
        ref="tableRef"
        :data="tableData"
        :columns="tableColumns"
        :config="config"
        :selection="selectionModule.selectionConfig.value"
        :loading="isLoading"
        :has-form-items="hasFormItems"
        v-bind="$attrs"
        @selection-change="selectionModule.handleSelectionChange"
        @row-click="selectionModule.handleRowClick"
        @cell-click="handleCellClick"
        @operation="operationsModule.handleOperation"
      >
        <!-- 展开行插槽 -->
        <template v-if="$slots.expand" #expand="slotProps">
          <slot name="expand" v-bind="slotProps" />
        </template>

        <!-- 空数据插槽 -->
        <template v-if="$slots.empty" #empty>
          <slot name="empty" />
        </template>

        <!-- 动态列插槽 -->
        <template 
          v-for="column in tableColumns" 
          :key="column.prop"
          #[column.prop]="slotProps"
        >
          <slot 
            v-if="$slots[column.prop]"
            :name="column.prop" 
            v-bind="slotProps" 
          />
        </template>

        <!-- 动态列头插槽 -->
        <template 
          v-for="column in tableColumns" 
          :key="`${column.prop}_header`"
          #[`${column.prop}_header`]="slotProps"
        >
          <slot 
            v-if="$slots[`${column.prop}_header`]"
            :name="`${column.prop}_header`" 
            v-bind="slotProps" 
          />
        </template>

        <!-- 表单项插槽 -->
        <template 
          v-for="column in tableColumns" 
          :key="`${column.prop}_form-item`"
          #[`${column.prop}_form-item`]="slotProps"
        >
          <slot 
            v-if="$slots[`${column.prop}_form-item`]"
            :name="`${column.prop}_form-item`" 
            v-bind="slotProps" 
          />
        </template>
      </co-table-body>

      <!-- 分页区域 -->
      <co-table-pagination
        v-if="paginationModule.isEnabled.value"
        :config="paginationModule.paginationConfig.value"
        :loading="paginationModule.isLoading.value"
        @current-change="paginationModule.handleCurrentChange"
        @size-change="paginationModule.handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, provide, onMounted } from 'vue'
import { useTable } from '../../hooks/useTable/index.js'
import CoTableSearch from './components/table-search.vue'
import CoTableBody from './components/table-body.vue'
import CoTablePagination from './components/table-pagination.vue'
import CoTableButton from './components/table-button.vue'

// 组件名称
defineOptions({
  name: 'CoTable',
  inheritAttrs: false
})

// Props定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  header: {
    type: Array,
    default: () => []
  },
  // 表格配置
  config: {
    type: Object,
    default: () => ({})
  },
  // 搜索配置
  search: {
    type: [Boolean, Object],
    default: false
  },
  // 请求参数
  params: {
    type: Object,
    default: () => ({})
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 数据对齐方式
  align: {
    type: String,
    default: 'center',
    validator: (value) => ['left', 'center', 'right'].includes(value)
  },
  // 单选模式
  singleMode: {
    type: [Boolean, String],
    default: false
  },
  // 点击行选择
  currentRow: {
    type: Boolean,
    default: false
  },
  // 高亮颜色
  highlightColor: {
    type: String,
    default: ''
  },
  // 是否验证表单
  isValidate: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits([
  'loaded',
  'data',
  'dicLoaded',
  'operation',
  'selection-change',
  'search-change',
  'select',
  'page-change'
])

// 使用表格Hook
const {
  // 状态
  state,
  tableId,
  isLoading,
  hasError,
  tableData,
  tableColumns,
  hasFormItems,
  
  // 引用
  tableRef,
  containerRef,
  
  // 配置
  config,
  
  // 子模块
  dataModule,
  searchModule,
  paginationModule,
  operationsModule,
  selectionModule,
  validationModule,
  dictionary,
  permission,
  
  // 方法
  init,
  loadData,
  setData,
  setRow,
  setHeader,
  setPage,
  setParams,
  validate,
  clearValidation,
  refresh,
  reset,
  getSelection,
  setSelection,
  clearSelection
} = useTable(props, { emit, slots: $slots, attrs: $attrs, expose })

// 计算属性
const containerStyle = computed(() => ({
  '--highlight-color': props.highlightColor || '#409EFF',
  '--align': props.align
}))

// 事件处理
const handleCellClick = (row, column, cell, event) => {
  emit('cell-click', { row, column, cell, event })
}

// 提供给子组件的数据
provide('tableContext', {
  tableId,
  config,
  dictionary,
  permission
})

// 暴露方法给父组件
defineExpose({
  // 表格引用
  tableRef,
  containerRef,
  
  // 数据方法
  loadData,
  setData,
  setRow,
  setHeader,
  setPage,
  setParams,
  
  // 验证方法
  validate,
  clearValidation,
  
  // 操作方法
  refresh,
  reset,
  
  // 选择方法
  getSelection,
  setSelection,
  clearSelection,
  
  // 状态
  isLoading,
  hasError,
  tableData,
  tableColumns
})

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化已在useTable中处理
})
</script>

<style lang="scss" scoped>
.co-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  
  .co-table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .co-table-operations {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// 高亮行样式
:deep(.el-table__row.row-highlight) {
  background-color: var(--highlight-color, #409EFF) !important;
  opacity: 0.1;
}

// 当前行样式
:deep(.el-table__row.current-row) {
  background-color: var(--highlight-color, #409EFF) !important;
  opacity: 0.05;
}

// 对齐样式
:deep(.el-table .cell) {
  text-align: var(--align, center);
}
</style>
