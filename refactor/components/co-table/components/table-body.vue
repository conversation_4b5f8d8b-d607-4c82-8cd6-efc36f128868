<!--
  表格主体组件
  负责渲染表格的主要内容，包括列、数据和表单项
-->
<template>
  <el-table
    ref="tableRef"
    :data="data"
    :loading="loading"
    v-bind="tableProps"
    @selection-change="$emit('selection-change', $event)"
    @row-click="$emit('row-click', $event)"
    @cell-click="$emit('cell-click', $event)"
  >
    <!-- 选择列 -->
    <el-table-column
      v-if="selection && selection.type === 'checkbox'"
      type="selection"
      width="55"
      :reserve-selection="selection.reserveSelection"
      :selectable="selection.selectable"
    />
    
    <!-- 单选列 -->
    <el-table-column
      v-else-if="selection && selection.type === 'radio'"
      width="55"
      align="center"
    >
      <template #default="{ row, $index }">
        <el-radio
          :model-value="isRowSelected(row)"
          @change="handleRadioChange(row, $index)"
        >
          <span></span>
        </el-radio>
      </template>
    </el-table-column>
    
    <!-- 展开列 -->
    <el-table-column
      v-if="$slots.expand"
      type="expand"
    >
      <template #default="props">
        <slot name="expand" v-bind="props" />
      </template>
    </el-table-column>
    
    <!-- 数据列 -->
    <el-table-column
      v-for="column in visibleColumns"
      :key="column.prop"
      v-bind="getColumnProps(column)"
    >
      <!-- 列头插槽 -->
      <template v-if="$slots[`${column.prop}_header`]" #header="headerProps">
        <slot :name="`${column.prop}_header`" v-bind="headerProps" />
      </template>
      
      <!-- 列内容插槽 -->
      <template #default="cellProps">
        <!-- 自定义插槽 -->
        <slot
          v-if="$slots[column.prop]"
          :name="column.prop"
          v-bind="cellProps"
        />
        
        <!-- 表单项插槽 -->
        <slot
          v-else-if="hasFormItems && isFormColumn(column) && $slots[`${column.prop}_form-item`]"
          :name="`${column.prop}_form-item`"
          v-bind="cellProps"
        />
        
        <!-- 默认表单项渲染 -->
        <co-table-form-item
          v-else-if="hasFormItems && isFormColumn(column)"
          :column="column"
          :row="cellProps.row"
          :index="cellProps.$index"
          @change="handleFormItemChange"
        />
        
        <!-- 默认内容渲染 -->
        <span v-else>
          {{ formatCellValue(cellProps.row, column) }}
        </span>
      </template>
    </el-table-column>
    
    <!-- 空数据插槽 -->
    <template v-if="$slots.empty" #empty>
      <slot name="empty" />
    </template>
  </el-table>
</template>

<script setup>
import { computed, inject } from 'vue'
import CoTableFormItem from './table-form-item.vue'

// 组件名称
defineOptions({
  name: 'CoTableBody'
})

// Props定义
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  config: {
    type: Object,
    default: () => ({})
  },
  selection: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasFormItems: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits([
  'selection-change',
  'row-click', 
  'cell-click',
  'operation',
  'form-item-change'
])

// 注入表格上下文
const tableContext = inject('tableContext', {})

// 表格引用
const tableRef = ref(null)

// 计算属性
const visibleColumns = computed(() => {
  return props.columns.filter(col => !col.hidden && col.type !== 'selection')
})

const tableProps = computed(() => {
  const baseProps = {
    border: props.config.table?.border !== false,
    stripe: props.config.table?.stripe === true,
    size: props.config.table?.size || 'default',
    fit: props.config.table?.fit !== false,
    'show-header': props.config.table?.showHeader !== false,
    'highlight-current-row': props.config.table?.highlightCurrentRow === true,
    'row-key': props.config.table?.rowKey || '_uuid',
    'header-cell-style': props.config.table?.headerCellStyle,
    'row-style': props.config.table?.rowStyle,
    'row-class-name': props.config.table?.rowClassName,
    'cell-style': props.config.table?.cellStyle,
    'cell-class-name': props.config.table?.cellClassName
  }
  
  // 过滤undefined值
  return Object.fromEntries(
    Object.entries(baseProps).filter(([_, value]) => value !== undefined)
  )
})

// 方法
const getColumnProps = (column) => {
  return {
    prop: column.prop,
    label: column.label,
    width: column.width,
    'min-width': column.minWidth,
    fixed: column.fixed,
    align: column.align || 'center',
    'header-align': column.headerAlign || column.align || 'center',
    sortable: column.sortable,
    'sort-method': column.sortMethod,
    'sort-by': column.sortBy,
    'sort-orders': column.sortOrders,
    resizable: column.resizable,
    formatter: column.formatter,
    'show-overflow-tooltip': column.showOverflowTooltip,
    'class-name': column.className,
    'label-class-name': column.labelClassName
  }
}

const isFormColumn = (column) => {
  const formTypes = [
    'input', 'inputText', 'textarea', 'inputNumber', 'password',
    'select', 'checkbox', 'radio', 'switch', 'slider',
    'date', 'datetime', 'time', 'daterange', 'datetimerange',
    'cascader', 'treeSelect', 'colorPicker', 'rate'
  ]
  return formTypes.includes(column.type) || column.formItem
}

const formatCellValue = (row, column) => {
  const value = row[column.prop]
  
  // 如果有字典，使用字典转换
  if (column.dicKey && tableContext.dictionary) {
    return tableContext.dictionary.getDictionaryLabel(column.dicKey, value)
  }
  
  // 如果有格式化函数
  if (typeof column.formatter === 'function') {
    return column.formatter(row, column, value)
  }
  
  // 默认返回原值
  return value
}

const isRowSelected = (row) => {
  // 这里需要与选择Hook集成
  return false
}

const handleRadioChange = (row, index) => {
  emit('selection-change', [row])
}

const handleFormItemChange = (data) => {
  emit('form-item-change', data)
}

// 暴露方法
defineExpose({
  tableRef
})
</script>

<style lang="scss" scoped>
// 表格样式可以在这里定义
</style>
