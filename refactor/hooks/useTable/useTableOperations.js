/**
 * 表格操作功能Hook
 * 处理操作按钮、权限控制和事件分发
 */

import { ref, reactive, computed, watch } from 'vue'
import { deepClone } from '../../utils/common/index.js'

/**
 * 表格操作Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTableOperations(props, options = {}) {
  const {
    config,
    permission,
    tableId,
    onOperation
  } = options
  
  // 操作状态
  const state = reactive({
    operations: [], // 操作配置
    loading: new Set(), // 加载中的操作
    disabled: new Set(), // 禁用的操作
    error: null
  })
  
  // 计算属性
  const isEnabled = computed(() => {
    return config.operations?.enabled !== false
  })
  
  const topOperations = computed(() => {
    if (!isEnabled.value) return []
    
    const operations = getOperationsByPosition('top')
    return operations.filter(op => checkOperationPermission(op))
  })
  
  const rowOperations = computed(() => {
    if (!isEnabled.value) return []
    
    const operations = getOperationsByPosition('row')
    return operations.filter(op => checkOperationPermission(op))
  })
  
  const allOperations = computed(() => {
    return [...topOperations.value, ...rowOperations.value]
  })
  
  /**
   * 初始化操作
   */
  const init = () => {
    if (!isEnabled.value) return
    
    // 从配置中获取操作定义
    const operationConfig = config.operations || {}
    const headerOperations = props.header?.filter(col => col.type === 'operation') || []
    
    // 合并操作配置
    const operations = []
    
    // 添加顶部操作
    if (operationConfig.buttons) {
      Object.entries(operationConfig.buttons).forEach(([key, button]) => {
        operations.push({
          ...button,
          mark: key,
          position: 'top',
          type: 'operation'
        })
      })
    }
    
    // 添加表头定义的操作
    headerOperations.forEach(col => {
      if (col.buttons && Array.isArray(col.buttons)) {
        col.buttons.forEach(button => {
          operations.push({
            ...button,
            mark: button.mark || button.name,
            position: 'row',
            type: 'operation',
            column: col.prop
          })
        })
      }
    })
    
    state.operations = operations
  }
  
  /**
   * 根据位置获取操作
   * @param {String} position 位置: top | row
   * @returns {Array} 操作列表
   */
  const getOperationsByPosition = (position) => {
    return state.operations.filter(op => op.position === position)
  }
  
  /**
   * 检查操作权限
   * @param {Object} operation 操作配置
   * @returns {Boolean} 是否有权限
   */
  const checkOperationPermission = (operation) => {
    if (!operation.permission && !operation.perms) return true
    
    const requiredPermission = operation.permission || operation.perms
    return permission.hasPermission(requiredPermission)
  }
  
  /**
   * 检查操作是否可用
   * @param {Object} operation 操作配置
   * @param {Object} row 行数据（行操作时）
   * @returns {Boolean} 是否可用
   */
  const isOperationAvailable = (operation, row = null) => {
    // 检查权限
    if (!checkOperationPermission(operation)) {
      return false
    }
    
    // 检查是否禁用
    if (state.disabled.has(operation.mark)) {
      return false
    }
    
    // 检查自定义可用性函数
    if (typeof operation.available === 'function') {
      return operation.available(row, operation)
    }
    
    // 检查条件显示
    if (operation.condition && typeof operation.condition === 'function') {
      return operation.condition(row, operation)
    }
    
    return true
  }
  
  /**
   * 检查操作是否加载中
   * @param {String} mark 操作标识
   * @returns {Boolean} 是否加载中
   */
  const isOperationLoading = (mark) => {
    return state.loading.has(mark)
  }
  
  /**
   * 设置操作加载状态
   * @param {String} mark 操作标识
   * @param {Boolean} loading 是否加载中
   */
  const setOperationLoading = (mark, loading) => {
    if (loading) {
      state.loading.add(mark)
    } else {
      state.loading.delete(mark)
    }
  }
  
  /**
   * 设置操作禁用状态
   * @param {String} mark 操作标识
   * @param {Boolean} disabled 是否禁用
   */
  const setOperationDisabled = (mark, disabled) => {
    if (disabled) {
      state.disabled.add(mark)
    } else {
      state.disabled.delete(mark)
    }
  }
  
  /**
   * 处理操作事件
   * @param {Object} operation 操作配置
   * @param {Object} row 行数据（行操作时）
   * @param {Number} index 行索引（行操作时）
   */
  const handleOperation = async (operation, row = null, index = null) => {
    try {
      // 检查操作是否可用
      if (!isOperationAvailable(operation, row)) {
        return
      }
      
      // 设置加载状态
      setOperationLoading(operation.mark, true)
      
      // 构建操作数据
      const operationData = {
        mark: operation.mark,
        operation,
        row,
        index,
        tableId
      }
      
      // 处理确认对话框
      if (operation.confirm) {
        const confirmText = typeof operation.confirm === 'string' 
          ? operation.confirm 
          : operation.confirmText || `确定要执行${operation.text || operation.label}操作吗？`
        
        // 这里需要根据实际的确认对话框实现
        const confirmed = await showConfirm(confirmText)
        if (!confirmed) {
          return
        }
      }
      
      // 执行操作前的钩子
      if (typeof operation.beforeExecute === 'function') {
        const result = await operation.beforeExecute(operationData)
        if (result === false) {
          return // 取消执行
        }
      }
      
      // 触发操作回调
      if (typeof onOperation === 'function') {
        await onOperation(operationData)
      }
      
      // 执行操作后的钩子
      if (typeof operation.afterExecute === 'function') {
        await operation.afterExecute(operationData)
      }
      
    } catch (error) {
      state.error = error
      console.error('操作执行失败:', error)
      
      // 执行错误处理钩子
      if (typeof operation.onError === 'function') {
        operation.onError(error, { operation, row, index })
      }
      
      throw error
    } finally {
      // 清除加载状态
      setOperationLoading(operation.mark, false)
    }
  }
  
  /**
   * 批量操作处理
   * @param {String} mark 操作标识
   * @param {Array} rows 选中的行数据
   */
  const handleBatchOperation = async (mark, rows) => {
    const operation = state.operations.find(op => op.mark === mark)
    if (!operation) {
      throw new Error(`未找到操作: ${mark}`)
    }
    
    if (!rows || rows.length === 0) {
      throw new Error('请选择要操作的数据')
    }
    
    try {
      setOperationLoading(mark, true)
      
      // 批量确认
      if (operation.confirm) {
        const confirmText = typeof operation.confirm === 'string' 
          ? operation.confirm 
          : `确定要对选中的 ${rows.length} 条数据执行${operation.text || operation.label}操作吗？`
        
        const confirmed = await showConfirm(confirmText)
        if (!confirmed) {
          return
        }
      }
      
      // 构建批量操作数据
      const operationData = {
        mark,
        operation,
        rows,
        count: rows.length,
        tableId
      }
      
      // 执行批量操作
      if (typeof onOperation === 'function') {
        await onOperation(operationData)
      }
      
    } catch (error) {
      state.error = error
      console.error('批量操作执行失败:', error)
      throw error
    } finally {
      setOperationLoading(mark, false)
    }
  }
  
  /**
   * 获取操作配置
   * @param {String} mark 操作标识
   * @returns {Object} 操作配置
   */
  const getOperation = (mark) => {
    return state.operations.find(op => op.mark === mark)
  }
  
  /**
   * 添加操作
   * @param {Object} operation 操作配置
   */
  const addOperation = (operation) => {
    if (!operation.mark) {
      throw new Error('操作必须有唯一标识(mark)')
    }
    
    // 检查是否已存在
    const existingIndex = state.operations.findIndex(op => op.mark === operation.mark)
    if (existingIndex >= 0) {
      // 更新现有操作
      state.operations[existingIndex] = { ...state.operations[existingIndex], ...operation }
    } else {
      // 添加新操作
      state.operations.push(operation)
    }
  }
  
  /**
   * 移除操作
   * @param {String} mark 操作标识
   */
  const removeOperation = (mark) => {
    const index = state.operations.findIndex(op => op.mark === mark)
    if (index >= 0) {
      state.operations.splice(index, 1)
    }
  }
  
  /**
   * 显示确认对话框
   * @param {String} message 确认消息
   * @returns {Promise<Boolean>} 是否确认
   */
  const showConfirm = async (message) => {
    // 这里需要根据实际的UI库实现确认对话框
    // 例如使用Element Plus的MessageBox
    try {
      if (typeof window !== 'undefined' && window.$confirm) {
        await window.$confirm(message, '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        return true
      } else {
        // 降级到原生确认对话框
        return window.confirm(message)
      }
    } catch {
      return false
    }
  }
  
  // 监听配置变化
  watch(() => config.operations, (newConfig) => {
    if (newConfig) {
      init()
    }
  }, { deep: true })
  
  watch(() => props.header, (newHeader) => {
    if (newHeader) {
      init()
    }
  }, { deep: true })
  
  // 初始化
  init()
  
  return {
    // 状态
    state: readonly(state),
    isEnabled,
    topOperations,
    rowOperations,
    allOperations,
    
    // 方法
    init,
    getOperationsByPosition,
    checkOperationPermission,
    isOperationAvailable,
    isOperationLoading,
    setOperationLoading,
    setOperationDisabled,
    handleOperation,
    handleBatchOperation,
    getOperation,
    addOperation,
    removeOperation
  }
}

export default useTableOperations
