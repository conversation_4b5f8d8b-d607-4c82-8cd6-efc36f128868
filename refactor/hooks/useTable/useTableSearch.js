/**
 * 表格搜索功能Hook
 * 处理搜索表单、搜索参数和搜索状态管理
 */

import { ref, reactive, computed, watch } from 'vue'
import { deepClone, isEmpty } from '../../utils/common/index.js'

/**
 * 表格搜索Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTableSearch(props, options = {}) {
  const {
    config,
    dictionary,
    onSearch,
    emit
  } = options
  
  // 搜索状态
  const state = reactive({
    searchData: {}, // 搜索表单数据
    searchConfig: null, // 搜索配置
    loading: false,
    error: null,
    history: [] // 搜索历史
  })
  
  // 搜索引用
  const searchRef = ref(null)
  
  // 计算属性
  const isEnabled = computed(() => {
    return !!(props.search && (typeof props.search === 'object' || props.search === true))
  })
  
  const isLoading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  
  const searchFormData = computed({
    get: () => state.searchData,
    set: (value) => {
      state.searchData = { ...value }
    }
  })
  
  const processedSearchConfig = computed(() => {
    if (!isEnabled.value) return null
    
    const baseConfig = config.search || {}
    const userConfig = typeof props.search === 'object' ? props.search : {}
    
    return {
      ...baseConfig,
      ...userConfig,
      // 确保有默认的按钮配置
      buttons: {
        search: { 
          type: 'primary', 
          icon: 'Search', 
          text: '搜索',
          ...baseConfig.buttons?.search,
          ...userConfig.buttons?.search
        },
        reset: { 
          type: 'default', 
          icon: 'Refresh', 
          text: '重置',
          ...baseConfig.buttons?.reset,
          ...userConfig.buttons?.reset
        },
        ...userConfig.buttons
      }
    }
  })
  
  /**
   * 初始化搜索
   */
  const init = () => {
    if (!isEnabled.value) return
    
    // 设置初始搜索数据
    if (props.params && typeof props.params === 'object') {
      state.searchData = { ...props.params }
    }
    
    // 设置搜索配置
    state.searchConfig = processedSearchConfig.value
  }
  
  /**
   * 执行搜索
   * @param {Object} searchParams 搜索参数
   */
  const search = async (searchParams = null) => {
    try {
      state.loading = true
      state.error = null
      
      const params = searchParams || state.searchData
      
      // 过滤空值
      const filteredParams = filterEmptyValues(params)
      
      // 添加到搜索历史
      addToHistory(filteredParams)
      
      // 触发搜索回调
      if (typeof onSearch === 'function') {
        await onSearch(filteredParams)
      }
      
      // 发送搜索事件
      emit('search', filteredParams)
      
      return filteredParams
      
    } catch (error) {
      state.error = error
      console.error('搜索执行失败:', error)
      throw error
    } finally {
      state.loading = false
    }
  }
  
  /**
   * 重置搜索
   */
  const reset = () => {
    // 重置搜索数据
    state.searchData = {}
    
    // 清除错误状态
    state.error = null
    
    // 如果有搜索组件引用，调用其重置方法
    if (searchRef.value && typeof searchRef.value.reset === 'function') {
      searchRef.value.reset()
    }
    
    // 触发搜索（空参数）
    search({})
    
    // 发送重置事件
    emit('search-reset')
  }
  
  /**
   * 设置搜索数据
   * @param {Object} data 搜索数据
   */
  const setSearchData = (data) => {
    if (typeof data === 'object' && data !== null) {
      state.searchData = { ...data }
    }
  }
  
  /**
   * 获取搜索数据
   * @returns {Object} 搜索数据
   */
  const getSearchData = () => {
    return deepClone(state.searchData)
  }
  
  /**
   * 更新搜索字段
   * @param {String} field 字段名
   * @param {*} value 字段值
   */
  const updateSearchField = (field, value) => {
    state.searchData[field] = value
    
    // 发送字段变化事件
    emit('search-change', field, value)
  }
  
  /**
   * 清除搜索字段
   * @param {String} field 字段名
   */
  const clearSearchField = (field) => {
    delete state.searchData[field]
    
    // 发送字段变化事件
    emit('search-change', field, undefined)
  }
  
  /**
   * 获取搜索历史
   * @param {Number} limit 限制数量
   * @returns {Array} 搜索历史
   */
  const getSearchHistory = (limit = 10) => {
    return state.history.slice(-limit)
  }
  
  /**
   * 清除搜索历史
   */
  const clearSearchHistory = () => {
    state.history = []
  }
  
  /**
   * 添加到搜索历史
   * @param {Object} params 搜索参数
   */
  const addToHistory = (params) => {
    if (isEmpty(params)) return
    
    const historyItem = {
      params: deepClone(params),
      timestamp: Date.now(),
      id: Date.now().toString()
    }
    
    // 避免重复的搜索记录
    const exists = state.history.some(item => 
      JSON.stringify(item.params) === JSON.stringify(params)
    )
    
    if (!exists) {
      state.history.push(historyItem)
      
      // 限制历史记录数量
      if (state.history.length > 50) {
        state.history.shift()
      }
    }
  }
  
  /**
   * 从历史记录搜索
   * @param {String} historyId 历史记录ID
   */
  const searchFromHistory = (historyId) => {
    const historyItem = state.history.find(item => item.id === historyId)
    if (historyItem) {
      setSearchData(historyItem.params)
      search(historyItem.params)
    }
  }
  
  /**
   * 过滤空值
   * @param {Object} params 参数对象
   * @returns {Object} 过滤后的参数
   */
  const filterEmptyValues = (params) => {
    const filtered = {}
    
    for (const [key, value] of Object.entries(params)) {
      if (!isEmpty(value)) {
        // 特殊处理数组
        if (Array.isArray(value) && value.length > 0) {
          filtered[key] = value
        }
        // 特殊处理字符串
        else if (typeof value === 'string' && value.trim() !== '') {
          filtered[key] = value.trim()
        }
        // 其他非空值
        else if (value !== null && value !== undefined && value !== '') {
          filtered[key] = value
        }
      }
    }
    
    return filtered
  }
  
  /**
   * 验证搜索参数
   * @param {Object} params 搜索参数
   * @returns {Object} 验证结果
   */
  const validateSearchParams = (params) => {
    const errors = []
    
    // 这里可以添加搜索参数的验证逻辑
    // 例如：必填字段验证、格式验证等
    
    if (state.searchConfig?.validation) {
      // 执行自定义验证
      const customValidation = state.searchConfig.validation
      if (typeof customValidation === 'function') {
        const result = customValidation(params)
        if (result !== true) {
          errors.push(typeof result === 'string' ? result : '搜索参数验证失败')
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 搜索事件处理器
   * @param {Object} data 搜索数据
   */
  const handleSearch = (data) => {
    setSearchData(data)
    search(data)
  }
  
  /**
   * 搜索字段变化处理器
   * @param {String} field 字段名
   * @param {*} value 字段值
   */
  const handleSearchChange = (field, value) => {
    updateSearchField(field, value)
  }
  
  // 监听props变化
  watch(() => props.params, (newParams) => {
    if (newParams && typeof newParams === 'object') {
      setSearchData(newParams)
    }
  }, { deep: true })
  
  watch(() => props.search, (newSearch) => {
    if (newSearch) {
      state.searchConfig = processedSearchConfig.value
    }
  }, { deep: true })
  
  // 初始化
  init()
  
  return {
    // 状态
    state: readonly(state),
    isEnabled,
    isLoading,
    hasError,
    searchFormData,
    searchConfig: processedSearchConfig,
    
    // 引用
    searchRef,
    
    // 方法
    init,
    search,
    reset,
    setSearchData,
    getSearchData,
    updateSearchField,
    clearSearchField,
    getSearchHistory,
    clearSearchHistory,
    searchFromHistory,
    validateSearchParams,
    handleSearch,
    handleSearchChange
  }
}

export default useTableSearch
