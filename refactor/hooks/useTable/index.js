/**
 * 表格主Hook
 * 整合所有表格相关的功能模块
 */

import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useTableData } from './useTableData.js'
import { useTableSearch } from './useTableSearch.js'
import { useTablePagination } from './useTablePagination.js'
import { useTableOperations } from './useTableOperations.js'
import { useTableSelection } from './useTableSelection.js'
import { useTableValidation } from './useTableValidation.js'
import { useConfig } from '../shared/useConfig.js'
import { useDictionary } from '../shared/useDictionary.js'
import { usePermission } from '../shared/usePermission.js'
import { tableConfig } from '../../utils/config/index.js'
import { generateId } from '../../utils/common/index.js'

/**
 * 表格主Hook
 * @param {Object} props 组件props
 * @param {Object} context 组件context (emit, slots, attrs, expose)
 * @returns {Object} Hook返回值
 */
export function useTable(props, context) {
  const { emit, slots, attrs, expose } = context
  
  // 生成唯一的表格ID
  const tableId = ref(attrs.id || generateId('table'))
  
  // 配置管理
  const { config: mergedConfig, updateConfig } = useConfig(tableConfig, {
    reactive: true,
    onConfigChange: (newConfig) => {
      // 配置变化时的处理逻辑
      console.debug('表格配置已更新:', newConfig)
    }
  })
  
  // 合并用户配置
  if (props.config) {
    updateConfig(props.config)
  }
  
  // 字典管理
  const dictionary = useDictionary({
    cache: true,
    autoLoad: mergedConfig.dictionary?.enabled ? Object.keys(props.config?.dic || {}) : []
  })
  
  // 权限管理
  const permission = usePermission({
    permissionKey: mergedConfig.permission?.key || 'perms'
  })
  
  // 数据管理
  const dataModule = useTableData(props, {
    config: mergedConfig,
    dictionary,
    tableId: tableId.value,
    emit
  })
  
  // 搜索功能
  const searchModule = useTableSearch(props, {
    config: mergedConfig,
    dictionary,
    onSearch: (searchData) => {
      dataModule.loadData({ searchParams: searchData, resetPage: true })
    },
    emit
  })
  
  // 分页功能
  const paginationModule = useTablePagination(props, {
    config: mergedConfig,
    onPageChange: (pageData) => {
      dataModule.loadData({ pageParams: pageData })
    },
    emit
  })
  
  // 操作功能
  const operationsModule = useTableOperations(props, {
    config: mergedConfig,
    permission,
    tableId: tableId.value,
    onOperation: (operationData) => {
      emit('operation', operationData)
    }
  })
  
  // 选择功能
  const selectionModule = useTableSelection(props, {
    config: mergedConfig,
    tableId: tableId.value,
    onSelectionChange: (selection) => {
      emit('selection-change', selection)
    }
  })
  
  // 验证功能
  const validationModule = useTableValidation(props, {
    config: mergedConfig,
    tableId: tableId.value
  })
  
  // 表格状态
  const state = reactive({
    loading: false,
    error: null,
    initialized: false
  })
  
  // 表格引用
  const tableRef = ref(null)
  const containerRef = ref(null)
  
  // 计算属性
  const isLoading = computed(() => {
    return state.loading || dataModule.isLoading.value || dictionary.isLoading.value
  })
  
  const hasError = computed(() => {
    return !!(state.error || dataModule.error.value || dictionary.hasErrors.value)
  })
  
  const tableData = computed(() => {
    return dataModule.processedData.value
  })
  
  const tableColumns = computed(() => {
    return dataModule.processedColumns.value
  })
  
  const hasFormItems = computed(() => {
    return tableColumns.value.some(col => 
      ['input', 'select', 'switch', 'inputNumber', 'date', 'datetime'].includes(col.type)
    )
  })
  
  // 表格方法
  const methods = {
    /**
     * 初始化表格
     */
    async init() {
      try {
        state.loading = true
        state.error = null
        
        // 初始化字典
        if (mergedConfig.dictionary?.enabled && props.config?.dic) {
          await dictionary.loadBatchDictionaries(Object.keys(props.config.dic))
        }
        
        // 初始化数据
        await dataModule.init()
        
        // 初始化操作按钮
        operationsModule.init()
        
        state.initialized = true
        
        // 发送loaded事件
        emit('loaded', {
          tableRef: tableRef.value,
          getDataList: methods.loadData,
          setData: methods.setData,
          setRow: methods.setRow,
          setHeader: methods.setHeader,
          setPage: methods.setPage,
          setParams: methods.setParams
        })
        
      } catch (error) {
        state.error = error
        console.error('表格初始化失败:', error)
      } finally {
        state.loading = false
      }
    },
    
    /**
     * 加载数据
     */
    async loadData(options = {}) {
      return dataModule.loadData(options)
    },
    
    /**
     * 设置数据
     */
    setData(data, type = 'replace') {
      return dataModule.setData(data, type)
    },
    
    /**
     * 设置行数据
     */
    setRow(index, data) {
      return dataModule.setRow(index, data)
    },
    
    /**
     * 设置表头
     */
    setHeader(prop, data) {
      return dataModule.setHeader(prop, data)
    },
    
    /**
     * 设置分页
     */
    setPage(pageData) {
      return paginationModule.setPage(pageData)
    },
    
    /**
     * 设置参数
     */
    setParams(params) {
      return dataModule.setParams(params)
    },
    
    /**
     * 验证表格
     */
    async validate() {
      return validationModule.validate()
    },
    
    /**
     * 清除验证
     */
    clearValidation() {
      return validationModule.clearValidation()
    },
    
    /**
     * 刷新表格
     */
    async refresh() {
      return dataModule.refresh()
    },
    
    /**
     * 重置表格
     */
    reset() {
      searchModule.reset()
      paginationModule.reset()
      selectionModule.clearSelection()
      return methods.loadData({ resetPage: true })
    },
    
    /**
     * 获取选中数据
     */
    getSelection() {
      return selectionModule.getSelection()
    },
    
    /**
     * 设置选中数据
     */
    setSelection(selection) {
      return selectionModule.setSelection(selection)
    },
    
    /**
     * 清除选中
     */
    clearSelection() {
      return selectionModule.clearSelection()
    }
  }
  
  // 监听props变化
  watch(() => props.data, (newData) => {
    if (newData && Array.isArray(newData)) {
      dataModule.setData(newData)
    }
  }, { immediate: true })
  
  watch(() => props.header, (newHeader) => {
    if (newHeader && Array.isArray(newHeader)) {
      dataModule.setColumns(newHeader)
    }
  }, { immediate: true })
  
  watch(() => props.config, (newConfig) => {
    if (newConfig) {
      updateConfig(newConfig)
    }
  }, { deep: true })
  
  // 生命周期
  onMounted(() => {
    methods.init()
  })
  
  onUnmounted(() => {
    // 清理资源
    dictionary.clearCache()
  })
  
  // 暴露给模板的方法
  if (expose) {
    expose({
      ...methods,
      tableRef,
      containerRef
    })
  }
  
  return {
    // 状态
    state: readonly(state),
    tableId,
    isLoading,
    hasError,
    tableData,
    tableColumns,
    hasFormItems,
    
    // 引用
    tableRef,
    containerRef,
    
    // 配置
    config: mergedConfig,
    
    // 子模块
    dataModule,
    searchModule,
    paginationModule,
    operationsModule,
    selectionModule,
    validationModule,
    dictionary,
    permission,
    
    // 方法
    ...methods
  }
}

export default useTable
