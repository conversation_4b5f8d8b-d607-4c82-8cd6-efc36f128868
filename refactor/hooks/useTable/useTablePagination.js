/**
 * 表格分页功能Hook
 * 处理分页逻辑、分页状态和分页事件
 */

import { ref, reactive, computed, watch } from 'vue'
import { deepClone } from '../../utils/common/index.js'

/**
 * 表格分页Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTablePagination(props, options = {}) {
  const {
    config,
    onPageChange,
    emit
  } = options
  
  // 分页状态
  const state = reactive({
    current: 1, // 当前页
    pageSize: 20, // 每页大小
    total: 0, // 总记录数
    pageSizes: [10, 20, 50, 100], // 每页大小选项
    layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
    background: true, // 是否显示背景
    align: 'right', // 对齐方式
    loading: false,
    error: null
  })
  
  // 计算属性
  const isEnabled = computed(() => {
    const paginationConfig = config.pagination
    return paginationConfig !== false
  })
  
  const isLoading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  
  const totalPages = computed(() => {
    return Math.ceil(state.total / state.pageSize)
  })
  
  const startIndex = computed(() => {
    return (state.current - 1) * state.pageSize + 1
  })
  
  const endIndex = computed(() => {
    return Math.min(state.current * state.pageSize, state.total)
  })
  
  const paginationInfo = computed(() => {
    return {
      current: state.current,
      pageSize: state.pageSize,
      total: state.total,
      totalPages: totalPages.value,
      startIndex: startIndex.value,
      endIndex: endIndex.value,
      hasNext: state.current < totalPages.value,
      hasPrev: state.current > 1
    }
  })
  
  const paginationConfig = computed(() => {
    const defaultConfig = {
      current: state.current,
      pageSize: state.pageSize,
      total: state.total,
      pageSizes: state.pageSizes,
      layout: state.layout,
      background: state.background,
      align: state.align
    }
    
    const userConfig = config.pagination || {}
    
    return {
      ...defaultConfig,
      ...userConfig
    }
  })
  
  /**
   * 初始化分页
   */
  const init = () => {
    if (!isEnabled.value) return
    
    const paginationSettings = config.pagination || {}
    
    // 合并分页配置
    Object.assign(state, {
      current: paginationSettings.current || 1,
      pageSize: paginationSettings.pageSize || paginationSettings['page-size'] || 20,
      total: paginationSettings.total || 0,
      pageSizes: paginationSettings.pageSizes || paginationSettings['page-sizes'] || [10, 20, 50, 100],
      layout: paginationSettings.layout || 'total, sizes, prev, pager, next, jumper',
      background: paginationSettings.background !== false,
      align: paginationSettings.align || 'right'
    })
  }
  
  /**
   * 设置分页数据
   * @param {Object} pageData 分页数据
   */
  const setPage = (pageData) => {
    if (typeof pageData === 'object' && pageData !== null) {
      Object.assign(state, pageData)
    }
    
    return { setPage }
  }
  
  /**
   * 设置当前页
   * @param {Number} current 当前页
   */
  const setCurrent = (current) => {
    if (current > 0 && current <= totalPages.value) {
      state.current = current
      handlePageChange('current', current)
    }
  }
  
  /**
   * 设置每页大小
   * @param {Number} pageSize 每页大小
   */
  const setPageSize = (pageSize) => {
    if (pageSize > 0) {
      // 当改变每页大小时，重置到第一页
      if (state.current > 1) {
        state.current = 1
      }
      
      state.pageSize = pageSize
      handlePageChange('page-size', pageSize)
    }
  }
  
  /**
   * 设置总记录数
   * @param {Number} total 总记录数
   */
  const setTotal = (total) => {
    if (total >= 0) {
      state.total = total
      
      // 如果当前页超出范围，调整到最后一页
      const maxPage = Math.ceil(total / state.pageSize)
      if (state.current > maxPage && maxPage > 0) {
        state.current = maxPage
      }
    }
  }
  
  /**
   * 跳转到指定页
   * @param {Number} page 页码
   */
  const goToPage = (page) => {
    setCurrent(page)
  }
  
  /**
   * 上一页
   */
  const prevPage = () => {
    if (state.current > 1) {
      setCurrent(state.current - 1)
    }
  }
  
  /**
   * 下一页
   */
  const nextPage = () => {
    if (state.current < totalPages.value) {
      setCurrent(state.current + 1)
    }
  }
  
  /**
   * 第一页
   */
  const firstPage = () => {
    setCurrent(1)
  }
  
  /**
   * 最后一页
   */
  const lastPage = () => {
    setCurrent(totalPages.value)
  }
  
  /**
   * 重置分页
   */
  const reset = () => {
    state.current = 1
    state.error = null
  }
  
  /**
   * 获取分页参数
   * @returns {Object} 分页参数
   */
  const getPageParams = () => {
    const requestConfig = config.pagination?.request || {
      current: 'current',
      size: 'size'
    }
    
    return {
      [requestConfig.current]: state.current,
      [requestConfig.size]: state.pageSize
    }
  }
  
  /**
   * 处理分页变化
   * @param {String} type 变化类型
   * @param {*} value 变化值
   */
  const handlePageChange = async (type, value) => {
    try {
      state.loading = true
      state.error = null
      
      const pageParams = getPageParams()
      
      // 触发分页变化回调
      if (typeof onPageChange === 'function') {
        await onPageChange(pageParams)
      }
      
      // 发送分页事件
      emit('page-change', { type, value, params: pageParams })
      
    } catch (error) {
      state.error = error
      console.error('分页处理失败:', error)
    } finally {
      state.loading = false
    }
  }
  
  /**
   * 当前页变化处理器
   * @param {Number} current 当前页
   */
  const handleCurrentChange = (current) => {
    setCurrent(current)
  }
  
  /**
   * 每页大小变化处理器
   * @param {Number} pageSize 每页大小
   */
  const handleSizeChange = (pageSize) => {
    setPageSize(pageSize)
  }
  
  /**
   * 获取分页统计信息
   * @returns {Object} 统计信息
   */
  const getPaginationStats = () => {
    return {
      current: state.current,
      pageSize: state.pageSize,
      total: state.total,
      totalPages: totalPages.value,
      startIndex: startIndex.value,
      endIndex: endIndex.value,
      hasNext: state.current < totalPages.value,
      hasPrev: state.current > 1,
      isEmpty: state.total === 0
    }
  }
  
  /**
   * 验证分页参数
   * @param {Object} params 分页参数
   * @returns {Object} 验证结果
   */
  const validatePaginationParams = (params) => {
    const errors = []
    
    if (params.current && (params.current < 1 || !Number.isInteger(params.current))) {
      errors.push('当前页必须是大于0的整数')
    }
    
    if (params.pageSize && (params.pageSize < 1 || !Number.isInteger(params.pageSize))) {
      errors.push('每页大小必须是大于0的整数')
    }
    
    if (params.total && (params.total < 0 || !Number.isInteger(params.total))) {
      errors.push('总记录数必须是非负整数')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  // 监听配置变化
  watch(() => config.pagination, (newConfig) => {
    if (newConfig && typeof newConfig === 'object') {
      init()
    }
  }, { deep: true })
  
  // 初始化
  init()
  
  return {
    // 状态
    state: readonly(state),
    isEnabled,
    isLoading,
    hasError,
    totalPages,
    startIndex,
    endIndex,
    paginationInfo,
    paginationConfig,
    
    // 方法
    init,
    setPage,
    setCurrent,
    setPageSize,
    setTotal,
    goToPage,
    prevPage,
    nextPage,
    firstPage,
    lastPage,
    reset,
    getPageParams,
    handleCurrentChange,
    handleSizeChange,
    getPaginationStats,
    validatePaginationParams
  }
}

export default useTablePagination
