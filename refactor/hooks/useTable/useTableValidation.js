/**
 * 表格验证功能Hook
 * 处理表格内表单验证、验证状态管理
 */

import { ref, reactive, computed, nextTick } from 'vue'
import { createValidator } from '../../utils/validation/index.js'
import { deepClone } from '../../utils/common/index.js'

/**
 * 表格验证Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTableValidation(props, options = {}) {
  const {
    config,
    tableId
  } = options
  
  // 验证状态
  const state = reactive({
    validators: new Map(), // 行验证器映射
    validationResults: new Map(), // 验证结果映射
    validatingRows: new Set(), // 正在验证的行
    hasFormItems: false, // 是否包含表单项
    error: null
  })
  
  // 计算属性
  const isEnabled = computed(() => {
    return props.isValidate && state.hasFormItems
  })
  
  const isValidating = computed(() => {
    return state.validatingRows.size > 0
  })
  
  const hasErrors = computed(() => {
    for (const result of state.validationResults.values()) {
      if (!result.valid) return true
    }
    return false
  })
  
  const errorCount = computed(() => {
    let count = 0
    for (const result of state.validationResults.values()) {
      if (!result.valid) {
        count += Object.keys(result.errors).length
      }
    }
    return count
  })
  
  const validationSummary = computed(() => {
    const summary = {
      total: state.validationResults.size,
      valid: 0,
      invalid: 0,
      errors: []
    }
    
    for (const [rowId, result] of state.validationResults.entries()) {
      if (result.valid) {
        summary.valid++
      } else {
        summary.invalid++
        Object.entries(result.errors).forEach(([field, fieldErrors]) => {
          fieldErrors.forEach(error => {
            summary.errors.push({
              rowId,
              field,
              message: error
            })
          })
        })
      }
    }
    
    return summary
  })
  
  /**
   * 初始化验证
   */
  const init = () => {
    // 检查是否有表单项
    checkFormItems()
    
    if (!isEnabled.value) return
    
    // 清理现有验证器
    clearValidators()
  }
  
  /**
   * 检查是否有表单项
   */
  const checkFormItems = () => {
    if (!props.header || !Array.isArray(props.header)) {
      state.hasFormItems = false
      return
    }
    
    const formItemTypes = [
      'input', 'inputText', 'textarea', 'inputNumber', 'password',
      'select', 'checkbox', 'radio', 'switch', 'slider',
      'date', 'datetime', 'time', 'daterange', 'datetimerange',
      'cascader', 'treeSelect', 'colorPicker', 'rate'
    ]
    
    state.hasFormItems = props.header.some(col => 
      formItemTypes.includes(col.type) || col.formItem
    )
  }
  
  /**
   * 创建行验证器
   * @param {String} rowId 行ID
   * @param {Object} row 行数据
   * @returns {Object} 验证器实例
   */
  const createRowValidator = (rowId, row) => {
    if (!isEnabled.value) return null
    
    // 构建验证规则
    const rules = buildValidationRules(row)
    
    // 创建验证器
    const validator = createValidator(`${tableId}_row_${rowId}`, {
      rules,
      validateOnChange: config.editing?.validateOnChange !== false,
      validateOnBlur: true,
      onValidationChange: (errors) => {
        updateValidationResult(rowId, errors)
      }
    })
    
    // 存储验证器
    state.validators.set(rowId, validator)
    
    return validator
  }
  
  /**
   * 构建验证规则
   * @param {Object} row 行数据
   * @returns {Object} 验证规则
   */
  const buildValidationRules = (row) => {
    const rules = {}
    
    if (!props.header || !Array.isArray(props.header)) {
      return rules
    }
    
    props.header.forEach(col => {
      if (col.formItem && col.prop) {
        // 从列配置中获取验证规则
        if (col.rules && Array.isArray(col.rules)) {
          rules[col.prop] = col.rules
        } else if (col.required) {
          rules[col.prop] = [{ required: true, message: `${col.label || col.prop}不能为空` }]
        }
        
        // 根据类型添加默认验证规则
        if (col.type === 'inputNumber') {
          rules[col.prop] = rules[col.prop] || []
          rules[col.prop].push({ type: 'number', message: `${col.label || col.prop}必须是数字` })
        } else if (col.type === 'email') {
          rules[col.prop] = rules[col.prop] || []
          rules[col.prop].push({ type: 'email', message: `${col.label || col.prop}格式不正确` })
        }
      }
    })
    
    return rules
  }
  
  /**
   * 更新验证结果
   * @param {String} rowId 行ID
   * @param {Object} errors 验证错误
   */
  const updateValidationResult = (rowId, errors) => {
    const result = {
      valid: Object.keys(errors).length === 0,
      errors,
      timestamp: Date.now()
    }
    
    state.validationResults.set(rowId, result)
  }
  
  /**
   * 验证指定行
   * @param {String} rowId 行ID
   * @param {Object} row 行数据
   * @returns {Promise<Object>} 验证结果
   */
  const validateRow = async (rowId, row) => {
    if (!isEnabled.value) {
      return { valid: true, errors: {} }
    }
    
    try {
      state.validatingRows.add(rowId)
      
      // 获取或创建验证器
      let validator = state.validators.get(rowId)
      if (!validator) {
        validator = createRowValidator(rowId, row)
      }
      
      // 执行验证
      const result = await validator.validateForm(row, buildValidationRules(row))
      
      // 更新验证结果
      updateValidationResult(rowId, result.errors)
      
      return result
      
    } catch (error) {
      state.error = error
      console.error(`行验证失败 [${rowId}]:`, error)
      throw error
    } finally {
      state.validatingRows.delete(rowId)
    }
  }
  
  /**
   * 验证指定字段
   * @param {String} rowId 行ID
   * @param {String} field 字段名
   * @param {*} value 字段值
   * @returns {Promise<Object>} 验证结果
   */
  const validateField = async (rowId, field, value) => {
    if (!isEnabled.value) {
      return { valid: true, errors: [] }
    }
    
    try {
      // 获取验证器
      const validator = state.validators.get(rowId)
      if (!validator) {
        return { valid: true, errors: [] }
      }
      
      // 构建字段规则
      const rules = buildValidationRules({})
      const fieldRules = rules[field]
      
      if (!fieldRules) {
        return { valid: true, errors: [] }
      }
      
      // 执行字段验证
      const result = await validator.validateField(field, value, fieldRules)
      
      return result
      
    } catch (error) {
      console.error(`字段验证失败 [${rowId}.${field}]:`, error)
      return { valid: false, errors: [error.message] }
    }
  }
  
  /**
   * 验证所有行
   * @param {Array} data 表格数据
   * @returns {Promise<Object>} 验证结果
   */
  const validate = async (data = []) => {
    if (!isEnabled.value) {
      return { valid: true, errors: {}, summary: { total: 0, valid: 0, invalid: 0 } }
    }
    
    try {
      const validationPromises = data.map(async (row, index) => {
        const rowId = row._uuid || row.id || `row_${index}`
        return validateRow(rowId, row)
      })
      
      const results = await Promise.all(validationPromises)
      
      // 汇总结果
      const summary = {
        total: results.length,
        valid: results.filter(r => r.valid).length,
        invalid: results.filter(r => !r.valid).length
      }
      
      const allErrors = {}
      results.forEach((result, index) => {
        if (!result.valid) {
          const rowId = data[index]._uuid || data[index].id || `row_${index}`
          allErrors[rowId] = result.errors
        }
      })
      
      return {
        valid: summary.invalid === 0,
        errors: allErrors,
        summary
      }
      
    } catch (error) {
      state.error = error
      console.error('表格验证失败:', error)
      throw error
    }
  }
  
  /**
   * 清除验证结果
   * @param {String} rowId 行ID，不传则清除所有
   */
  const clearValidation = (rowId = null) => {
    if (rowId) {
      // 清除指定行
      const validator = state.validators.get(rowId)
      if (validator) {
        validator.clearValidation()
      }
      state.validationResults.delete(rowId)
    } else {
      // 清除所有
      for (const validator of state.validators.values()) {
        validator.clearValidation()
      }
      state.validationResults.clear()
    }
    
    state.error = null
  }
  
  /**
   * 清除验证器
   */
  const clearValidators = () => {
    // 销毁所有验证器
    for (const [rowId, validator] of state.validators.entries()) {
      if (validator && typeof validator.destroy === 'function') {
        validator.destroy()
      }
    }
    
    state.validators.clear()
    state.validationResults.clear()
    state.validatingRows.clear()
  }
  
  /**
   * 获取行验证结果
   * @param {String} rowId 行ID
   * @returns {Object|null} 验证结果
   */
  const getRowValidationResult = (rowId) => {
    return state.validationResults.get(rowId) || null
  }
  
  /**
   * 获取字段验证错误
   * @param {String} rowId 行ID
   * @param {String} field 字段名
   * @returns {Array} 错误信息数组
   */
  const getFieldErrors = (rowId, field) => {
    const result = state.validationResults.get(rowId)
    if (!result || result.valid) return []
    
    return result.errors[field] || []
  }
  
  /**
   * 检查行是否有错误
   * @param {String} rowId 行ID
   * @returns {Boolean} 是否有错误
   */
  const hasRowError = (rowId) => {
    const result = state.validationResults.get(rowId)
    return result ? !result.valid : false
  }
  
  /**
   * 检查字段是否有错误
   * @param {String} rowId 行ID
   * @param {String} field 字段名
   * @returns {Boolean} 是否有错误
   */
  const hasFieldError = (rowId, field) => {
    const errors = getFieldErrors(rowId, field)
    return errors.length > 0
  }
  
  // 初始化
  init()
  
  return {
    // 状态
    state: readonly(state),
    isEnabled,
    isValidating,
    hasErrors,
    errorCount,
    validationSummary,
    
    // 方法
    init,
    validateRow,
    validateField,
    validate,
    clearValidation,
    clearValidators,
    getRowValidationResult,
    getFieldErrors,
    hasRowError,
    hasFieldError
  }
}

export default useTableValidation
