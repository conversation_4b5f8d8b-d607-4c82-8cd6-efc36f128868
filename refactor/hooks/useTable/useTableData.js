/**
 * 表格数据管理Hook
 * 处理数据加载、处理、缓存和状态管理
 */

import { ref, reactive, computed, watch, nextTick } from 'vue'
import { generateId, deepClone, isEmpty } from '../../utils/common/index.js'

/**
 * 表格数据管理Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTableData(props, options = {}) {
  const {
    config,
    dictionary,
    tableId,
    emit
  } = options
  
  // 数据状态
  const state = reactive({
    rawData: [], // 原始数据
    processedData: [], // 处理后的数据
    columns: [], // 表格列配置
    loading: false,
    error: null,
    total: 0,
    params: {}, // 请求参数
    searchParams: {}, // 搜索参数
    pageParams: {} // 分页参数
  })
  
  // 数据缓存
  const cache = reactive({
    requestCache: new Map(), // 请求缓存
    processedCache: new Map() // 处理后数据缓存
  })
  
  // 计算属性
  const isLoading = computed(() => state.loading)
  const hasData = computed(() => state.processedData.length > 0)
  const isEmpty = computed(() => !state.loading && state.processedData.length === 0)
  const error = computed(() => state.error)
  const total = computed(() => state.total)
  
  const processedData = computed(() => {
    return state.processedData.map(row => ({
      ...row,
      _uuid: row._uuid || generateId('row')
    }))
  })
  
  const processedColumns = computed(() => {
    return state.columns.filter(col => !col.hidden).map(col => ({
      ...col,
      // 处理字典列
      ...(col.dicKey && dictionary.getDictionaryData(col.dicKey) ? {
        formatter: (row) => dictionary.getDictionaryLabel(col.dicKey, row[col.prop])
      } : {})
    }))
  })
  
  /**
   * 初始化数据模块
   */
  const init = async () => {
    try {
      state.loading = true
      state.error = null
      
      // 设置初始列配置
      if (props.header && Array.isArray(props.header)) {
        setColumns(props.header)
      }
      
      // 设置初始数据
      if (props.data && Array.isArray(props.data)) {
        setData(props.data)
      } else if (config.request?.apiName) {
        // 如果配置了API，则加载远程数据
        await loadData()
      }
      
    } catch (error) {
      state.error = error
      throw error
    } finally {
      state.loading = false
    }
  }
  
  /**
   * 加载数据
   * @param {Object} options 加载选项
   */
  const loadData = async (options = {}) => {
    const {
      searchParams = state.searchParams,
      pageParams = state.pageParams,
      resetPage = false,
      useCache = true
    } = options
    
    // 如果没有配置API，直接返回
    if (!config.request?.apiName) {
      return Promise.resolve(state.processedData)
    }
    
    try {
      state.loading = true
      state.error = null
      
      // 合并请求参数
      const requestParams = {
        ...state.params,
        ...searchParams,
        ...pageParams
      }
      
      // 重置页码
      if (resetPage && requestParams[config.pagination?.request?.current || 'current']) {
        requestParams[config.pagination?.request?.current || 'current'] = 1
      }
      
      // 检查缓存
      const cacheKey = JSON.stringify(requestParams)
      if (useCache && cache.requestCache.has(cacheKey)) {
        const cachedData = cache.requestCache.get(cacheKey)
        setRawData(cachedData.records, cachedData.total)
        return state.processedData
      }
      
      // 发起请求
      const response = await makeRequest(requestParams)
      
      // 处理响应数据
      const { records, total } = processResponse(response)
      
      // 缓存数据
      if (useCache) {
        cache.requestCache.set(cacheKey, { records, total })
      }
      
      // 设置数据
      setRawData(records, total)
      
      // 更新参数状态
      state.searchParams = searchParams
      state.pageParams = pageParams
      
      return state.processedData
      
    } catch (error) {
      state.error = error
      console.error('数据加载失败:', error)
      throw error
    } finally {
      state.loading = false
    }
  }
  
  /**
   * 发起请求
   * @param {Object} params 请求参数
   * @returns {Promise} 请求Promise
   */
  const makeRequest = async (params) => {
    const { apiName, headers = {}, formatData } = config.request
    
    // 处理参数格式化
    let requestData = params
    if (typeof formatData === 'function') {
      requestData = formatData(params)
    }
    
    // 过滤空值参数
    if (config.request.safeNullParams) {
      requestData = filterNullParams(requestData)
    }
    
    // 这里需要根据实际的API调用方式来实现
    // 假设有全局的API调用方法
    if (typeof apiName === 'function') {
      return apiName(requestData, { headers })
    } else if (typeof apiName === 'string') {
      // 需要从全局配置中获取API调用方法
      const apiMethod = config.apiMethod || window.$api
      if (!apiMethod) {
        throw new Error('未配置API调用方法')
      }
      return apiMethod(apiName, requestData, { headers })
    }
    
    throw new Error('无效的API配置')
  }
  
  /**
   * 处理响应数据
   * @param {Object} response 响应数据
   * @returns {Object} 处理后的数据
   */
  const processResponse = (response) => {
    const responseConfig = config.pagination?.response || {
      records: 'list',
      total: 'total'
    }
    
    const records = response[responseConfig.records] || response.data || []
    const total = response[responseConfig.total] || records.length
    
    return { records, total }
  }
  
  /**
   * 设置原始数据
   * @param {Array} data 数据数组
   * @param {Number} total 总数
   */
  const setRawData = (data, total = null) => {
    state.rawData = Array.isArray(data) ? data : []
    state.total = total !== null ? total : state.rawData.length
    
    // 处理数据
    processData()
    
    // 发送数据事件
    emit('data', state.processedData)
  }
  
  /**
   * 处理数据
   */
  const processData = () => {
    let processed = deepClone(state.rawData)
    
    // 添加唯一标识
    processed = processed.map((row, index) => ({
      ...row,
      _uuid: row._uuid || generateId('row'),
      _index: index
    }))
    
    // 处理字典数据
    if (dictionary && state.columns.length > 0) {
      processed = processed.map(row => {
        const processedRow = { ...row }
        
        state.columns.forEach(col => {
          if (col.dicKey && row[col.prop] !== undefined) {
            const dicData = dictionary.getDictionaryData(col.dicKey)
            if (dicData) {
              processedRow[`${col.prop}_label`] = dictionary.getDictionaryLabel(
                col.dicKey, 
                row[col.prop]
              )
            }
          }
        })
        
        return processedRow
      })
    }
    
    state.processedData = processed
  }
  
  /**
   * 设置数据
   * @param {Array} data 数据数组
   * @param {String} type 设置类型: replace | push | unshift
   */
  const setData = (data, type = 'replace') => {
    if (!Array.isArray(data)) {
      throw new Error('数据必须是数组类型')
    }
    
    switch (type) {
      case 'push':
        state.rawData.push(...data)
        break
      case 'unshift':
        state.rawData.unshift(...data)
        break
      default:
        state.rawData = data
    }
    
    processData()
    emit('data', state.processedData)
    
    return { setData, setRow, setHeader, setPage, setParams }
  }
  
  /**
   * 设置行数据
   * @param {Number} index 行索引
   * @param {Object} data 行数据
   */
  const setRow = (index, data) => {
    if (index < 0 || index >= state.rawData.length) {
      throw new Error('行索引超出范围')
    }
    
    if (data === null) {
      // 删除行
      state.rawData.splice(index, 1)
    } else {
      // 更新行
      Object.assign(state.rawData[index], data)
    }
    
    processData()
    emit('data', state.processedData)
    
    return { setData, setRow, setHeader, setPage, setParams }
  }
  
  /**
   * 设置列配置
   * @param {Array} columns 列配置数组
   */
  const setColumns = (columns) => {
    state.columns = Array.isArray(columns) ? columns : []
    processData() // 重新处理数据以应用新的列配置
  }
  
  /**
   * 设置表头
   * @param {String} prop 列属性
   * @param {Object} data 列数据
   */
  const setHeader = (prop, data) => {
    const column = state.columns.find(col => col.prop === prop)
    if (column) {
      Object.assign(column, data)
      processData()
    }
    
    return { setData, setRow, setHeader, setPage, setParams }
  }
  
  /**
   * 设置分页参数
   * @param {Object} pageData 分页数据
   */
  const setPage = (pageData) => {
    state.pageParams = { ...state.pageParams, ...pageData }
    return { setData, setRow, setHeader, setPage, setParams }
  }
  
  /**
   * 设置请求参数
   * @param {Object} params 参数对象
   */
  const setParams = (params) => {
    state.params = { ...state.params, ...params }
    return { setData, setRow, setHeader, setPage, setParams }
  }
  
  /**
   * 刷新数据
   */
  const refresh = () => {
    return loadData({ useCache: false })
  }
  
  /**
   * 清除缓存
   */
  const clearCache = () => {
    cache.requestCache.clear()
    cache.processedCache.clear()
  }
  
  /**
   * 过滤空值参数
   * @param {Object} params 参数对象
   * @returns {Object} 过滤后的参数
   */
  const filterNullParams = (params) => {
    const filtered = {}
    for (const [key, value] of Object.entries(params)) {
      if (!isEmpty(value)) {
        filtered[key] = value
      }
    }
    return filtered
  }
  
  return {
    // 状态
    state: readonly(state),
    isLoading,
    hasData,
    isEmpty,
    error,
    total,
    processedData,
    processedColumns,
    
    // 方法
    init,
    loadData,
    setData,
    setRow,
    setColumns,
    setHeader,
    setPage,
    setParams,
    refresh,
    clearCache
  }
}

export default useTableData
