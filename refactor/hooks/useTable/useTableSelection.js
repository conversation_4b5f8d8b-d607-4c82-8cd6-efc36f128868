/**
 * 表格选择功能Hook
 * 处理单选、多选、选择状态管理
 */

import { ref, reactive, computed, watch } from 'vue'
import { deepClone, generateId } from '../../utils/common/index.js'

/**
 * 表格选择Hook
 * @param {Object} props 组件props
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useTableSelection(props, options = {}) {
  const {
    config,
    tableId,
    onSelectionChange
  } = options
  
  // 选择状态
  const state = reactive({
    selection: [], // 选中的数据
    currentRow: null, // 当前行（单选模式）
    selectionType: 'none', // 选择类型: none | checkbox | radio
    reserveSelection: false, // 是否保留选择状态
    selectOnIndeterminate: true, // 半选状态时是否可选
    error: null
  })
  
  // 计算属性
  const isEnabled = computed(() => {
    return state.selectionType !== 'none'
  })
  
  const isMultipleMode = computed(() => {
    return state.selectionType === 'checkbox'
  })
  
  const isSingleMode = computed(() => {
    return state.selectionType === 'radio' || props.singleMode
  })
  
  const hasSelection = computed(() => {
    return isMultipleMode.value ? state.selection.length > 0 : !!state.currentRow
  })
  
  const selectionCount = computed(() => {
    return isMultipleMode.value ? state.selection.length : (state.currentRow ? 1 : 0)
  })
  
  const selectionConfig = computed(() => {
    if (!isEnabled.value) return null
    
    return {
      type: state.selectionType,
      reserveSelection: state.reserveSelection,
      selectOnIndeterminate: state.selectOnIndeterminate,
      selectable: getSelectableFunction()
    }
  })
  
  /**
   * 初始化选择功能
   */
  const init = () => {
    // 确定选择类型
    determineSelectionType()
    
    // 设置选择配置
    const selectionSettings = config.selection || {}
    state.reserveSelection = selectionSettings.reserveSelection || false
    state.selectOnIndeterminate = selectionSettings.selectOnIndeterminate !== false
  }
  
  /**
   * 确定选择类型
   */
  const determineSelectionType = () => {
    // 检查是否有选择列配置
    const hasSelectionColumn = props.header?.some(col => col.type === 'selection')
    
    if (hasSelectionColumn) {
      const selectionColumn = props.header.find(col => col.type === 'selection')
      state.selectionType = selectionColumn.selectionType || 'checkbox'
    } else if (props.singleMode) {
      state.selectionType = typeof props.singleMode === 'string' ? props.singleMode : 'radio'
    } else if (config.selection?.enabled) {
      state.selectionType = config.selection.type || 'checkbox'
    } else {
      state.selectionType = 'none'
    }
  }
  
  /**
   * 获取可选择函数
   * @returns {Function|null} 可选择判断函数
   */
  const getSelectableFunction = () => {
    const selectionSettings = config.selection || {}
    
    if (typeof selectionSettings.selectable === 'function') {
      return selectionSettings.selectable
    }
    
    // 默认都可选择
    return null
  }
  
  /**
   * 处理选择变化
   * @param {Array} selection 选中的数据
   */
  const handleSelectionChange = (selection) => {
    if (!isEnabled.value) return
    
    try {
      if (isMultipleMode.value) {
        state.selection = Array.isArray(selection) ? [...selection] : []
      } else {
        // 单选模式，取第一个或最后一个
        state.currentRow = selection && selection.length > 0 ? selection[selection.length - 1] : null
        state.selection = state.currentRow ? [state.currentRow] : []
      }
      
      // 触发选择变化回调
      if (typeof onSelectionChange === 'function') {
        onSelectionChange(getSelectionData())
      }
      
    } catch (error) {
      state.error = error
      console.error('选择处理失败:', error)
    }
  }
  
  /**
   * 处理行点击
   * @param {Object} row 行数据
   * @param {Object} column 列配置
   * @param {Event} event 事件对象
   */
  const handleRowClick = (row, column, event) => {
    // 只有在启用currentRow或单选模式时才处理行点击
    if (!props.currentRow && !isSingleMode.value) return
    
    if (isSingleMode.value) {
      // 单选模式：切换选择状态
      if (state.currentRow === row) {
        // 取消选择
        state.currentRow = null
        state.selection = []
      } else {
        // 选择新行
        state.currentRow = row
        state.selection = [row]
      }
      
      // 触发选择变化
      if (typeof onSelectionChange === 'function') {
        onSelectionChange(getSelectionData())
      }
    } else if (props.currentRow) {
      // 仅设置当前行，不影响选择状态
      state.currentRow = row
    }
  }
  
  /**
   * 设置选择数据
   * @param {Array|Object} selection 选择数据
   */
  const setSelection = (selection) => {
    if (!isEnabled.value) return
    
    try {
      if (isMultipleMode.value) {
        // 多选模式
        if (Array.isArray(selection)) {
          state.selection = [...selection]
        } else if (selection) {
          state.selection = [selection]
        } else {
          state.selection = []
        }
      } else {
        // 单选模式
        if (Array.isArray(selection)) {
          state.currentRow = selection.length > 0 ? selection[0] : null
        } else {
          state.currentRow = selection || null
        }
        state.selection = state.currentRow ? [state.currentRow] : []
      }
      
      // 触发选择变化
      if (typeof onSelectionChange === 'function') {
        onSelectionChange(getSelectionData())
      }
      
    } catch (error) {
      state.error = error
      console.error('设置选择失败:', error)
    }
  }
  
  /**
   * 获取选择数据
   * @returns {Object} 选择数据
   */
  const getSelectionData = () => {
    return {
      selection: deepClone(state.selection),
      currentRow: deepClone(state.currentRow),
      count: selectionCount.value,
      type: state.selectionType,
      isEmpty: !hasSelection.value
    }
  }
  
  /**
   * 获取选中数据
   * @returns {Array} 选中的数据数组
   */
  const getSelection = () => {
    return deepClone(state.selection)
  }
  
  /**
   * 清除选择
   */
  const clearSelection = () => {
    state.selection = []
    state.currentRow = null
    
    // 触发选择变化
    if (typeof onSelectionChange === 'function') {
      onSelectionChange(getSelectionData())
    }
  }
  
  /**
   * 全选
   * @param {Array} data 表格数据
   */
  const selectAll = (data) => {
    if (!isMultipleMode.value || !Array.isArray(data)) return
    
    const selectableFunction = getSelectableFunction()
    let selectableData = data
    
    // 过滤可选择的数据
    if (typeof selectableFunction === 'function') {
      selectableData = data.filter((row, index) => selectableFunction(row, index))
    }
    
    state.selection = [...selectableData]
    
    // 触发选择变化
    if (typeof onSelectionChange === 'function') {
      onSelectionChange(getSelectionData())
    }
  }
  
  /**
   * 反选
   * @param {Array} data 表格数据
   */
  const toggleSelection = (data) => {
    if (!isMultipleMode.value || !Array.isArray(data)) return
    
    const selectableFunction = getSelectableFunction()
    const currentSelectionIds = state.selection.map(row => row._uuid || row.id)
    const newSelection = []
    
    data.forEach((row, index) => {
      const isSelectable = typeof selectableFunction === 'function' 
        ? selectableFunction(row, index) 
        : true
      
      if (isSelectable) {
        const rowId = row._uuid || row.id
        if (!currentSelectionIds.includes(rowId)) {
          newSelection.push(row)
        }
      }
    })
    
    state.selection = newSelection
    
    // 触发选择变化
    if (typeof onSelectionChange === 'function') {
      onSelectionChange(getSelectionData())
    }
  }
  
  /**
   * 切换行选择状态
   * @param {Object} row 行数据
   */
  const toggleRowSelection = (row) => {
    if (!isEnabled.value || !row) return
    
    if (isMultipleMode.value) {
      // 多选模式
      const rowId = row._uuid || row.id
      const existingIndex = state.selection.findIndex(item => 
        (item._uuid || item.id) === rowId
      )
      
      if (existingIndex >= 0) {
        // 取消选择
        state.selection.splice(existingIndex, 1)
      } else {
        // 添加选择
        state.selection.push(row)
      }
    } else {
      // 单选模式
      if (state.currentRow === row) {
        state.currentRow = null
        state.selection = []
      } else {
        state.currentRow = row
        state.selection = [row]
      }
    }
    
    // 触发选择变化
    if (typeof onSelectionChange === 'function') {
      onSelectionChange(getSelectionData())
    }
  }
  
  /**
   * 检查行是否被选中
   * @param {Object} row 行数据
   * @returns {Boolean} 是否被选中
   */
  const isRowSelected = (row) => {
    if (!isEnabled.value || !row) return false
    
    const rowId = row._uuid || row.id
    return state.selection.some(item => (item._uuid || item.id) === rowId)
  }
  
  /**
   * 检查是否全选
   * @param {Array} data 表格数据
   * @returns {Boolean} 是否全选
   */
  const isAllSelected = (data) => {
    if (!isMultipleMode.value || !Array.isArray(data) || data.length === 0) {
      return false
    }
    
    const selectableFunction = getSelectableFunction()
    const selectableData = typeof selectableFunction === 'function'
      ? data.filter((row, index) => selectableFunction(row, index))
      : data
    
    if (selectableData.length === 0) return false
    
    return selectableData.every(row => isRowSelected(row))
  }
  
  /**
   * 检查是否半选
   * @param {Array} data 表格数据
   * @returns {Boolean} 是否半选
   */
  const isIndeterminate = (data) => {
    if (!isMultipleMode.value || !Array.isArray(data)) return false
    
    const selectedCount = state.selection.length
    return selectedCount > 0 && !isAllSelected(data)
  }
  
  // 监听配置变化
  watch(() => config.selection, (newConfig) => {
    if (newConfig) {
      init()
    }
  }, { deep: true })
  
  watch(() => props.singleMode, (newSingleMode) => {
    determineSelectionType()
  })
  
  watch(() => props.header, (newHeader) => {
    if (newHeader) {
      determineSelectionType()
    }
  }, { deep: true })
  
  // 初始化
  init()
  
  return {
    // 状态
    state: readonly(state),
    isEnabled,
    isMultipleMode,
    isSingleMode,
    hasSelection,
    selectionCount,
    selectionConfig,
    
    // 方法
    init,
    handleSelectionChange,
    handleRowClick,
    setSelection,
    getSelection,
    getSelectionData,
    clearSelection,
    selectAll,
    toggleSelection,
    toggleRowSelection,
    isRowSelected,
    isAllSelected,
    isIndeterminate
  }
}

export default useTableSelection
