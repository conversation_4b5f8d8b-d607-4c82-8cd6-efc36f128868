/**
 * 字典管理Hook
 * 提供字典数据的获取、缓存和管理功能
 */

import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import { getDictionary, getBatchDictionaries, clearDictionaryCache } from '../../utils/dictionary/index.js'

/**
 * 字典管理Hook
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function useDictionary(options = {}) {
  const {
    cache = true,
    format = true,
    keys = ['label', 'value'],
    autoLoad = false,
    onError = null
  } = options
  
  // 响应式状态
  const state = reactive({
    dictionaries: new Map(), // 字典数据存储
    loading: new Set(), // 加载状态
    errors: new Map() // 错误状态
  })
  
  // 计算属性
  const isLoading = computed(() => state.loading.size > 0)
  const hasErrors = computed(() => state.errors.size > 0)
  const loadingCount = computed(() => state.loading.size)
  const errorCount = computed(() => state.errors.size)
  
  /**
   * 加载单个字典
   * @param {String|Function} dicId 字典ID或获取函数
   * @param {Object} loadOptions 加载选项
   * @returns {Promise} 字典数据Promise
   */
  const loadDictionary = async (dicId, loadOptions = {}) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    
    // 如果已经在加载中，返回现有Promise
    if (state.loading.has(cacheKey)) {
      return state.dictionaries.get(cacheKey)
    }
    
    // 如果已有缓存数据且启用缓存，直接返回
    if (cache && state.dictionaries.has(cacheKey)) {
      return state.dictionaries.get(cacheKey)
    }
    
    // 开始加载
    state.loading.add(cacheKey)
    state.errors.delete(cacheKey)
    
    try {
      const mergedOptions = { cache, format, keys, ...loadOptions }
      const data = await getDictionary(dicId, mergedOptions)
      
      // 存储数据
      state.dictionaries.set(cacheKey, data)
      
      return data
    } catch (error) {
      // 记录错误
      state.errors.set(cacheKey, error)
      
      // 调用错误回调
      if (typeof onError === 'function') {
        onError(error, dicId)
      }
      
      throw error
    } finally {
      // 清除加载状态
      state.loading.delete(cacheKey)
    }
  }
  
  /**
   * 批量加载字典
   * @param {Array} dicIds 字典ID数组
   * @param {Object} loadOptions 加载选项
   * @returns {Promise} 字典数据Map
   */
  const loadBatchDictionaries = async (dicIds, loadOptions = {}) => {
    if (!Array.isArray(dicIds) || dicIds.length === 0) {
      return new Map()
    }
    
    // 标记所有字典为加载中
    dicIds.forEach(dicId => {
      const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
      state.loading.add(cacheKey)
      state.errors.delete(cacheKey)
    })
    
    try {
      const mergedOptions = { cache, format, keys, ...loadOptions }
      const results = await getBatchDictionaries(dicIds, mergedOptions)
      
      // 存储结果
      for (const [dicId, data] of results) {
        const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
        if (data !== null) {
          state.dictionaries.set(cacheKey, data)
        } else {
          state.errors.set(cacheKey, new Error('字典加载失败'))
        }
      }
      
      return results
    } catch (error) {
      // 记录所有字典的错误
      dicIds.forEach(dicId => {
        const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
        state.errors.set(cacheKey, error)
      })
      
      if (typeof onError === 'function') {
        onError(error, dicIds)
      }
      
      throw error
    } finally {
      // 清除所有加载状态
      dicIds.forEach(dicId => {
        const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
        state.loading.delete(cacheKey)
      })
    }
  }
  
  /**
   * 获取字典数据
   * @param {String} dicId 字典ID
   * @returns {Object|null} 字典数据
   */
  const getDictionaryData = (dicId) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    return state.dictionaries.get(cacheKey) || null
  }
  
  /**
   * 获取字典标签
   * @param {String} dicId 字典ID
   * @param {*} value 字典值
   * @param {Object} labelOptions 标签选项
   * @returns {String} 字典标签
   */
  const getDictionaryLabel = (dicId, value, labelOptions = {}) => {
    const { emptyText = '-', separator = ',' } = labelOptions
    const dictionary = getDictionaryData(dicId)
    
    if (!dictionary) return emptyText
    
    if (value === null || value === undefined || value === '') {
      return emptyText
    }
    
    // 处理多选情况
    if (typeof value === 'string' && value.includes(separator)) {
      const values = value.split(separator).filter(v => v.trim())
      const labels = values.map(v => dictionary[v.trim()] || v.trim())
      return labels.join(separator + ' ')
    }
    
    return dictionary[value] || value
  }
  
  /**
   * 检查字典是否正在加载
   * @param {String} dicId 字典ID
   * @returns {Boolean} 是否正在加载
   */
  const isDictionaryLoading = (dicId) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    return state.loading.has(cacheKey)
  }
  
  /**
   * 检查字典是否有错误
   * @param {String} dicId 字典ID
   * @returns {Boolean} 是否有错误
   */
  const hasDictionaryError = (dicId) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    return state.errors.has(cacheKey)
  }
  
  /**
   * 获取字典错误信息
   * @param {String} dicId 字典ID
   * @returns {Error|null} 错误信息
   */
  const getDictionaryError = (dicId) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    return state.errors.get(cacheKey) || null
  }
  
  /**
   * 刷新字典数据
   * @param {String} dicId 字典ID
   * @param {Object} loadOptions 加载选项
   * @returns {Promise} 字典数据Promise
   */
  const refreshDictionary = async (dicId, loadOptions = {}) => {
    const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
    
    // 清除缓存
    state.dictionaries.delete(cacheKey)
    state.errors.delete(cacheKey)
    
    // 重新加载
    return loadDictionary(dicId, loadOptions)
  }
  
  /**
   * 清除字典缓存
   * @param {String} dicId 字典ID，不传则清除所有
   */
  const clearCache = (dicId) => {
    if (dicId) {
      const cacheKey = typeof dicId === 'string' ? dicId : dicId.toString()
      state.dictionaries.delete(cacheKey)
      state.errors.delete(cacheKey)
      state.loading.delete(cacheKey)
    } else {
      state.dictionaries.clear()
      state.errors.clear()
      state.loading.clear()
    }
    
    // 同时清除全局缓存
    clearDictionaryCache(dicId)
  }
  
  /**
   * 预加载字典
   * @param {Array} dicIds 字典ID数组
   * @param {Object} loadOptions 加载选项
   */
  const preloadDictionaries = async (dicIds, loadOptions = {}) => {
    if (!Array.isArray(dicIds)) return
    
    const promises = dicIds.map(dicId => 
      loadDictionary(dicId, loadOptions).catch(error => {
        console.warn(`预加载字典失败 [${dicId}]:`, error)
        return null
      })
    )
    
    await Promise.all(promises)
  }
  
  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  const getCacheStats = () => {
    return {
      dictionaryCount: state.dictionaries.size,
      loadingCount: state.loading.size,
      errorCount: state.errors.size,
      dictionaryKeys: Array.from(state.dictionaries.keys()),
      loadingKeys: Array.from(state.loading),
      errorKeys: Array.from(state.errors.keys())
    }
  }
  
  // 自动加载字典（如果配置了autoLoad）
  if (autoLoad && Array.isArray(autoLoad)) {
    preloadDictionaries(autoLoad)
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    // 可选择是否在组件卸载时清理缓存
    // clearCache()
  })
  
  return {
    // 状态
    state: readonly(state),
    isLoading,
    hasErrors,
    loadingCount,
    errorCount,
    
    // 方法
    loadDictionary,
    loadBatchDictionaries,
    getDictionaryData,
    getDictionaryLabel,
    isDictionaryLoading,
    hasDictionaryError,
    getDictionaryError,
    refreshDictionary,
    clearCache,
    preloadDictionaries,
    getCacheStats
  }
}

export default useDictionary
