/**
 * 配置管理Hook
 * 提供组件配置的合并、管理和响应式更新功能
 */

import { ref, reactive, computed, watch, toRaw } from 'vue'
import { mergeConfig, deepClone, getGlobalConfig } from '../../utils/config/index.js'
import { isObject, isFunction } from '../../utils/common/types.js'

/**
 * 配置管理Hook
 * @param {Object} defaultConfig 默认配置
 * @param {Object} options 选项配置
 * @returns {Object} Hook返回值
 */
export function useConfig(defaultConfig = {}, options = {}) {
  const {
    reactive: isReactive = true,
    deep = true,
    immediate = true,
    onConfigChange = null
  } = options
  
  // 响应式状态
  const state = reactive({
    config: {},
    originalConfig: {},
    isDirty: false,
    changeHistory: []
  })
  
  // 当前配置（响应式或非响应式）
  const currentConfig = isReactive ? reactive({}) : ref({})
  
  /**
   * 初始化配置
   * @param {Object} config 初始配置
   */
  const initConfig = (config = {}) => {
    const merged = mergeConfig(defaultConfig, config)
    
    if (isReactive) {
      Object.assign(currentConfig, merged)
    } else {
      currentConfig.value = merged
    }
    
    state.config = deepClone(merged)
    state.originalConfig = deepClone(merged)
    state.isDirty = false
    state.changeHistory = []
  }
  
  /**
   * 更新配置
   * @param {Object|Function} newConfig 新配置或配置更新函数
   * @param {Boolean} merge 是否合并配置
   */
  const updateConfig = (newConfig, merge = true) => {
    let updatedConfig
    
    if (isFunction(newConfig)) {
      // 如果是函数，传入当前配置并获取新配置
      const current = isReactive ? toRaw(currentConfig) : currentConfig.value
      updatedConfig = newConfig(deepClone(current))
    } else if (isObject(newConfig)) {
      updatedConfig = newConfig
    } else {
      console.warn('配置更新参数必须是对象或函数')
      return
    }
    
    const oldConfig = isReactive ? toRaw(currentConfig) : currentConfig.value
    let finalConfig
    
    if (merge) {
      finalConfig = mergeConfig(oldConfig, updatedConfig)
    } else {
      finalConfig = mergeConfig(defaultConfig, updatedConfig)
    }
    
    // 记录变更历史
    state.changeHistory.push({
      timestamp: Date.now(),
      oldConfig: deepClone(oldConfig),
      newConfig: deepClone(finalConfig),
      changes: getConfigChanges(oldConfig, finalConfig)
    })
    
    // 限制历史记录数量
    if (state.changeHistory.length > 50) {
      state.changeHistory.shift()
    }
    
    // 更新配置
    if (isReactive) {
      Object.assign(currentConfig, finalConfig)
    } else {
      currentConfig.value = finalConfig
    }
    
    state.config = deepClone(finalConfig)
    state.isDirty = !isConfigEqual(state.config, state.originalConfig)
    
    // 触发配置变化回调
    if (typeof onConfigChange === 'function') {
      onConfigChange(finalConfig, oldConfig)
    }
  }
  
  /**
   * 重置配置到默认值
   */
  const resetConfig = () => {
    initConfig({})
  }
  
  /**
   * 重置配置到原始值
   */
  const revertConfig = () => {
    const original = deepClone(state.originalConfig)
    
    if (isReactive) {
      Object.assign(currentConfig, original)
    } else {
      currentConfig.value = original
    }
    
    state.config = original
    state.isDirty = false
  }
  
  /**
   * 获取配置值
   * @param {String} path 配置路径，如 'table.pagination.pageSize'
   * @param {*} defaultValue 默认值
   * @returns {*} 配置值
   */
  const getConfigValue = (path, defaultValue = undefined) => {
    if (!path) return isReactive ? currentConfig : currentConfig.value
    
    const config = isReactive ? currentConfig : currentConfig.value
    const keys = path.split('.')
    let result = config
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return defaultValue
      }
      result = result[key]
    }
    
    return result !== undefined ? result : defaultValue
  }
  
  /**
   * 设置配置值
   * @param {String} path 配置路径
   * @param {*} value 配置值
   */
  const setConfigValue = (path, value) => {
    if (!path) return
    
    const config = isReactive ? currentConfig : currentConfig.value
    const keys = path.split('.')
    let current = config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!(key in current) || !isObject(current[key])) {
        current[key] = {}
      }
      current = current[key]
    }
    
    const lastKey = keys[keys.length - 1]
    const oldValue = current[lastKey]
    current[lastKey] = value
    
    // 更新状态
    state.config = deepClone(isReactive ? currentConfig : currentConfig.value)
    state.isDirty = !isConfigEqual(state.config, state.originalConfig)
    
    // 记录变更
    state.changeHistory.push({
      timestamp: Date.now(),
      path,
      oldValue: deepClone(oldValue),
      newValue: deepClone(value),
      type: 'setValue'
    })
    
    // 触发配置变化回调
    if (typeof onConfigChange === 'function') {
      const currentConfigValue = isReactive ? toRaw(currentConfig) : currentConfig.value
      onConfigChange(currentConfigValue, { [path]: oldValue })
    }
  }
  
  /**
   * 删除配置值
   * @param {String} path 配置路径
   */
  const deleteConfigValue = (path) => {
    if (!path) return
    
    const config = isReactive ? currentConfig : currentConfig.value
    const keys = path.split('.')
    let current = config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!(key in current) || !isObject(current[key])) {
        return // 路径不存在
      }
      current = current[key]
    }
    
    const lastKey = keys[keys.length - 1]
    const oldValue = current[lastKey]
    delete current[lastKey]
    
    // 更新状态
    state.config = deepClone(isReactive ? currentConfig : currentConfig.value)
    state.isDirty = !isConfigEqual(state.config, state.originalConfig)
    
    // 记录变更
    state.changeHistory.push({
      timestamp: Date.now(),
      path,
      oldValue: deepClone(oldValue),
      type: 'deleteValue'
    })
  }
  
  /**
   * 合并全局配置
   */
  const mergeGlobalConfig = () => {
    const globalConfig = getGlobalConfig()
    if (globalConfig && isObject(globalConfig)) {
      updateConfig(globalConfig, true)
    }
  }
  
  /**
   * 获取配置变更历史
   * @param {Number} limit 限制数量
   * @returns {Array} 变更历史
   */
  const getChangeHistory = (limit = 10) => {
    return state.changeHistory.slice(-limit)
  }
  
  /**
   * 清空变更历史
   */
  const clearChangeHistory = () => {
    state.changeHistory = []
  }
  
  /**
   * 导出配置
   * @param {Boolean} includeDefaults 是否包含默认值
   * @returns {Object} 配置对象
   */
  const exportConfig = (includeDefaults = false) => {
    const config = isReactive ? toRaw(currentConfig) : currentConfig.value
    
    if (includeDefaults) {
      return deepClone(config)
    } else {
      // 只导出与默认配置不同的部分
      return getConfigDifferences(config, defaultConfig)
    }
  }
  
  /**
   * 导入配置
   * @param {Object} config 配置对象
   * @param {Boolean} merge 是否合并
   */
  const importConfig = (config, merge = true) => {
    if (!isObject(config)) {
      console.warn('导入的配置必须是对象')
      return
    }
    
    updateConfig(config, merge)
  }
  
  /**
   * 验证配置
   * @param {Object} schema 验证模式
   * @returns {Object} 验证结果
   */
  const validateConfig = (schema) => {
    const config = isReactive ? toRaw(currentConfig) : currentConfig.value
    return validateConfigSchema(config, schema)
  }
  
  // 计算属性
  const isDirty = computed(() => state.isDirty)
  const hasChanges = computed(() => state.changeHistory.length > 0)
  const changeCount = computed(() => state.changeHistory.length)
  const configSize = computed(() => {
    const config = isReactive ? currentConfig : currentConfig.value
    return JSON.stringify(config).length
  })
  
  // 监听配置变化
  if (deep && immediate) {
    watch(
      () => isReactive ? currentConfig : currentConfig.value,
      (newConfig, oldConfig) => {
        if (typeof onConfigChange === 'function' && oldConfig) {
          onConfigChange(newConfig, oldConfig)
        }
      },
      { deep: true, immediate: false }
    )
  }
  
  // 初始化
  initConfig()
  
  return {
    // 状态
    config: isReactive ? currentConfig : computed(() => currentConfig.value),
    state: readonly(state),
    isDirty,
    hasChanges,
    changeCount,
    configSize,
    
    // 方法
    initConfig,
    updateConfig,
    resetConfig,
    revertConfig,
    getConfigValue,
    setConfigValue,
    deleteConfigValue,
    mergeGlobalConfig,
    getChangeHistory,
    clearChangeHistory,
    exportConfig,
    importConfig,
    validateConfig
  }
}

/**
 * 比较两个配置是否相等
 * @param {Object} config1 配置1
 * @param {Object} config2 配置2
 * @returns {Boolean} 是否相等
 */
function isConfigEqual(config1, config2) {
  return JSON.stringify(config1) === JSON.stringify(config2)
}

/**
 * 获取配置变更
 * @param {Object} oldConfig 旧配置
 * @param {Object} newConfig 新配置
 * @returns {Array} 变更列表
 */
function getConfigChanges(oldConfig, newConfig) {
  const changes = []
  const allKeys = new Set([...Object.keys(oldConfig), ...Object.keys(newConfig)])
  
  for (const key of allKeys) {
    const oldValue = oldConfig[key]
    const newValue = newConfig[key]
    
    if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
      changes.push({
        key,
        oldValue: deepClone(oldValue),
        newValue: deepClone(newValue),
        type: oldValue === undefined ? 'added' : newValue === undefined ? 'removed' : 'modified'
      })
    }
  }
  
  return changes
}

/**
 * 获取配置差异
 * @param {Object} config 当前配置
 * @param {Object} defaultConfig 默认配置
 * @returns {Object} 差异配置
 */
function getConfigDifferences(config, defaultConfig) {
  const differences = {}
  
  for (const key in config) {
    if (JSON.stringify(config[key]) !== JSON.stringify(defaultConfig[key])) {
      differences[key] = config[key]
    }
  }
  
  return differences
}

/**
 * 验证配置模式
 * @param {Object} config 配置
 * @param {Object} schema 模式
 * @returns {Object} 验证结果
 */
function validateConfigSchema(config, schema) {
  const errors = []
  
  for (const key in schema) {
    const rule = schema[key]
    const value = config[key]
    
    if (rule.required && (value === undefined || value === null)) {
      errors.push(`配置项 ${key} 是必需的`)
    }
    
    if (value !== undefined && rule.type && typeof value !== rule.type) {
      errors.push(`配置项 ${key} 类型错误，期望 ${rule.type}，实际 ${typeof value}`)
    }
    
    if (rule.validator && typeof rule.validator === 'function') {
      const result = rule.validator(value)
      if (result !== true) {
        errors.push(typeof result === 'string' ? result : `配置项 ${key} 验证失败`)
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

export default useConfig
