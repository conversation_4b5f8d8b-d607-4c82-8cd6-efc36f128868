/**
 * 权限管理Hook
 * 提供权限检查、权限控制和权限管理功能
 */

import { ref, reactive, computed, watch } from 'vue'
import { getGlobalConfig } from '../../utils/config/index.js'

/**
 * 权限管理Hook
 * @param {Object} options 配置选项
 * @returns {Object} Hook返回值
 */
export function usePermission(options = {}) {
  const {
    permissionKey = 'perms',
    userPermissions = null,
    autoCheck = true,
    onPermissionChange = null
  } = options
  
  // 响应式状态
  const state = reactive({
    permissions: new Set(), // 用户权限集合
    loading: false, // 权限加载状态
    error: null // 权限错误信息
  })
  
  // 计算属性
  const isLoading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  const permissionCount = computed(() => state.permissions.size)
  const permissionList = computed(() => Array.from(state.permissions))
  
  /**
   * 设置用户权限
   * @param {Array|Set|String} permissions 权限数据
   */
  const setPermissions = (permissions) => {
    state.permissions.clear()
    
    if (!permissions) return
    
    let permissionArray = []
    
    if (typeof permissions === 'string') {
      permissionArray = permissions.split(',').map(p => p.trim()).filter(p => p)
    } else if (Array.isArray(permissions)) {
      permissionArray = permissions
    } else if (permissions instanceof Set) {
      permissionArray = Array.from(permissions)
    } else if (typeof permissions === 'object') {
      // 如果是对象，尝试从指定字段获取权限
      const globalPermissionKey = getGlobalConfig('permissionKey') || permissionKey
      permissionArray = permissions[globalPermissionKey] || []
    }
    
    // 添加权限到集合
    permissionArray.forEach(permission => {
      if (permission && typeof permission === 'string') {
        state.permissions.add(permission.trim())
      }
    })
    
    // 触发权限变化回调
    if (typeof onPermissionChange === 'function') {
      onPermissionChange(Array.from(state.permissions))
    }
  }
  
  /**
   * 检查是否有指定权限
   * @param {String|Array} permission 权限代码或权限数组
   * @param {String} mode 检查模式：'some'（有任一权限）| 'every'（有所有权限）
   * @returns {Boolean} 是否有权限
   */
  const hasPermission = (permission, mode = 'some') => {
    if (!permission) return true
    
    // 如果权限集合为空，默认无权限
    if (state.permissions.size === 0) return false
    
    // 处理单个权限
    if (typeof permission === 'string') {
      return state.permissions.has(permission)
    }
    
    // 处理权限数组
    if (Array.isArray(permission)) {
      if (permission.length === 0) return true
      
      if (mode === 'every') {
        return permission.every(p => state.permissions.has(p))
      } else {
        return permission.some(p => state.permissions.has(p))
      }
    }
    
    return false
  }
  
  /**
   * 检查是否有任一权限
   * @param {Array} permissions 权限数组
   * @returns {Boolean} 是否有任一权限
   */
  const hasAnyPermission = (permissions) => {
    return hasPermission(permissions, 'some')
  }
  
  /**
   * 检查是否有所有权限
   * @param {Array} permissions 权限数组
   * @returns {Boolean} 是否有所有权限
   */
  const hasAllPermissions = (permissions) => {
    return hasPermission(permissions, 'every')
  }
  
  /**
   * 添加权限
   * @param {String|Array} permission 权限代码或权限数组
   */
  const addPermission = (permission) => {
    if (!permission) return
    
    if (typeof permission === 'string') {
      state.permissions.add(permission)
    } else if (Array.isArray(permission)) {
      permission.forEach(p => {
        if (p && typeof p === 'string') {
          state.permissions.add(p)
        }
      })
    }
    
    // 触发权限变化回调
    if (typeof onPermissionChange === 'function') {
      onPermissionChange(Array.from(state.permissions))
    }
  }
  
  /**
   * 移除权限
   * @param {String|Array} permission 权限代码或权限数组
   */
  const removePermission = (permission) => {
    if (!permission) return
    
    if (typeof permission === 'string') {
      state.permissions.delete(permission)
    } else if (Array.isArray(permission)) {
      permission.forEach(p => {
        if (p && typeof p === 'string') {
          state.permissions.delete(p)
        }
      })
    }
    
    // 触发权限变化回调
    if (typeof onPermissionChange === 'function') {
      onPermissionChange(Array.from(state.permissions))
    }
  }
  
  /**
   * 清空所有权限
   */
  const clearPermissions = () => {
    state.permissions.clear()
    
    // 触发权限变化回调
    if (typeof onPermissionChange === 'function') {
      onPermissionChange([])
    }
  }
  
  /**
   * 权限过滤器 - 过滤有权限的项目
   * @param {Array} items 项目数组
   * @param {String|Function} permissionGetter 权限获取器
   * @returns {Array} 过滤后的项目数组
   */
  const filterByPermission = (items, permissionGetter = 'permission') => {
    if (!Array.isArray(items)) return []
    
    return items.filter(item => {
      let itemPermission
      
      if (typeof permissionGetter === 'function') {
        itemPermission = permissionGetter(item)
      } else if (typeof permissionGetter === 'string') {
        itemPermission = item[permissionGetter]
      } else {
        return true
      }
      
      // 如果项目没有权限要求，默认显示
      if (!itemPermission) return true
      
      return hasPermission(itemPermission)
    })
  }
  
  /**
   * 按钮权限检查器
   * @param {Object} button 按钮配置
   * @returns {Boolean} 按钮是否可见
   */
  const checkButtonPermission = (button) => {
    if (!button || typeof button !== 'object') return true
    
    // 检查权限字段
    const permission = button.permission || button.perms || button.auth
    if (!permission) return true
    
    return hasPermission(permission)
  }
  
  /**
   * 菜单权限检查器
   * @param {Object} menu 菜单配置
   * @returns {Boolean} 菜单是否可见
   */
  const checkMenuPermission = (menu) => {
    if (!menu || typeof menu !== 'object') return true
    
    // 检查权限字段
    const permission = menu.permission || menu.perms || menu.auth || menu.meta?.permission
    if (!permission) return true
    
    return hasPermission(permission)
  }
  
  /**
   * 路由权限检查器
   * @param {Object} route 路由配置
   * @returns {Boolean} 路由是否可访问
   */
  const checkRoutePermission = (route) => {
    if (!route || typeof route !== 'object') return true
    
    // 检查权限字段
    const permission = route.permission || route.meta?.permission || route.meta?.perms
    if (!permission) return true
    
    return hasPermission(permission)
  }
  
  /**
   * 创建权限指令
   * @returns {Object} Vue指令对象
   */
  const createPermissionDirective = () => {
    return {
      mounted(el, binding) {
        const permission = binding.value
        if (!hasPermission(permission)) {
          el.style.display = 'none'
          el.setAttribute('data-permission-hidden', 'true')
        }
      },
      updated(el, binding) {
        const permission = binding.value
        const isHidden = el.getAttribute('data-permission-hidden') === 'true'
        
        if (hasPermission(permission)) {
          if (isHidden) {
            el.style.display = ''
            el.removeAttribute('data-permission-hidden')
          }
        } else {
          if (!isHidden) {
            el.style.display = 'none'
            el.setAttribute('data-permission-hidden', 'true')
          }
        }
      }
    }
  }
  
  /**
   * 获取权限统计信息
   * @returns {Object} 统计信息
   */
  const getPermissionStats = () => {
    return {
      total: state.permissions.size,
      permissions: Array.from(state.permissions),
      loading: state.loading,
      error: state.error
    }
  }
  
  /**
   * 异步加载权限
   * @param {Function} loader 权限加载函数
   * @returns {Promise} 加载Promise
   */
  const loadPermissions = async (loader) => {
    if (typeof loader !== 'function') {
      throw new Error('权限加载器必须是一个函数')
    }
    
    state.loading = true
    state.error = null
    
    try {
      const permissions = await loader()
      setPermissions(permissions)
      return permissions
    } catch (error) {
      state.error = error
      throw error
    } finally {
      state.loading = false
    }
  }
  
  // 初始化权限
  if (userPermissions) {
    setPermissions(userPermissions)
  }
  
  return {
    // 状态
    state: readonly(state),
    isLoading,
    hasError,
    permissionCount,
    permissionList,
    
    // 方法
    setPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    addPermission,
    removePermission,
    clearPermissions,
    filterByPermission,
    checkButtonPermission,
    checkMenuPermission,
    checkRoutePermission,
    createPermissionDirective,
    getPermissionStats,
    loadPermissions
  }
}

export default usePermission
