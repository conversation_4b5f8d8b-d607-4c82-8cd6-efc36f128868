<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <page-table ref="innerTableRef" :search="searchConfig" :config="tableConfig"
                   :header="tableHeader()" @operation="onOperation" @selection-change="onSelectChange">
        <template #loanRateMax="{ row }">{{ row.loanRateMin + '%' + ' ~ ' + row.loanRateMax + '%' }}</template>
        <template #tag="{ row }">
          <el-tag v-for="tag in row.tag ? row.tag.split(',') : []" size="small" class="mr-1">{{ tag }}</el-tag>
        </template>
        <template #loanQuotaMax="{ row }"> {{ row.loanQuotaMax }}万元</template>
        <template #loanTimeMax="{ row }"> {{ row.loanTimeMax }}个月</template>
      </page-table>
      <component :is="components[dynamicComponent]" v-if="dialogVisible" v-model:visible="dialogVisible" :row="rowData"
                 @refresh="refresh"></component>
    </div>
  </div>
</template>
<script setup>
import {searchConfig, tableConfig, tableHeader} from './data';
import {useMessageBox} from '/@/hooks/message';
import {deleteById, updateStatus, updateHot, exportData} from '../../api';
import {downBlobFile} from '/@/utils/other';
import {ElMessage} from 'element-plus';


let rowData = ref(null);
let dialogVisible = ref(false);
let dynamicComponent = ref('');
const components = {
  sort: defineAsyncComponent(() => import('./components/sort')),
  recommend: defineAsyncComponent(() => import('./components/recommend')),
};
const router = useRouter();
// 表格selection
const state = reactive({
  selectedData: [],
});
const onSelectChange = (selection) => {
  state.selectedData = selection;
};

function refresh() {
  innerTableRef.value?.onSearchHandle();
}

let innerTableRef = ref('');

// 表格操作方法
const onOperation = async ({field, row, btn}, refresh) => {
  rowData.value = row || null;
  const productIds = state.selectedData.map((item) => item.id);
  switch (field) {
    case 'ups':
      if (!productIds.length) return ElMessage.error('请选择要上架的数据');
      useMessageBox()
          .confirm('是否批量上架勾选的产品?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            btn.loading = true;
            updateStatus(productIds.map(id => {
              return {id, status: 1}
            })).then(() => {
              btn.loading = false;
              ElMessage.success('操作成功');
              refresh && refresh();
            });
          }).finally(() => {
        btn.loading = false;
      });
      break;
    case 'downs':
      if (!productIds.length) return ElMessage.error('请选择要下架的数据');
      useMessageBox()
          .confirm('是否批量下架勾选的产品?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            btn.loading = true;
            updateStatus(productIds.map(id => {
              return {id, status: 0}
            })).then(() => {
              btn.loading = false;
              ElMessage.success('操作成功');
              refresh && refresh();
            }).finally(() => {
              btn.loading = false;
            });
          });
      break;
    case 'export':
      btn.loading = true;
      try {
        btn.loading = !(await downBlobFile(exportData, { productIds, ...toRaw(innerTableRef.value.oldParams) }));
      } catch (error) {
        btn.loading = false;
        ElMessage.error('下载失败');
      }
      break;
    case 'matcnEnterprise':
      dialogVisible.value = true;
      dynamicComponent.value = 'recommend';
      break;
    case 'sort':
      dialogVisible.value = true;
      dynamicComponent.value = field;
      break;
    case 'audit':
      router.push({
        path: `../detail/index`,
        query: {id: row.id, type: 'auditPage'},
      });
      break;
    case 'detail':
      router.push({
        path: `../detail/index`,
        query: {id: row.id},
      });
      break;
    case 'edit':
      router.push({
        path: `../edit/index`,
        query: {id: row.id},
      });
      break;
    case 'add':
      router.push({
        path: `../edit/index`,
      });
      break;
    case 'del':
      useMessageBox()
          .confirm('确定删除当前数据?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            deleteById(row.id).then(() => {
              ElMessage.success('删除成功');
              refresh && refresh();
            });
          });
      break;
    case 'hot':
      useMessageBox()
          .confirm('是否设置为热门?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            updateHot({id: row.id, ifHot: 1}).then(() => {
              ElMessage.success('操作成功');
              refresh && refresh();
            });
          });
      break;
    case 'delHot':
      useMessageBox()
          .confirm('是否取消热门?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            updateHot({id: row.id, ifHot: 0}).then(() => {
              ElMessage.success('操作成功');
              refresh && refresh();
            });
          });
      break;
    case 'up':
      useMessageBox()
          .confirm('是否上架?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            updateStatus([{id: row.id, status: 1}]).then(() => {
              ElMessage.success('操作成功');
              refresh && refresh();
            });
          });
      break;
    case 'down':
      useMessageBox()
          .confirm('是否下架?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            updateStatus([{id: row.id, status: 0}]).then(() => {
              ElMessage.success('操作成功');
              refresh && refresh();
            });
          });
      break;
  }
};
</script>
