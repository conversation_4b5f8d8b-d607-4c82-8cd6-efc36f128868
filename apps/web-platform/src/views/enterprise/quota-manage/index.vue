<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<!-- <co-table :config="tableConfig" :header="tableHeader" /> -->
			<page-table :search-config="searchConfig" :table-config="tableConfig" :table-header="tableHeader" />
		</div>
	</div>
</template>

<script setup>
import { orgCreditLimit } from '/@/api/information/theme.ts';
const searchConfig = {
	items: [{ prop: 'orgName', type: 'input', attrs: { placeholder: '请填写企业名称' } }],
};
const tableConfig = {
	request: {
		apiName: orgCreditLimit,
	},
	operation: false,
};
const tableHeader = [
	{ type: 'index', label: '序号', width: 55 },
	{ prop: 'creditNum', label: '授信记录编号', 'min-width': 300, showOverflowTooltip: true },
	{ prop: 'orgName', label: '授信企业名称', 'min-width': 300, showOverflowTooltip: true },
	{ prop: 'highestLoanAmount', label: '授信额度（元）', 'min-width': 200 },
	{ prop: 'creditGrade', label: '信用等级', width: 180 },
	{ prop: 'insertTime', label: '授信时间', 'min-width': 180 },
];
</script>
