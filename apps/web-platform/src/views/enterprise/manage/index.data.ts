import { getList } from '/@/api/information/theme';
// 搜索配置
export const defaultSearchParams = {
	input_prepend: 'businessId',
};
export const searchConfig = {
	items: [
		{
			prop: 'input',
			type: 'input',
			attrs: {
				placeholder: '请输入内容',
			},
			prepend: {
				attrs: {
					placeholder: '请选择',
					clearable: false,
				},
				option: [
					{ label: '企业ID', value: 'businessId' },
					{ label: '企业名称', value: 'businessName' },
					{ label: '法人姓名', value: 'legalPersonName' },
					{ label: '社会统一代码', value: 'unifiedSocialCreditCode' },
				],
			},
		},
		{
			prop: 'disabledState',
			type: 'select',
			attrs: {
				placeholder: '请选择企业状态',
			},
			option: [
				{ label: '正常', value: 0 },
				{ label: '黑名单', value: 1 },
			],
		},
		{
			prop: 'times',
			type: 'datetimerange',
			splitProp: ['startTime', 'stopTime'],
			attrs: {
				startPlaceholder: '开始时间',
				endPlaceholder: '结束时间',
				formate: 'YYYY-MM-DD HH:mm:ss',
				valueFormat: 'YYYY-MM-DD HH:mm:ss',
			},
		},
	],
};
// 表头配置
export const tableHeader = [
	{ type: 'selection', label: '', width: 55 },
	{ prop: 'id', label: '企业ID', 'min-width': 180 },
	{ prop: 'businessName', label: '企业名称', 'min-width': 180 },
	{ prop: 'unifiedSocialCreditCode', label: '统一社会信用代码', 'min-width': 200, showOverflowTooltip: true },
	{ prop: 'legalPersonName', label: '法定代表人姓名', width: 180, showOverflowTooltip: true },
	{ prop: 'insertTime', label: '提交时间', width: 180, showOverflowTooltip: true },
	{ prop: 'disabledState', label: '黑名单', width: 120, showOverflowTooltip: true },
];
// 表格配置
export const tableConfig = {
	dic: {
		disabledState: {
			data: [
				{ label: '是', value: '1' },
				{ label: '否', value: '0' },
			],
			color: { 1: 'color-danger' },
		},
	},
	request: {
		apiName: getList,
		// formatData: (data: TableData[]) => {
		// 	data.forEach((item) => {
		// 		item.fileJson && (item.fileJson = JSON.parse(item.fileJson));
		// 		item.isBlack = item.fileJson?.xydBlack === 'true';
		// 	});
		// 	return data;
		// },
	},
	operation: {
		width: 180,
	},
};
