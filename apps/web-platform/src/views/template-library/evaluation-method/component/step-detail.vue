<template>
	<div>
		<!-- 评审项 -->
		<div class="co-flex co-flex-between">
			<div>
				<el-button link type="primary" @click.native="btnCallback('add')">添加评审项</el-button>
				<el-button link type="primary" class="text-danger" @click.native="btnCallback('deleteAll')">全部删除</el-button>
			</div>
			<span v-if="methodName == '10' && rejectContent && rejectContent.length > 0" class="co-text-warning">
				否决条件：
				<span v-html="rejectContent" />
			</span>
		</div>
		<el-form ref="sheetForm" :model="formInfo" :rules="clauseRules">
			<el-table :data="formInfo.tableData" :header-cell-style="{ backgroundColor: '#dee7ff' }" stripe border>
				<el-table-column type="index" width="60" align="center" />

				<el-table-column prop="itemName" width="200">
					<template #header>
						<span class="co-text-red">*</span>
						<span>评审项名称</span>
					</template>
					<template #default="scope">
						<el-form-item :prop="'tableData.' + scope.$index + '.itemName'" label-width="0" :rules="clauseRules.itemName">
							<el-input v-model="scope.row.itemName" placeholder="请输入评审项名称" />
						</el-form-item>
					</template>
				</el-table-column>

				<el-table-column prop="itemStandard">
					<template #header>
						<span class="co-text-red">*</span>
						<span>评审标准</span>
					</template>
					<template #default="scope">
						<el-form-item :prop="'tableData.' + scope.$index + '.itemStandard'" label-width="0" :rules="clauseRules.itemStandard">
							<el-input v-model="scope.row.itemStandard" placeholder="请输入评审标准" type="textarea" :rows="2" />
						</el-form-item>
					</template>
				</el-table-column>

				<!-- 最高分 -->
				<el-table-column v-if="methodName == '10' && stepItem.stepType == 1" prop="itemMaxScore" width="140">
					<template #header>
						<span class="co-text-red">*</span>
						<span>最高分</span>
					</template>
					<template #default="scope">
						<el-form-item :prop="'tableData.' + scope.$index + '.itemMaxScore'" label-width="0" :rules="clauseRules.itemMaxScore">
							<el-input-number v-model="scope.row.itemMaxScore" placeholder="请输入" type="text" :controls="false" :min="0" :max="100" :precision="0" style="width: 100%" />
						</el-form-item>
					</template>
				</el-table-column>

				<el-table-column v-else prop="ifAbandoned" width="170">
					<template #header>
						<span class="co-text-red">*</span>
						<span>是否标“&#9733;”项</span>
					</template>
					<template #default="scope">
						<el-form-item :prop="'tableData.' + scope.$index + '.ifAbandoned'" label-width="0" :rules="clauseRules.ifAbandoned">
							<whether :field="'ifAbandoned'" :value="scope.row.ifAbandoned" :index="scope.$index" @listenCallback="listenCallback" />
						</el-form-item>
					</template>
				</el-table-column>

				<el-table-column width="100">
					<template #header>
						<span>操作</span>
					</template>
					<template #default="scope">
						<el-button link type="primary" class="text-danger" @click.native="btnCallback('deleteClause', scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-form>
	</div>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, defineAsyncComponent, defineExpose } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';

const whether = defineAsyncComponent(() => import('./whether.vue'));

const emit = defineEmits(['detailCallBack']);
const debounce = (fn, ms = 500) => {
	let timeoutId;
	return function (...args) {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => fn.apply(this, args), ms);
	};
};
const props = defineProps({
	stepItem: { type: Object, default: () => ({}) },
	methodName: { type: String, default: '' },
	index: { type: Number, default: 0 },
});

const sheetForm = ref();
const formInfo = ref({
	tableData: [],
});
const clauseRules = ref({
	itemName: [{ required: true, message: '请输入评审项名称', trigger: 'blur' }],
	itemStandard: [{ required: true, message: '请输入评审标准', trigger: 'blur' }],
	itemMaxScore: [{ required: true, message: '请输入最高分', trigger: 'blur' }],
	ifAbandoned: [{ required: true, message: '请选择是否废标项', trigger: 'change' }],
});

const rejectContent = ref();

watch(
	() => props.stepItem,
	(n) => {
		if (!n) return;
		formInfo.value.tableData = n.items || [];
		if (n.stepParam) {
			const stepParamData = JSON.parse(n.stepParam);
			rejectContent.value = stepParamData.rejectCondition && stepParamData.rejectCondition.ruleContent;
		}
	},
	{ deep: true }
);

// 按钮回调方法
const btnCallback = debounce(async function (mark, index) {
	switch (mark) {
		case 'add': // 添加评审项
			formInfo.value.tableData.push({ ifAbandoned: false });
			break;

		case 'deleteAll': // 全部删除
			useMessageBox()
				.confirm('确定要全部删除')
				.then(() => {
					formInfo.value.tableData = [];
				});
			break;

		case 'deleteClause': // 删除评审项，表格操作列
			formInfo.value.tableData.splice(index, 1);
			break;
	}
});

function listenCallback({ field, value, index }) {
	switch (field) {
		case 'ifAbandoned':
			formInfo.value.tableData[index].ifAbandoned = value;
			break;
	}
}

// 表单校验
const stepItemValidate = async () => {
	const valid = await sheetForm.value.validate();
	// 最高分等于满分
	const sum = formInfo.value.tableData.reduce((accumulator, currentItem) => {
		return Number(accumulator) + Number(currentItem.itemMaxScore);
	}, 0);

	if (props.stepItem.stepType == 1 && sum != Number(props.stepItem.standardScore)) {
		useMessage().warning('最高分之和应等于满分');
		return;
	}
	if (!valid) return;
	emit('detailCallBack', { index: props.index, type: 'save', formData: formInfo.value.tableData });
};

// 保存
function save() {
	emit('detailCallBack', { index: props.index, type: 'save', formData: formInfo.value.tableData });
}

defineExpose({ stepItemValidate, save });
</script>

<style lang="scss" scoped>
.co-flex {
	display: flex;
}
.co-flex-between {
	justify-content: space-between;
}
.co-text-warning {
	color: #fc9b45;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
.tip {
	display: table;
	padding: 0 10px;
	min-width: 400px;
	background-color: #ffedd8;
	color: #fe9c2e;
	text-align: center;
}

.item-tip {
	line-height: 18px;
}
.w-tip {
	position: absolute;
	padding-top: 4px;
	color: #f56c6c;
	font-size: 12px;
	line-height: 1;
	font-weight: 400;
}
::v-deep .error-input .el-input .el-input__inner {
	border-color: #f56c6c;
}
.co-text-red {
	color: red;
}
</style>
