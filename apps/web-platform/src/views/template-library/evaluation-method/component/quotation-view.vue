<template>
	<div>
		<div class="title">{{ tableData1[0]?.computeName }}</div>
		<el-table :data="tableData1" :header-cell-style="{ backgroundColor: '#fafafa', color: '#000' }" style="width: 100%" size="small" stripe border>
			<el-table-column prop="standardName" label="名称" width="180" />
			<el-table-column prop="computeMethod" label="计算公式"></el-table-column>
			<el-table-column prop="ruleContent" label="说明"></el-table-column>
		</el-table>

		<div class="title">
			{{ tableData2[0]?.computeName }}
		</div>
		<el-table :data="tableData2" :header-cell-style="{ backgroundColor: '#fafafa', color: '#000' }" style="width: 100%" size="small" stripe border>
			<el-table-column prop="computeName" label="名称" width="180" />
			<el-table-column prop="computeMethod" label="计算公式" />
			<el-table-column prop="ruleContent" label="说明" />
		</el-table>

		<div class="title">
			{{ tableData3[0]?.computeName }}
		</div>
		<el-table :data="tableData3" :header-cell-style="{ backgroundColor: '#fafafa', color: '#000' }" style="width: 100%" size="small" stripe border>
			<el-table-column prop="computeName" label="名称" width="180" />
			<el-table-column prop="computeMethod" label="计算公式"></el-table-column>
			<el-table-column prop="ruleContent" label="说明"></el-table-column>
		</el-table>
	</div>
</template>
<script setup>
import { ref, reactive, defineProps, defineExpose } from 'vue';

const props = defineProps({
	quotationData: { type: Object, default: () => {} },
});
const tableData1 = ref([]);
const tableData2 = ref([]);
const tableData3 = ref([]);
const flag = ref(true);

const getDetail = (n) => {
	nextTick(() => {
		flag.value = false;
		const stepParamObj = typeof n.stepParam === 'string' ? JSON.parse(n.stepParam || '') : n.stepParam || {};
		tableData1.value.push(stepParamObj.standardPriceRule);
		tableData2.value.push(stepParamObj.bidPriceDeviationRule);
		tableData3.value.push(stepParamObj.quotationScoreRule);
	});
};
// watch(() => props.quotationData, (newVal) => {
// 	if (Object.keys(newVal).length === 0) return
// 	if (flag.value) {
// 		getVal()
// 	}
// }, { deep: true })

defineExpose({ getDetail });
</script>
<style lang="scss" scoped>
.title {
	font-size: 13px;
	font-weight: 700;
	margin: 10px 0;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
	width: 500px;
}
</style>
