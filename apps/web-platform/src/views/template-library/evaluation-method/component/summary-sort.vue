<template>
	<div class="sumbox">
		<el-radio-group v-model="candidateParam.candidateType" class="co-p-l-20">
			<!-- 综合评分法 -->
			<el-radio v-if="methodName == '10'" :class="{ active: candidateParam.candidateType == 0 }" :label="0">
				汇总得分由高到低顺序推荐
				<el-input-number
					v-model="candidateParam.candidateNum"
					class="r-i w-1"
					:class="{ valid: showTip && !candidateParam.candidateNum }"
					placeholder="请输入"
					:controls="false"
					:min="0"
					:precision="0"
					@blur="showTip = false"
				/>
				名中标候选人，汇总得分相同的，按照最后报价由低到高的顺序推荐。汇总得分且最后报价相同的，由评审小组按照技术指标优劣顺序推荐中标候选人。
			</el-radio>
			<!-- 最低评标价法 -->
			<el-radio v-else-if="methodName == '11'" :class="{ active: candidateParam.candidateType == 0 }" :label="0">
				按最终报价由低到高排列顺序推荐
				<el-input-number
					v-model="candidateParam.candidateNum"
					class="r-i w-1"
					:class="{ valid: showTip && !candidateParam.candidateNum }"
					placeholder="请输入"
					:controls="false"
					:min="0"
					:precision="0"
					@blur="showTip = false"
				/>
				名中标候选人，报价相同时，由评审小组按照质量、服务优劣顺序推荐。
			</el-radio>
		</el-radio-group>

		<div class="co-p-l-20 co-m-t-10" style="position: relative">
			<div v-if="showTip && !!candidateParam.candidateType" class="valid-tip vaild-tip-1">{{ tipInfo }}</div>
			<div class="co-text-gray tip">
				定标规则:
				<div>{{ noteTip }}</div>
			</div>
		</div>

		<el-table v-if="methodName == '10'" class="co-m-t-20" :data="tableData" :header-cell-style="{ backgroundColor: '#dee7ff', color: '#000' }" style="width: 100%" size="small" stripe border>
			<el-table-column prop="name" label="名称" width="180" />
			<el-table-column prop="content" label="计算公式" />
			<el-table-column prop="ruleContent" label="说明" />
		</el-table>
	</div>
</template>
<script setup>
import { ref, reactive, defineProps, watch, defineEmits, computed } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';

const emit = defineEmits([]);

const props = defineProps({
	summaryEchoData: { type: Object, default: () => {} },
	methodName: { type: String, default: '' },
});
const candidateParam = reactive({
	candidateType: 0, // 推荐类型 0 、1、 2
	candidateNum: undefined, // 候选人数
	minNum: undefined,
	maxNum: undefined,
	recommendMethod: '', // 候选人推荐方法
	calibrateRule: '', // 定标规则
});
const showTip = ref(true);
const tipInfo = ref('请选择定标方式');
const tableData = reactive([
	{
		sortType: 0,
		name: '汇总得分',
		content: '汇总得分=报价得分+技术得分',
		ruleContent: `-`,
	},
]);

const noteTip = computed(() => {
	if (props.methodName === '10') {
		return '汇总得分由高到低顺序推荐中标候选人，汇总得分相同的，按照最后报价由低到高的顺序推荐。汇总得分且最后报价相同的，由评审小组按照技术指标优劣顺序推荐中标候选人。';
	} else if (props.methodName === '11') {
		return '按最终报价由低到高排列顺序推荐中标候选人，报价相同时，由评审小组按照质量、服务优劣顺序推荐。';
	}
});
watch(
	() => props.summaryEchoData,
	(n) => {
		if (!n) return;
		if (!n.candidateParam) return;
		Object.assign(candidateParam, {
			minNum: n.candidateParam['minNum'],
			maxNum: n.candidateParam['maxNum'],
			candidateNum: n.candidateParam['candidateNum'],
		});
	},
	{ deep: true }
);

// 表单校验
function summaryValidate() {
	showTip.value = candidateParam['candidateNum'] == undefined;
	if (showTip.value) {
		useMessage().warning('汇总排名表单尚未填写');
	}
	return !showTip.value;
}

// 数据回调(提交)
function summaryDataCb() {
	switch (props.methodName) {
		case '10': // 综合评分法
			Object.assign(candidateParam, {
				recommendMethod: `汇总得分由高到低顺序推荐 ${candidateParam.candidateNum} 名中标候选人，汇总得分相同的，按照最后报价由低到高的顺序推荐。汇总得分且最后报价相同的，由评审小组按照技术指标优劣顺序推荐中标候选人。`,
				calibrateRule: noteTip.value,
			});
			return {
				resultGatherModeParam: JSON.stringify({
					candidateParam: candidateParam,
					sortParam: tableData[0],
				}),
			};
		case '11': // 最低评标价法
			Object.assign(candidateParam, {
				recommendMethod: `按最终报价由低到高排列顺序推荐 ${candidateParam.candidateNum} 名中标候选人，报价相同时，由评审小组按照质量、服务优劣顺序推荐。`,
				calibrateRule: noteTip.value,
			});
			return {
				resultGatherModeParam: JSON.stringify({
					candidateParam: candidateParam,
					sortParam: {},
				}),
			};
	}
}
defineExpose({ summaryValidate, summaryDataCb });
</script>
<style lang="scss" scoped>
.label {
	line-height: 40px;
	font-size: 14px;
	color: #606266;
	font-weight: 700;
}

::v-deep .el-radio {
	display: block;
	line-height: 30px;
}
::v-deep .el-radio__label {
	display: inline-flex;
	flex-wrap: wrap;
	text-indent: -3px;
	padding-left: 10px !important;
	white-space: pre-wrap;
}

.r-s,
.r-i {
	display: inline-block;
	line-height: 30px;
}
::v-deep .r-i .el-input__inner {
	border: none;
	border-bottom: solid 1px #4c6ee7;
	border-radius: 0;
	line-height: 30px;
	height: 25px;
}
.w-1,
::v-deep .w-1 .el-input__inner {
	width: 50px !important;
}
::v-deep .w-1 .el-input__inner {
	padding: 5px;
}
.w-2 {
	width: 350px;
}
::v-deep .tip {
	line-height: 18px;
	.el-radio__label {
		font-weight: 400;
		font-size: 13px !important;
		line-height: 22px;
		color: #606266;
	}
}

::v-deep .active {
	.valid .el-input__inner {
		border-bottom: solid 1px #f56c6c !important;
	}
}
.valid-tip {
	color: #f56c6c;
	position: absolute;
}
.vaild-tip-1 {
	top: -18px;
}
.sumbox {
	.co-p-l-20 {
		padding-left: 20px;
	}
	.co-m-t-10 {
		margin-top: 10px;
	}
	.tip {
		line-height: 18px;
	}
	.co-text-gray {
		color: #8e8e8e;
	}
	.co-m-t-20 {
		margin-top: 20px;
	}
}
</style>
