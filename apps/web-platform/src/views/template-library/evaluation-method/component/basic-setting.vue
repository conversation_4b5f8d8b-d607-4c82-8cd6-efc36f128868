<template>
	<div class="method-page">
		<el-form ref="ruleForm" :model="formData" :rules="rules" label-width="150px" :label-position="'top'" class="vpage">
			<el-row :gutter="24">
				<!-- 评标办法模板名称 -->
				<el-col :span="8">
					<el-form-item label="评标办法模板名称" prop="templateName" >
						<el-input v-model="formData.templateName" clearable placeholder="请输入评标办法模板名称" style="width: 300px;"/>
					</el-form-item>
				</el-col>

				<!-- 评标办法 -->
				<el-col :span="8">
					<el-form-item label="评标办法" prop="methodName"  style="width: 300px;">
						<el-select v-model="formData.methodName" transfer="true" :popper-append-to-body="true" placeholder="请选择" style="width: 100%" @change="listenCallback({ field: 'methodName', value: $event })">
							<el-option v-for="item in methodNameArr" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>

				<!-- 综合评分法 -->
				<!-- 分值构成 -->
				<el-col v-if="formData.methodName == '10'" :span="24">
					<el-form-item label="分值构成" prop="scoreValue">
						<span>本项目评标总分为100分，商务得分（报价得分):</span>
						<el-input-number v-model="scoreComposeParam.priceScore" class="i-s w-1" :class="{ 'i-valid': showScoreTip && !scoreComposeParam.priceScore }" placeholder="请输入" :controls="false" :min="0" :precision="0" @blur="scoreComposeParamValid('blur')"/>
						<span>分 ，技术分:</span>
						<el-input-number v-model="scoreComposeParam.technologyScore" class="i-s w-1" :class="{ 'i-valid': showScoreTip && !scoreComposeParam.technologyScore }" placeholder="请输入" :controls="false" :min="0" :precision="0" @blur="scoreComposeParamValid('blur')" />
						<span>分</span>
						<span v-if="showScoreTip" class="co-text-red valid">本采购项目评标总分为100分</span>
					</el-form-item>
				</el-col>

				<!-- 评分原则 -->
				<el-col v-if="formData.methodName == '10'" :span="24">
					<el-form-item label="评分原则" prop="scorePrincipleType">
						{{ formData.scorePrincipleParam }}
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script setup>
import { ref, reactive, defineProps,onMounted, watch, defineEmits, defineExpose} from 'vue'
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';

const emit = defineEmits(['basicCallBack']);

const ruleForm = ref()

const formData = reactive({
	templateName: '',
	methodName: undefined,
	scoreValue: 'score', // 分值权重构成
	scorePrincipleType: 0,
	scorePrincipleParam: '计算所有评委评分，汇总全部评分后，计算评分均值',
	info: 'a'
})
const rules = reactive({
	templateName: [{ required: true, message: '评标办法模板名称不能为空', trigger: 'change' }],
	methodName: [{ required: true, message: '评标办法名称不能为空', trigger: 'blur' }],
	scoreValue: [{ required: true, message: '分值权重构成不能为空', trigger: 'blur' }],
	scorePrincipleType: [{ required: true }],
	info: [{ required: true }]
})

const methodNameArr= ref([]) // 评标办法名称下拉值
const showRandomTip = ref(false)
const scoreComposeParam = reactive({
	priceScore: undefined,
	technologyScore: undefined,
	businessScore: 0
})
const showScoreTip = ref(false)
const evalComposeParam = reactive({
	evalCompose: '采用有限数量评审制',
	evaleContent: ''
})

const props = defineProps({
	basicEchoData: { type: Object, default: () => {} },
});

watch(() => props.basicEchoData, (newVal) => {
  if (Object.keys(newVal).length == 0) return
		Object.assign(formData, newVal)
		if (newVal.basicInfo != '{}' && newVal.basicInfo != null) {
			const basicInfoObj = JSON.parse(newVal.basicInfo)
			if (formData.methodName == '10') {
				// formData.ifBonus = basicInfoObj.ifBonus || ''
				Object.assign(formData, { ifBonus: basicInfoObj.ifBonus || ''})
				Object.assign(scoreComposeParam, basicInfoObj.scoreComposeParam || {})
				emit('basicCallBack', { field: 'scoreComposeParam', value: scoreComposeParam })
			}
		}
}, { deep: true });

const listenCallback = ({ field, value })=> {
	switch (field) {
		case 'methodName':
			emit('basicCallBack', { field: 'methodName', value })
			ruleForm.value.clearValidate()
			break
	}
}
// 分值权重构成
function scoreComposeParamValid (type) {
	emit('basicCallBack', { field: 'scoreComposeParam', value: {} })
	delete scoreComposeParam.ruleContent
	const keys = Object.keys(scoreComposeParam)
	let allScore = 0
	keys.forEach(e => {
		allScore = allScore + (scoreComposeParam[e] || 0)
	})

	if (type == 'blur') {
		const flag = keys.some(e => {
			return scoreComposeParam[e] == undefined
		})
		showScoreTip.value = allScore !== 100 && !flag
	} else {
		showScoreTip.value = allScore !== 100
	}
	if (allScore == 100) {
		scoreComposeParam.ruleContent = `本项目评标总分为100分，商务得分（报价得分)${scoreComposeParam.priceScore}分，技术分${scoreComposeParam.technologyScore}分`

		emit('basicCallBack', { field: 'scoreComposeParam', value: scoreComposeParam })
	}
}

// 表单校验
const basicValidate = async() => {
	let flag = false
	return await ruleForm.value.validate(valid => {
		if (valid) {
			flag = true
			// 综合评分法
			if (formData.methodName == '10') {
				scoreComposeParamValid()
				flag = flag && !showScoreTip.value
			}
			return flag
		} else {
			flag = false
			useMessage().warning('请补充完整表单内容');
			return flag
		}
	})
	
}

	// 数据回调(提交)
function basicDataCb() {
	let basicInfoObj = {}
	switch (formData.methodName) {
		case '10':
			// 综合评分法
			basicInfoObj = {
				scoreComposeParam: scoreComposeParam,
				evalComposeParam: evalComposeParam
			}
			break
		case '11':
			// 最低评标价法
			basicInfoObj = {
				evalComposeParam: evalComposeParam
			}
			break
	}
	const { methodName, templateName, scorePrincipleType, scorePrincipleParam } = formData
	return {
		templateName,
		methodName,
		scorePrincipleType,
		scorePrincipleParam,
		basicInfo: JSON.stringify(basicInfoObj)
	}
}
async function getInit() {
	const resList = await getDicTionaryAndValue(dictApi['methodName']);
	methodNameArr.value = resList.data
}
onMounted(()=> {
	getInit()
})
defineExpose({ basicValidate, basicDataCb })

</script>
<style lang="scss" scoped>
.method-page .vpage {
	.el-col{
		margin-bottom: 21px
	}
}
::v-deep .el-form--label-top .el-form-item__label {
	padding: 0;
	font-weight: 600;
  font-size: 14px;
}
::v-deep .i-s .el-input__inner {
	border: none;
	border-bottom: solid 1px #4c6ee7;
	border-radius: 0;
	line-height: 30px;
	height: 25px;
}
.w-1,
::v-deep .w-1 .el-input__inner {
	width: 50px !important;
}
.w-2,
::v-deep .w-2 .el-input__inner {
	width: 70px !important;
}
::v-deep .w-1 .el-input__inner,
::v-deep .w-2 .el-input__inner {
	padding: 5px;
}

::v-deep .el-radio {
	display: block;
	line-height: 30px;
}
::v-deep .el-radio__label {
	display: inline-flex;
	flex-wrap: wrap;
	text-indent: -3px;
	padding-left: 10px !important;
	white-space: pre-wrap;
}

.tip {
	line-height: 26px;
}
.valid {
	position: absolute;
	color: #f56c6c;
	font-size: 12px;
	line-height: 1;
	padding-top: 4px;
	left: 20px;
	bottom: -10px;
}

::v-deep .i-valid .el-input__inner {
	border-color: #f56c6c;
}

.s-i {
	display: inline-block;
	width: 140px;
	padding-left: 8px;
}
::v-deep .el-input-number--medium {
	width: 112px;
}
.i-n {
	width: 65px;
}
::v-deep .i-n .el-input__inner {
	border: none;
	width: 60px;
	border-bottom: solid 1px #4c6ee7;
	border-radius: 0;
	line-height: 30px;
	height: 25px;
}
::deep .el-input-number .el-input__wrapper{
	padding: 0;
	border: none;
	border-color: #fff;
	box-shadow: none;
}
</style>
