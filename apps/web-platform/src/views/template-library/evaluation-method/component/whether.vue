<template>
	<div :class="{'c-s-d': true, 'c-s-a' : value }" @click="handleChange" style="display: flex;">
		<span :class="{'c-s-l': true, 'c-s-l-a': value }">
      <el-icon :class="{'c-s-i-a': value}"><CircleCheckFilled /></el-icon>
		</span>
		<span :class="{'c-s-t': true, 'c-s-t-a' : value }"> {{ value ? '是' : '否' }}</span>
	</div>
</template>
<script setup>
import { ref, defineProps, watch, defineEmits, defineExpose} from 'vue'
const emit = defineEmits(['listenCallback']);
const props = defineProps({
  field: { // 对应的参数
    type: String,
    default: ''
  },
  value: { // 值， true、false
    type: Boolean,
    default: false
  },
  dis: {
    type: Boolean,
    default: false
  },
  index: { // table内使用需要指明index
    type: Number,
    default: 0,
    require: false
  }
})
watch(()=>props.value,(n)=>{
})
function handleChange() {
  emit('listenCallback', {
    field: props.field,
    value: !props.value,
    index: props.index
  })
}
</script>
<style lang="scss" scoped>
// 未选中状态 灰色
.c-s-d {
  display: inline-block;
  height: 35px;
  margin-bottom: 2px;
  border: 1px solid #f0f0f0;
  line-height: 35px;
  box-sizing: content-box;
  cursor: pointer;
  .c-s-l {
    height: 35px;
    width: 35px;
    display: inline-block;
    background: #f0f0f0;
    text-align: center;
    line-height: 35px;
    overflow: hidden;
  }
  .el-icon-success {
    font-size: 22px;
    color: #b0b0b0;
  }
  .c-s-t {
    width: 110px;
    display: inline-block;
    overflow: hidden;
    text-align: center;
    color: #b0b0b0;
  }
}
// 激活状态 蓝色 #2C8AFD
.c-s-a {
  border: 1px solid #2C8AFD;
  .c-s-l-a {
    background-color: #2C8AFD;
  }
  .c-s-i-a {
    color: #fff;
  }
  .c-s-t-a {
    color: #2C8AFD;
  }
}
</style>
