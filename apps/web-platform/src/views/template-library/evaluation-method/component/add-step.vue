<template>
	<el-dialog title="添加评审步骤" v-model="stepDialog" width='800px'  @beforeClose="beforeClose">
		<co-form ref="addFormRef" :formData="formData" :formList="formList" labelPosition="top" @event="handlerEvent" class="co-form">
			<!-- 打分类型 -->
      <template v-slot:scoreType>
        <el-radio-group v-model="formData.scoreType">
					<el-radio :label="1" :disabled="formData.technologyScoreMax == 0">
						<span class="s-i">技术打分</span>
						技术分最高：{{ formData.technologyScoreMax }}分，该步骤满分
						<el-input-number v-model="standardScore" class="i-n"
              :class="{ 'i-valid': showVaild  && formDataStepType == 1 && !standardScore}"
              :min="0" :max="formData.technologyScoreMax" :controls="false" />
						分
					</el-radio>
				</el-radio-group>
      </template>
			<!-- 是否否决 -->
			<template v-slot:ifReject>
				<el-radio-group v-model="formData.ifReject" @change="changeRadio">
					<el-radio :label="0">
						<span>否</span>
					</el-radio>
					<el-radio :label="1">
						<span>是</span>
						<span v-if="formDataStepType == 1">
							汇总评审得分小于
							<el-input-number v-model="rejectCondition.totalScore" class="i-n" 
              :class="{ 'i-valid': showVaild && formData.ifReject && !rejectCondition.totalScore }"
               :min="0" :controls="false" />
							分，否决投标人
						</span>
					</el-radio>
				</el-radio-group>
			</template>
			<!-- 否决条件 -->
			<template #customrejectType>
				<span class="co-text-warning" v-html="rejectCondition.ruleContent"></span>
			</template>
		</co-form>
    <template #footer>
			<span class="dialog-footer">
				<el-button @click="onSubmit" type="primary">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>
<script setup>
import { ref, reactive,onMounted, defineEmits, defineExpose} from 'vue'
import { useMessage } from '/@/hooks/message';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';
import { addStepFormList } from '../data.js'

const emit = defineEmits(['addStepCb']);
const stepList = ref()
const rejectCondition= reactive({
  totalScore: undefined,
  ruleContent: ''
})

const addFormRef = ref()
const stepDialog = ref(false)
const formData = ref({})
const formList = ref()
const showVaild = ref(true)
const standardScore= ref()
const formDataStepType = ref()

async function getDicList() {
  // 评审类型步骤
	const bidEvaluationStepTypeList = await getDicTionaryAndValue(dictApi['bidEvaluationStepType']);
  // 评标办法名称
  const methodNameList = await getDicTionaryAndValue(dictApi['methodName']);
  // 资格审查方
  const reviewerList = await getDicTionaryAndValue(dictApi['reviewer']);

  const pqrTypeItem = addStepFormList.filter((i) =>{ return i.id == 'pqrType'})
  pqrTypeItem[0].list = reviewerList.data

  const methodNameItem = addStepFormList.filter((i) => { return i.id == 'methodName'})
  methodNameItem[0].list = methodNameList.data

  stepList.value = bidEvaluationStepTypeList.data
	formList.value = addStepFormList
}

async function changeRadio() {
  let formDatas = await addFormRef.value.getFormDataKey()
  formDataStepType.value = formDatas.stepType
  addFormRef.value.setFormDataKey('ifReject', formData.value.ifReject);
}

function handlerEvent(row, val) {
  if (row.id == 'stepType') {
		if (val == '1') {
			addFormRef.value.setFormDataKey('scoreType', '1');
    }
    if (val == '0') {
      Object.assign(rejectCondition, { ruleContent: '标 &#9733; 项评审不通过时，按照评标委员评审结果少数服从多数的原则进行否决。'})
    } else if(val == '1' && formData.value.ifReject) {
      Object.assign(rejectCondition, { ruleContent: `汇总评审得分小于${rejectCondition.totalScore}分，否决投标人`})
    }
    formDataStepType.value = val
  }
}


function beforeClose () {
  stepDialog.value = false
}

const onSubmit = async ()=> {
  const valid = await addFormRef.value.getFormData().catch((e)=>{
    useMessage().warning('请补充完整表单内容');
  })
  if (!valid) return false;

  const formdata = await addFormRef.value.getFormDataKey();

	let data = { ...formdata, ...formData.value };
  if (data.stepType == 1 && data.scoreType == 1 && !standardScore.value) {
    useMessage().warning('请补充完整表单内容');
    return false
  }
  if (data.ifReject && !rejectCondition.totalScore) {
    useMessage().warning('请补充完整表单内容');
    return false
  }
  if (data.stepType == '0') {
    Object.assign(rejectCondition, { ruleContent: '标 &#9733; 项评审不通过时，按照评标委员评审结果少数服从多数的原则进行否决。'})
  }
  const _obj = Object.assign({}, data, {
    ifPqr: data.stepType == '0' ? data.ifPqr == '1' : null,
    pqrType: (data.stepType == '0' && data.ifPqr == '1') ? Number(data.pqrType) : null,
    stepType: data.stepType,
    standardScore: data.methodName == '10' && data.stepType == '1' ? standardScore.value : 0,
    // 否决条件
    stepParam: JSON.stringify({
      standardScore: data.methodName == '10' && data.stepType == '1' ? standardScore.value : 0,
      rejectCondition: rejectCondition,
      ifReject: data.ifReject ? 1 : 0,
      rejectType: data.rejectType
    })
  })
  emit('addStepCb', _obj)
  beforeClose()

}
// 打分式-满分值设置
function setInit({ tecSum, busSum, scoreComposeParam, num, methodName }) {
  stepDialog.value = true
  standardScore.value = undefined
  rejectCondition.totalScore = undefined
  formData.value = {
    sortNum: num,
    methodName: methodName,
    // tecSum-技术分已设置的分值，busSum-商务分已设置的分值,scoreComposeParam-基本信息设置的分值
    technologyScoreMax: Number(scoreComposeParam.technologyScore) - Number(tecSum),
    businessScoreMax: Number(scoreComposeParam.businessScore) - Number(busSum),
    scoreType: 1
  }
  const stepTypeItem = addStepFormList.filter((i) => { return i.id == 'stepType'})

  stepTypeItem[0].list = stepList.value.filter((i) => { return i.value != 2})
  stepTypeItem[1].list = [stepList.value[0]]
  formList.value = addStepFormList
}
onMounted(async () => {
  await getDicList()
})

defineExpose({setInit})

</script>
<style  lang="scss">
// ::v-deep .co-form .co-label-none.el-form-item--default .el-form-item__label{
//   display: none !important;
// }
.co-form {
  .el-form-item.co-form-span12 {
    flex:0 0 48%;
    width: 48%;
    float: left;
  }
  .co-label-none.el-form-item .el-form-item__label{
    display: none !important;
  }
  .co-item-span24 .el-form-item{
    width: 100%;
    float: left;
  }
  .el-form-item.co--formitem-flex  {
    display: flex;
    width: 100%;
    .el-form-item__content{
      margin: 0;
      padding: 2px;
    }
    .el-form-item__label{
      color:#fc9b45;
    }
  }
}
</style>
<style lang="scss" scoped>
.s-i {
	display: inline-block;
	width: 80px;
	padding-left: 8px;
}
::v-deep .el-input-number--medium {
	width: 112px;
}

.i-n {
	width: 65px;
}
::v-deep .i-n .el-input__inner {
	border: none;
	width: 60px;
	border-bottom: solid 1px #4c6ee7;
	border-radius: 0;
	line-height: 30px;
	height: 25px;
}
::v-deep .i-valid .el-input__inner {
	border-color: #f56c6c;
}
.co-text-warning{
  color:#fc9b45;
}
</style>
