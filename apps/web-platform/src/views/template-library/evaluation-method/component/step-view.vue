<template>
	<div v-if="tableData.length > 0">
		<div v-for="(item, index) in tableData" :key="index">
			<el-descriptions class="margin-top" :title="`步骤${index + 1}:  ${item.stepName}`" :column="2" border>
				<el-descriptions-item label="步骤类型">
					{{ stepTypeList[item.stepType]?.label || stepTypeList[item.stepType] }}
				</el-descriptions-item>
				<el-descriptions-item label="是否资格评审项">
					{{ item.ifPqr == 1 ? '是' : '否' }}
				</el-descriptions-item>
				<el-descriptions-item v-if="item.ifPqr" label="资格审查方">
					{{ reviewList[item.pqrType]?.label || reviewList[item.pqrType] || '-' }}
				</el-descriptions-item>
				<el-descriptions-item v-if="item.rejectCondition && item.rejectCondition.ruleContent" label="否决条件">
					<span v-html="item.rejectCondition && item.rejectCondition.ruleContent" />
				</el-descriptions-item>
			</el-descriptions>

			<el-table :data="item.items" :header-cell-style="{ backgroundColor: '#dfdfdfd', color: '#000' }" class="co-m-t-10" style="width: 100%" size="small" stripe border>
				<el-table-column prop="itemName" label="评审项名称" width="180" />
				<el-table-column prop="itemStandard" label="评审标准" />
				<el-table-column v-if="methodName == '10' && item.stepType == 1" label="最高分" prop="itemMaxScore" />
				<el-table-column v-else prop="ifAbandoned">
					<template #header>
						<span class="co-text-red">*</span>
						<span>是否标“&#9733;”项</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>
<script setup>
import { ref, reactive, defineProps, onMounted, watch, defineEmits } from 'vue';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';

const props = defineProps({
	stepsData: { type: Array, default: () => [] },
	ifNeedTecFile: { type: Boolean, default: false },
	methodName: { type: String, default: '' },
});

const tableData = reactive([]);
const qualificationData = ref([]);
const commerceData = ref([]);
const technologyData = ref([]);

const stepTypeList = ref();
const reviewList = ref();
const evaFileType = ref(1);

// 字典获取
async function getDicList() {
	const resList = await getDicTionaryAndValue(dictApi['bidEvaluationStepType']);
	stepTypeList.value = resList.data;

	const resList2 = await getDicTionaryAndValue(dictApi['reviewer']);
	reviewList.value = resList2.data;
}
function changeType(event) {
	evaFileType.value = event;
	// switch (event) {
	// 	case 1:
	// 		tableData.value = qualificationData.value
	// 		break
	// 	case 3:
	// 		tableData.value = commerceData.value
	// 		break
	// 	case 2:
	// 		tableData.value = technologyData.value
	// 		break
	// }
}
let flag = ref(true);

function getVal(n) {
	nextTick(() => {
		flag.value = false;
		if (n.length == 0) return;
		n.forEach((item) => {
			if (item.items && item.items.length) {
				item.items.forEach((i) => {
					i.ifAbandoned = i.ifAbandoned ? '是' : '否';
				});
			}
			if (item.stepParam) {
				const itemStepParam = JSON.parse(item.stepParam);
				item.rejectCondition = itemStepParam.rejectCondition;
			}
		});

		Object.assign(tableData, n);
	});
}
onMounted(async () => {
	await getDicList();
});
defineExpose({ getVal });
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions__title {
	font-size: 13px;
	font-weight: 700;
	margin: 10px 0 0;
}
::v-deep .el-descriptions .el-descriptions-item__cell {
	width: 500px;
}
.co-text-red {
	color: red;
}
</style>
