<template>
	<div>
		<div class="co-flex co-flex-between">
			<el-button class="co-m-b-20" type="primary" mark="addStep" @click="btnCallback('add')">添加评审步骤</el-button>
		</div>
		<div v-for="(row, index) in tableData" :key="index">
			<div class="s-r co-flex-y-center co-font-weight">
				<div class="s-r-f-10">
					<span>步骤 {{ index + 1 }}</span>
				</div>

				<div class="s-r-f-1">
					<span v-if="row.ifPqr" class="qualify">资格审查</span>
					<span>步骤名称：</span>
					<span class="co-text-blue">{{ row.stepName }}</span>
					<span v-if="row.stepType == 1 && methodName == '10'">（满分 {{ row.standardScore || '-' }} 分）</span>
				</div>

				<div class="s-r-f-1">
					<span>步骤类型：</span>
					<span class="co-text-blue">{{ stepTypeList[row.stepType].label || stepTypeList[row.stepType] }}</span>
				</div>

				<div class="s-o">
					<el-button link type="primary" v-if="!row.isExpand" @click="btnCallback('edit', index, row)">编辑</el-button>
					<el-button link type="primary" v-else @click="btnCallback('save', index, row)">保存</el-button>
					<el-button link type="danger" key="danger" @click="btnCallback('delete', index, row)">删除</el-button>
				</div>
			</div>
			<!-- 评审项 :ref="'stepDetailRef' + index"  -->
			<step-detail v-show="row.isExpand"
			:ref="(el) => handleSetInputMap(el, index)"
			class="s-d" :method-name="methodName" :step-item="row" :index="index" @detailCallBack="detailCallBack" />
		</div>

		<!-- 添加评审步骤 -->
		<add-step ref="addStepRef" :method-name="methodName" :score-compose-param="scoreComposeParam"  @addStepCb="addStepCb" />
	</div>
</template>
<script setup>
import { ref, reactive, defineProps,onMounted, watch, defineEmits, defineAsyncComponent, getCurrentInstance, defineExpose} from 'vue'
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';

const emit = defineEmits(['basicCallBack']);
const { proxy: { $forceUpdate }} = getCurrentInstance();

const addStepRef = ref()

const props = defineProps({
	ifNeedTecFile: { type: Boolean, default: false },
	stepEchoData: { type: Array, default: () => [] },
	methodName: { type: String, default:'' },
	scoreComposeParam: { type: Object, default: () => {} },
})

const tableData = ref([])
const stepTypeList = reactive({}) // 步骤类型
const stepDialog = ref(false)
const sortNum = ref(null) // 新增-排序
// stepDetailRef+index


const stepDetail = defineAsyncComponent(() => import('./step-detail.vue'));
const addStep = defineAsyncComponent(() => import('./add-step.vue'))

watch(()=> props.stepEchoData, (n) => {
	if (!n) {
		tableData.value = []
		return
	} else {
		const ars = []
		n.forEach((ele) => {
			if(ele.stepType != 2) {
				ars.push(ele)
			}
		})
		tableData.value = ars
	}
}, { deep: true })

async function getDicList() {
	const resList2 = await getDicTionaryAndValue(dictApi['bidEvaluationStepType']);
	Object.assign(stepTypeList, resList2.data)
}

const inputRefMap = ref({});
const handleSetInputMap = (el,index) => {
  if (el) {
    inputRefMap.value[`stepDetailRef${index}`] = el;
  }
}

// table 行样式
function tableRowClassName({ row, rowIndex }) {
	if (!row.isExpand) {
		return 'bg-blue'
	} else {
		return 'bd-blue'
	}
}

function btnCallback(mark, index, row) {
	switch (mark) {
		case 'add':
			if (!props.methodName) {
				useMessage().warning('请先选择评标办法')
				return
			}

			// 综合评分法 tecSum技术打分 busSum商务打分
			// eslint-disable-next-line no-case-declarations
			let tecSum = 0
			// eslint-disable-next-line no-case-declarations
			let busSum = 0
			if (props.methodName == '10') {
				if (Object.keys(props.scoreComposeParam).length == 0) {
					useMessage().warning('请先完善基本设置的分值权重构成。')
					return
				}

				// 动态设置 技术打分/商务打分满分
				tableData.value.forEach(ele => {
					if (ele.stepType == 1 && ele.scoreType == 1) {
						tecSum = tecSum + Number(ele.standardScore)
					} else if (ele.stepType == 1 && ele.scoreType == 2) {
						busSum = busSum + Number(ele.standardScore)
					}
				})
			}

			sortNum.value = tableData.value.length + 1

			nextTick(() => {
				addStepRef.value.setInit({
					tecSum, busSum,
					scoreComposeParam: props.scoreComposeParam,
					num: sortNum.value,
					methodName: props.methodName
				})
			})
			break
		case 'edit':
			tableData.value[index].isExpand = true
			// $forceUpdate()
			break
		case 'save':
			// 表单校验
			const refasd = inputRefMap.value[`stepDetailRef${index}`]
			refasd.stepItemValidate()
			break
		case 'delete':
			useMessageBox().confirm('确定要删除?').then((res)=>{
				tableData.value.splice(index, 1)
			}).catch(()=>{})
			break
	}
}

// tabel 展开内容（详情） 回调函数
function detailCallBack({ index, type, formData }) {

	switch (type) {
		case 'save': // 保存
			tableData.value[index].items = JSON.parse(JSON.stringify(formData))
			tableData.value[index].isExpand = false
			break
		case 'cancel': // 取消
			if (tableData.value[index].id) {
				tableData.value[index].isExpand = false
			} else {
				tableData.value.splice(index, 1)
			}
			break
	}
}

// 新增步骤-回调
function addStepCb(stepData) {
	tableData.value.push(stepData)
}

// 切换评标办法，置空数据
function blankSteps() {
	tableData.value = []
}

// 数据校验
function stepValidate() {
	const steps = tableData.value
	if (steps.length == 0) {
		useMessage().warning('评审步骤不能为空')
		return false
	}
	const flag = steps.some(item => {
		return item.isExpand
	})
	if (flag) {
		useMessage().warning('请先保存评审步骤')
		return false
	} else {
		return true
	}
}

// 数据回调
function stepDataCb() {
	return tableData.value
}
defineExpose({ blankSteps,stepDataCb, stepValidate});
onMounted(()=>{
	getDicList()
})
</script>
<style lang="scss" scoped>
.co-flex{
	display: flex;
}
.co-flex-between{
	justify-content: space-between;
}
.co-text-warning{
	color: #fc9b45
}
.co-m-b-20{
	margin-bottom: 20px;
}
.s-r {
	width: 100%;
	height: 100%;
	position: relative;
	background-color: rgb(222, 231, 255);
	padding: 9px 30px;
	margin-top: 10px;

	.s-r-f-1 {
		flex: 1;
		text-align: center;
	}
	.s-r-f-10 {
		flex: 0 0 100px;
		text-align: center;
	}
}

.s-d {
	width: 100%;
	height: 100%;
	position: relative;
	padding: 10px 30px;
	background-color: #fff;
	border: solid 1px #dee7ff;
}

.s-o {
	margin-left: auto;
}
.qualify {
	padding: 4px 10px;
	background: #fff;
	border-radius: 13px;
	color: #ff6a41;
	font-weight: 500;
	margin-right: 10px;
}
.co-flex-x-left, .co-flex-y-center{
	display: flex;
	flex-direction: row;
}
.co-flex-y-center{
	align-items: center;
}
.co-font-weight{
	font-weight: 700;
}
.co-text-blue{
	color: #2e5cf6;
}
.co-m-l-15{
	margin-left: 15px;
}
</style>
