// import { dictApi } from '/@/api/dict/index';

export const searchConfig = {
	styles: { background: '#ffffff' },
	inline: true,
	items: [
		{
			prop: 'templateName',
			type: 'input',
			attrs: {
				placeholder: '请输入评标办法模板名称',
				clearable: true,
				label: '评标办法模板名称',
			},
		},
		{
			prop: 'methodName',
			type: 'select',
			option: 'methodName',
			attrs: {
				placeholder: '请选择评标办法',
				clearable: true,
				label: '评标办法',
			},
		},
		{
			prop: 'effectiveState',
			type: 'select',
			option: 'effectiveState',
			attrs: {
				placeholder: '请选择生效状态',
				clearable: true,
				label: '生效状态',
			},
		},
	],
};

// 新增，编辑
export const fromList = [
	{ name: '评标办法模板名称', id: 'templateName', css: 'co-form-span12' },
	{ name: '评标办法', id: 'methodName', css: 'co-form-span12' },
];

export const detailbase = [
	{
		name: '评标办法',
		id: 'methodName',
	},
];
export const detailpm = [
	{
		name: '推荐中标候选人数',
		id: 'candidateNum',
	},
];
export const detailpm2 = [
	...detailpm,
	{
		name: '定标规则',
		id: 'calibrateRule',
	},
];

// 汇总排名
export const detableHeader = [
	{ name: '名称', id: 'name' },
	{ name: '计算公式', id: 'content' },
	{ name: '说明', id: 'ruleContent' },
];

export const addStepFormList = [
	{ name: '评标办法', id: 'methodName', relation: { id: 'none', val: 0 } },
	{ name: '评审类型', id: 'stepType', type: 'radio', list: [], relation: { id: 'methodName', val: '10' } },
	{ name: '评审类型', id: 'stepType', type: 'radio', list: [], relation: { id: 'methodName', val: '11' } },
	{ name: '技术打分', id: 'scoreType', type: 'radio', value: '1', slot: 'scoreType', relation: { id: 'stepType', val: '1' }, css: 'co-label-none' },
	{ name: '步骤名称', id: 'stepName' },
	{ name: '步骤顺序', id: 'sortNum', disabled: true },
	{
		name: '是否是资格审查项',
		id: 'ifPqr',
		type: 'radio',
		list: [
			{ id: 1, name: '是' },
			{ id: 0, name: '否' },
		],
		relation: { id: 'stepType', val: '0' },
		css: 'co-form-span12',
	},
	{ name: '资格审查方', id: 'pqrType', type: 'radio', list: [], relation: { id: 'ifPqr', val: 1 }, css: 'co-form-span12' },
	{ name: '是否否决', id: 'ifReject', slot: 'ifReject', relation: { id: 'stepType', val: '1' }, css: 'co-item-span24' },
	{
		name: '否决条件',
		id: 'customrejectType',
		type: 'text',
		slot: 'customrejectType',
		css: 'co--formitem-flex',
		relation: [
			{ id: 'methodName', val: '10' },
			{ id: 'stepType', val: '0' },
		],
	},
];
