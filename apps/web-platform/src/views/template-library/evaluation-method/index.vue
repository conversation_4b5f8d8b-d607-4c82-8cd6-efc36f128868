<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<co-search ref="searchRef" inline label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" single-mode="icon-hook" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left" />
			<!-- 复制 -->
			<copy-dialog ref="copyDialogRef" @getList="onSearchHandle" />
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { getList, editEvaMethodTemplate, delObjs } from '/@/api/template-library/evaluate-method';
import { searchConfig } from './data';
import { dictApi } from '/@/api/dict/index';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';

const router = useRouter();
const route = useRoute();
const searchData = ref({});
const dicData = ref({});
// 定义的变量
var onSearch = null;
const tableHeader = ref([
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ prop: 'templateName', label: '评标办法模板名称' },
	{ prop: 'methodName', label: '评标办法' },
	{ prop: 'ifContainReviewItem', label: '是否包含资格审查评审项', width: '210' },
	{ prop: 'effectiveState', label: '是否生效', width: '100' },
	{
		prop: 'state',
		label: '模板状态',
		type: 'switch',
		width: '120',
		attrs: {
			'active-value': 1,
			'inactive-value': 0,
			'before-change': (row) => {
				return handleStatusChange(row);
			},
		},
	},
]);
// 表格配置
const tableConfig = ref({
	dic: {
		methodName: { value: dictApi['methodName'] }, // 业务类型
		effectiveState: {
			data: [
				{ label: '生效', value: '1' },
				{ label: '未生效', value: '0' },
			],
		},
		ifContainReviewItem: {
			data: [
				{ label: '是', value: true },
				{ label: '否', value: false },
			],
		},
	},
	operation: {
		fixed: 'right',
		width: 300,
		list: [],
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {}, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
		// formatData: formatData
	},
});

// 查询
function onSearchHandle(data) {
	onSearch({ params: data });
}
function formatData(data) {
	data.forEach((v) => {
		v.state = v.state == 1;
	});
	return data;
}
// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}

// 字典加载完毕
function onDicLoaded(data) {
	data.effectiveState = [
		{ label: '生效', value: '1' },
		{ label: '未生效', value: '0' },
	];
	dicData.value = data;
}

const copyDialog = defineAsyncComponent(() => import('./component/copy-dialog.vue')); // 导出订单

const copyDialogRef = ref();
// 监听表格搜索操作
function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	if (field == 'edit') {
		// 编辑
		router.push({ path: '/template-library/evaluation-method/edit', query: { id: row.id } });
	} else if (field == 'toView') {
		// 查看
		router.push({ path: '/template-library/evaluation-method/detail', query: { id: row.id } });
	} else if (field == 'delete') {
		//  删除
		useMessageBox()
			.confirm('是否确认删除此数据?')
			.then((res) => {
				delObjs({ id: row.id })
					.then((res) => {
						onSearchHandle();
						useMessage().success(res.msg || '操作成功');
					})
					.catch((err) => {
						useMessage().error(err.msg);
					});
			})
			.catch((err) => {});
	} else if (field == 'copy') {
		// 复制
		copyDialogRef.value.openDialog(row);
	} else if (field == 'add') {
		// 新增
		router.push({ path: '/template-library/evaluation-method/edit' });
	} else if (field == 'state') {
		// 模板状态修改
		// handleStatusChange(row)
	}
}
// 模板状态修改
const handleStatusChange = async (row) => {
	const text = row.state == 1 ? '确认要改为关闭状态吗？' : '确认要改为开启状态吗？';
	try {
		const confirmRes = await useMessageBox().confirm(text);
		if (!confirmRes) return false;
		const { code, msg } = await editEvaMethodTemplate({ id: row.id, ...row, state: row.state < 1 ? 1 : 0 }).catch((err) => err);
		if (code === 200) {
			useMessage().success('操作成功');
			onSearchHandle();
		} else {
			useMessage().warning(msg);
		}
		return code === 200;
	} catch (error) {
		return false;
	}
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.btn-list {
	margin-bottom: 10px;
}
</style>
