<template>
	<div id="evlist-template-edit">
		<pro-back-pre title="评标办法模板详情" />
		<div class="ecpage">
			<el-collapse v-model="activeNames" class="detailbody">
				<el-collapse-item title="基本信息" name="1">
					<el-descriptions :column="2" border>
						<el-descriptions-item v-for="item in detailbase" :label="item.name" :key="item.id">
							<div>{{ baseData[item.id] || '' }}</div>
						</el-descriptions-item>
					</el-descriptions>
				</el-collapse-item>
				<el-collapse-item title="评审步骤" name="2">
					<step-view :method-name="methodName" :steps-data="stepsData" :if-need-tec-file="ifNeedTecFile" ref="stepViewRef" />
				</el-collapse-item>
				<el-collapse-item title="报价得分" v-show="methodName == '10'" name="3">
					<quotation-view :method-name="methodName" :quotation-data="quotationData" ref="quotationViewRefs" />
				</el-collapse-item>
				<el-collapse-item title="汇总排名" name="4">
					<el-descriptions :column="1" border v-if="methodName != '10'">
						<el-descriptions-item v-for="item in detailpm" :label="item.name" :key="item.id">
							<div>{{ ldecData[item.id] || '' }}</div>
						</el-descriptions-item>
					</el-descriptions>
					<el-descriptions :column="2" border v-else>
						<el-descriptions-item v-for="item in detailpm2" :label="item.name" :key="item.id">
							<div>{{ ldecData[item.id] || '' }}</div>
						</el-descriptions-item>
					</el-descriptions>
				</el-collapse-item>
				<el-collapse-item name="5">
					<el-table :data="ltable" :header-cell-style="{ backgroundColor: '#fafafa', color: '#000' }">
						<el-table-column prop="name" label="名称"></el-table-column>
						<el-table-column prop="content" label="计算公式"></el-table-column>
						<el-table-column prop="ruleContent" label="说明"></el-table-column>
					</el-table>
				</el-collapse-item>
			</el-collapse>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, defineAsyncComponent, nextTick } from 'vue';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';
import { getDetail, addEvaMethodTemplate, editEvaMethodTemplate } from '/@/api/template-library/evaluate-method';
import { detailbase, detailpm, detailpm2 } from './data.js';

const route = useRoute();

const id = ref();
const activeNames = ref(['1', '2', '3', '4', '5']);
const methodNameArr = ref([]);
const ltable = ref([]);
const ldecData = ref({});
const methodName = ref();
const quotationData = ref({});
const stepsData = ref([]);
const ifNeedTecFile = ref(false);
const quotationViewRefs = ref();
const stepViewRef = ref();

const reviewStepRef = ref();
const scoreComposeParam = reactive({}); // 综合评分法-分值权重构成

const quotationView = defineAsyncComponent(() => import('./component/quotation-view.vue'));
const stepView = defineAsyncComponent(() => import('./component/step-view.vue'));

const baseData = ref({ methodName: '' });

function basicCallBack({ field, value }) {
	switch (field) {
		case 'methodName':
			methodName.value = value;
			reviewStepRef.value.blankSteps();
			break;
		case 'scoreComposeParam':
			Object.assign(scoreComposeParam, value);
	}
}

async function getInit() {
	const resList = await getDicTionaryAndValue(dictApi['methodName']);
	methodNameArr.value = resList.data;
}

function getMethoDetail() {
	getDetail(id.value).then((res) => {
		const { templateName, basicInfo, steps, resultGatherModeParam, scorePrincipleParam } = res.data;
		methodName.value = res.data.methodName;
		const resultObj = JSON.parse(resultGatherModeParam);
		if (methodNameArr.value.length) {
			const filterData = methodNameArr.value.filter((i) => i.value == methodName.value);
			baseData.value = { methodName: filterData[0].label };
		}
		ltable.value = resultObj.sortParam && Object.getOwnPropertyNames(resultObj.sortParam).length != 0 ? [resultObj.sortParam] : [];
		if (methodName.value) {
			ldecData.value = resultObj.candidateParam || {};
		}

		const allSteps = JSON.parse(steps);
		if (methodName.value == '10') {
			// 评审步骤数据回显
			if (allSteps.length > 0) {
				const quotationData =
					allSteps.filter((ele) => {
						return ele.stepType == 2;
					})?.[0] || {};

				const filterDataSteps = allSteps.filter((ele) => {
					return ele.stepType != 2;
				});
				nextTick(() => {
					stepViewRef.value.getVal(filterDataSteps);
					quotationViewRefs.value.getDetail(quotationData);
				});
			}
		} else {
			nextTick(() => {
				stepViewRef.value.getVal(allSteps);
			});
		}
	});
}

onMounted(async () => {
	id.value = route.query.id;
	await getInit();
	if (id.value) {
		getMethoDetail();
	}
});
</script>

<style lang="scss" scoped>
.ecpage {
	padding: 15px;
	border-radius: 8px;
	background-color: #fff;
}
::v-deep .el-collapse {
	border: none;
	padding-bottom: 80px;
}
::v-deep .el-collapse-item__header {
	height: 40px;
	margin-bottom: 10px;
	font-weight: 900;
	font-size: 18px;
}
::v-deep .el-collapse-item__wrap {
	border: none;
}

.co-f-b {
	width: calc(100% - 94px);
	position: fixed;
	bottom: 0;
	margin: 0 -20px;
	padding: 15px 0;
	display: flex;
	justify-content: center;
	-webkit-transition: width 0.28s;
	transition: width 0.28s;
	z-index: 10;
	background-color: #fff;
	-webkit-box-shadow: 0px -5px 10px #f1f1f1;
	box-shadow: 0px -5px 10px #f1f1f1;
}

::v-deep .el-table th.el-table__cell.is-leaf {
	background-color: #f3f6ff !important;
	border-bottom: 1px solid #ebeef5;
}
</style>
