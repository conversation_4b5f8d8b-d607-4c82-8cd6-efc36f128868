<template>
  <div id="evaluation-template-edit">
    <pro-back-pre :title="title" />
    <div class="ecpage px-[20px] py-[10px]">
      <el-collapse v-model="activeNames" class="detailbody">
        <el-collapse-item title="基本设置" name="1">
          <basic-setting id="basic" ref="basicSettingRef" :basic-echo-data="basicEchoData" @basicCallBack="basicCallBack" />
        </el-collapse-item>
        <el-collapse-item title="评审步骤设置" name="2">
          <review-step id="step" ref="reviewStepRef" :method-name="methodName" :step-echo-data="stepEchoData" :score-compose-param="scoreComposeParam" />
        </el-collapse-item>
        <el-collapse-item title="报价得分" v-if="methodName == 10" name="3">
          <quotation-score id="quotation" ref="quotationRef" :method-name="methodName" :quotation-echo-data="quotationEchoData" :score-compose-param="scoreComposeParam" />
        </el-collapse-item>
        <el-collapse-item title="汇总排名" name="4">
          <summary-sort id="summary" ref="summarySortRef" :method-name="methodName" :summary-echo-data="summaryEchoData" />
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="footer">
      <el-button v-if="effectiveState != 1" mark="takeEffect" type="warning" 
      @click="handlerSave('takeEffect')">生效使用</el-button>
			<el-button mark="save" type="primary"
      @click="handlerSave('confirm')">确认保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive,onMounted, defineAsyncComponent} from 'vue'
import { useMessage } from '/@/hooks/message';
import { getDetail, addEvaMethodTemplate, editEvaMethodTemplate} from '/@/api/template-library/evaluate-method'

const router = useRouter();
const route = useRoute();

const activeNames = ref(['1','2','3','4'])
const basicSettingRef = ref()
const reviewStepRef = ref()
const summarySortRef = ref()
const quotationRef = ref()


const id = ref()
const methodName = ref()
const effectiveState = ref()
const basicEchoData = reactive({})
const summaryEchoData = reactive({})
const stepEchoData = ref()
const quotationEchoData = ref({})
const scoreComposeParam = reactive({}) // 综合评分法-分值权重构成

const title = ref()


const basicSetting = defineAsyncComponent(() => import('./component/basic-setting.vue')); // 基本设置
const quotationScore = defineAsyncComponent(() => import('./component/quotation-score.vue'));
const summarySort = defineAsyncComponent(() => import('./component/summary-sort.vue'));
const reviewStep = defineAsyncComponent(() => import('./component/review-step.vue'));

function basicCallBack ({ field, value }) {
  switch (field) {
    case 'methodName':
      methodName.value = value
      reviewStepRef.value.blankSteps()
      break
    case 'scoreComposeParam':
      Object.assign(scoreComposeParam, value)
  }
}
function getMethoDetail () {
  getDetail(id.value).then(res => {
    methodName.value = res.data.methodName
    
    const { templateName, basicInfo, steps, 
      resultGatherModeParam, scorePrincipleType, scorePrincipleParam } = res.data
    
    Object.assign(basicEchoData, { methodName: methodName.value, templateName, basicInfo, scorePrincipleType, scorePrincipleParam }) // 基本设置数据回显
    Object.assign(summaryEchoData, JSON.parse(resultGatherModeParam)) // 汇总结果排序数据回显
    effectiveState.value = res.data.effectiveState

    if (methodName.value == '10') {
      // 评审步骤数据回显
      const allSteps = JSON.parse(steps)
      if (allSteps.length > 0) {
        let filterQuotationEchoData = allSteps.filter(ele => {return Number(ele.stepType) === 2})?.[0] || {}
        quotationEchoData.value = filterQuotationEchoData
        let filtersStepEchoData = allSteps.filter(ele => { return Number(ele.stepType) !== 2})
        stepEchoData.value = filtersStepEchoData
        nextTick(()=>{
          if (quotationRef.value) {
            quotationRef.value.setData(quotationEchoData.value)
          } else {
            setTimeout(()=>{
              quotationRef.value.setData(quotationEchoData.value)
            },2000)
          }
        })
      }
    } else {
      stepEchoData.value = JSON.parse(steps)
    }
  })
}

async function submit(effectiveState) {
  // 表单验证
  // 基本信息校验
  const basicValid = await basicSettingRef.value.basicValidate()

  // 评审步骤校验
  const stepValid = await reviewStepRef.value.stepValidate()

  // 汇总排序步骤校验
  const summaryValid = await summarySortRef.value.summaryValidate()
  if (!basicValid) {
    document.getElementById('basic').scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
    return
  }
  if (!stepValid) {
    document.getElementById('step').scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
    return
  }
  if (!summaryValid) {
    document.getElementById('summary').scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
    return
  }

  // 基准价+报价得分校验
  if (methodName.value == '10') {
    const quotationValid = quotationRef.value.quotationValidate()
    if (!quotationValid) {
      document.getElementById('quotation').scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      return
    }
  }
  // 数据拼接
  const basicData = basicSettingRef.value.basicDataCb()
 
  const stepData = reviewStepRef.value.stepDataCb()

  if (quotationRef.value) {
    const quotationData = quotationRef.value.quotationDataCb()
    stepData.push(quotationData)
  }

  const summaryData = summarySortRef.value.summaryDataCb()

  // 信息填写完整，保存
  const finalData = Object.assign({}, basicData, {
    id: id.value || '',
    steps: JSON.stringify(stepData),
    resultGatherModeParam: summaryData.resultGatherModeParam, // 汇总结果排序
    state: 0, // 模板状态
    effectiveState // 生效状态
  })

  const api = id.value ? editEvaMethodTemplate : addEvaMethodTemplate
  try {
		await api(finalData)
		router.push({path: '/template-library/evaluation-method/index'})
		useMessage().success('操作成功')
	} catch (err) {
		useMessage().error(err.msg);
	}
  
}


// 确认保存
function handlerSave(mark) {
  switch (mark) {
    case 'confirm':
    {
      const status = effectiveState.value != 1 ? 0 : 1
      submit(status)
      break
    }
    case 'takeEffect':
     submit(1)
      break
  }
}

onMounted(()=>{
  id.value = route.query.id
  
  if (id.value) {
    getMethoDetail()
    title.value = '修改评标办法模板'
  } else {
    title.value = '新增评标办法模板'
  }
})
</script>

<style lang="scss" scoped>
::v-deep .el-input-number .el-input__wrapper{
  padding: 0;
  box-shadow: none;
}
.ecpage {
  padding: 15px;
  border-radius: 8px;
  background-color: #fff;
}
::v-deep .el-collapse {
	border: none;
	padding-bottom: 80px;
}
::v-deep .el-collapse-item__header {
	background-color: #F3F6FF;
	height: 40px;
	padding-left: 19px;
	margin-bottom: 10px;
	font-weight: 500;
}
::v-deep .el-collapse-item__wrap {
	border: none;
}

.co-f-b {
	width: calc(100% - 94px);
	position: fixed;
	bottom: 0;
	margin: 0 -20px;
	padding: 15px 0;
	display: flex;
	justify-content: center;
	-webkit-transition: width 0.28s;
	transition: width 0.28s;
	z-index: 10;
	background-color: #fff;
	-webkit-box-shadow: 0px -5px 10px #f1f1f1;
	box-shadow: 0px -5px 10px #f1f1f1;
}
#evaluation-template-edit{
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  padding: 15px 15px 5px 15px !important;
  .footer{
    width: calc(100% - 200px);
    position: fixed;
    bottom: 0;
    right: 0;
    margin: 0 -20px;
    padding: 15px 0;
    display: flex;
    justify-content: center;
    z-index: 10;
    background-color: #fff;
    box-shadow: 0 -5px 10px #f1f1f1;
  }
}
</style>