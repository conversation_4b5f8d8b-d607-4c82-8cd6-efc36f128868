<template>
	<list :params="params"></list>
</template>
<script setup name="ContractTemplate">
import list from '../components/index.vue';

// 搜索表单
const params = {
	tempType: 4, // 合同模板
	tempDic: 'contracTemp', // 业务类型字典key
	editPath: '/template-library/contract-template/edit',
	searchConfig: {
		items: [
			{
				prop: 'tempName',
				type: 'input',
				attrs: {
					placeholder: '请输入模板名称',
					clearable: true,
					label: '模板名称',
				},
			},
			{
				prop: 'tempBusinessType',
				type: 'select',
				option: 'tempBusinessType',
				attrs: {
					placeholder: '请选择业务类型',
					clearable: true,
					label: '业务类型',
				},
			},
			{
				prop: 'projectCategory',
				type: 'select',
				option: 'projectCategory',
				attrs: {
					placeholder: '请选择项目类别',
					clearable: true,
					label: '项目类别',
				},
			},
		],
	},
	tableHeader: (onSwitch) => {
		return [
			{ type: 'index', label: '序号', width: 60, align: 'center' },
			{ prop: 'tempName', label: '模板名称' },
			{ prop: 'projectCategoryStr', label: '项目类别' },
			{ prop: 'createUserName', label: '创建人' },
			{ prop: 'insertTime', label: '创建时间' },
			{
				prop: 'disabled',
				label: '禁用启用',
				type: 'switch',
				width: 100,
				attrs: {
					'active-value': 0,
					'inactive-value': 1,
					'before-change': onSwitch,
				},
			},
		];
	},
};
</script>
<style></style>
