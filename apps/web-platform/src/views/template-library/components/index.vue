<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-search ref="searchRef" inline label-position="left" :model="searchData" :config="params.searchConfig" :dic="dicData" @change="onChange" @search="onSearchHandle" />
			<co-table ref="dsTableRef" v-loading="handleLoading" :config="tableConfig" :header="tableHeaderNew" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left" />
		</div>
	</div>
</template>
<script setup>
import { getTemplateList, editortem, delTemplate } from '/@/api/template-library/index';
import { dictApi } from '/@/api/dict/index';
import { useHandles } from '/@/hooks/handles';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 接受父组件数据
const props = defineProps({
	params: { type: Object, default: () => {} },
});

// 定义变量内容
var onSearch = null;
const route = useRoute();
const router = useRouter();
const { handleConfirm, handleLoading } = useHandles();

// 搜索表单
const dicData = ref({});
const searchData = ref({});
const tableHeaderNew = props.params.tableHeader(onSwitch);

// 公示模板特殊处理
const projectCategoryA = [{ value: '1', label: '工程项目' }];
const projectCategoryB = [
	{ value: '1', label: '工程项目' },
	{ value: '2', label: '采购项目' },
	{ value: '3', label: '寻源项目' },
];

// 表格配置
const tableConfig = ref({
	dic: {
		tempBusinessType: { value: dictApi[props.params.tempDic] }, // 业务类型
		projectCategory: { data: props.params.tempDic == 6 ? projectCategoryA : projectCategoryB }, // 项目类别字典
		bidModeCode: { data: [] }, // 招标/采购方式
		tenderMode: { value: dictApi['engineering'] }, // 招标方式
		purchaseMode: { value: dictApi['serviceData'] }, // 采购方式
		sourceMode: { value: dictApi['goodsSeeking'] }, // 寻源方式
	},
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getTemplateListApi, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {
			tempType: props.params.tempType, // 业务类型
			tempName: undefined, // 模板名称
			projectCategory: undefined, // 项目类别
			bidModeCode: undefined, // 招标/采购方式
			tenderMode: undefined, // 招标方式
		},
	},
});

// 查询
function onSearchHandle(data, type) {
	if (type === 'reset') {
		dicData.value['bidModeCode'] = [];
	}
	onSearch({ params: searchData.value });
}

// 搜索框更改回调
function onChange(prop, value) {
	if (prop === 'projectCategory') {
		let dicList = [];
		if (value == 1) dicList = dicData.value['tenderMode'];
		if (value == 2) dicList = dicData.value['purchaseMode'];
		if (value == 3) dicList = dicData.value['sourceMode'];
		searchData.value.bidModeCode = undefined;
		dicData.value['bidModeCode'] = dicList;
	}
}

// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}

// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}

// 列表接口
function getTemplateListApi(params) {
	const _params = { ...params };
	searchData.etbTemplateColumnList = undefined;

	// 拼装项目类别和采购方式传输格式
	// 如果选择了项目类别
	const projectCategory = _params.projectCategory;
	if (projectCategory) {
		_params.etbTemplateColumnList = [
			{
				columnName: 'projectCategory',
				columnValue: projectCategory,
			},
		];

		// 如果选择了招标/采购方式 根据项目类别提交不同的key
		const bidModeCode = _params.bidModeCode;
		if (bidModeCode) {
			let columnName = '';
			if (projectCategory == '1') columnName = 'tenderMode'; // 选择了工程项目（招标）
			if (projectCategory == '2') columnName = 'purchaseMode'; // 选择了采购项目
			if (projectCategory == '3') columnName = 'sourceMode'; // 选择了寻源项目

			_params.etbTemplateColumnList.push({
				columnName: columnName,
				columnValue: bidModeCode,
			});
		}
	}

	return getTemplateList(_params).then((res) => {
		// 回显表格里项目类别、招标方式和采购方式字典
		res.data.list.forEach((item) => {
			const etbTemplateColumnList = item.etbTemplateColumnList || [];
			const strList = ['projectCategory', 'tenderMode', 'purchaseMode', 'sourceMode'];
			strList.forEach((str) => {
				const findItem = etbTemplateColumnList.find((itemz) => itemz.columnName === str);
				if (findItem) {
					item[str] = showLabel(findItem.columnValue, dicData.value[str]);
				} else {
					item[str] = '';
				}
			});
			item.projectCategoryStr = item.projectCategory;
			item.bidModeStr = item.tenderMode + ' ' + item.purchaseMode + ' ' + item.sourceMode;

			const findName = etbTemplateColumnList.find((itemz) => itemz.columnName === 'createUserName');
			if (findName) {
				item.createUserName = findName.columnValue;
			}
		});

		return res;
	});
}

// 字典数据回显
function showLabel(arrCode, dicList) {
	const arr = [];
	arrCode = arrCode.split(',');
	arrCode.forEach((item) => {
		const findItem = dicList.find((itemz) => itemz.value === item);
		if (findItem) {
			arr.push(findItem.label);
		}
	});
	return arr.join(',');
}

// switch状态改变
async function onSwitch(row) {
	// 确认框提示
	try {
		await useMessageBox().confirm(row.disabled == 1 ? '确认开启状态？' : '确认关闭状态？');
	} catch {
		return;
	}

	try {
		// 调用开关接口
		await editortem({ id: row.id, disabled: row.disabled == 1 ? 0 : 1 });
		// 开关后提示
		useMessage().success(row.disabled == 1 ? '开启状态成功' : '关闭状态成功');
		return true;
	} catch (err) {
		useMessage().error(err.msg);
		return false;
	}
}

// 监听表格搜索操作
function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	switch (field) {
		case 'add':
			router.push({
				path: props.params.editPath,
			});
			break;
		case 'edit':
			router.push({
				path: props.params.editPath,
				query: {
					id: row.id,
				},
			});
			break;
		case 'remove':
			handleConfirm({
				texts: '是否确认删除？',
				params: { id: row.id },
				request: delTemplate,
				refresh: onSearchHandle,
			});
			break;
	}
}
</script>
<style></style>
