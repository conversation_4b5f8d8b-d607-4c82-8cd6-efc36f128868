<!--编辑模板 中标通知书/结果公告/合同/公示模板 -->
<template>
	<div class="layout-padding">
		<pro-back-pre :title="id ? `修改${paramsData.tempTitle}` : `新增${paramsData.tempTitle}`" />
		<div class="layout-padding-view px-[20px] py-[10px]" v-loading="loading">
			<el-form ref="formRef" :model="form" :rules="rules" inline>
				<!-- 模板基本信息 -->
				<pro-side-title title="模板基本信息"></pro-side-title>
				<!-- 模板名称 -->
				<el-form-item label="模板名称：" prop="tempName">
					<el-input v-model="form.tempName" placeholder="请输入" class="w-[240px]" :maxlength="50" />
				</el-form-item>
				<!-- 业务类型 -->
				<el-form-item v-if="paramsData.tempType != 4" label="业务类型：" prop="tempBusinessType">
					<el-select v-model="form.tempBusinessType" placeholder="请选择" @change="onChange">
						<el-option v-for="item in dictList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<!-- 项目类别 -->
				<el-form-item v-if="paramsData.tempType == 4" label="项目类别：" prop="projectCategory">
					<el-select v-model="form.projectCategory" placeholder="请选择">
						<el-option v-for="item in projectCategoryList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<!-- 项目类别 -->
				<template v-if="paramsData.tempType != 4">
					<div class="label-row">
						<el-form-item label="项目类别：" prop="projectCategory">
							<el-checkbox-group v-model="form.projectCategory">
								<el-checkbox v-for="(item, index) in projectCategoryList" :key="index" :label="item.value" border>
									{{ item.label }}
								</el-checkbox>
							</el-checkbox-group>
						</el-form-item>
					</div>
					<div v-if="form.projectCategory && setTypeShow(1)" class="label-row">
						<el-form-item label="招标方式：" prop="tenderMode">
							<el-checkbox-group v-model="form.tenderMode">
								<el-checkbox v-for="(item, index) in tenderModeList" :key="index" :label="item.value" border>
									{{ item.label }}
								</el-checkbox>
							</el-checkbox-group>
						</el-form-item>
					</div>
					<div v-if="form.projectCategory && setTypeShow(2) && paramsData.tempType != 6" class="label-row">
						<el-form-item label="采购方式：" prop="purchaseMode">
							<el-checkbox-group v-model="form.purchaseMode">
								<el-checkbox v-for="(item, index) in purchaseModeList" :key="index" :label="item.value" border>
									{{ item.label }}
								</el-checkbox>
							</el-checkbox-group>
						</el-form-item>
					</div>
					<div v-if="form.projectCategory && setTypeShow(3) && paramsData.tempType != 6" class="label-row">
						<el-form-item label="寻源方式：" prop="sourceMode">
							<el-checkbox-group v-model="form.sourceMode">
								<el-checkbox v-for="(item, index) in sourceModeList" :key="index" :label="item.value" border>
									{{ item.label }}
								</el-checkbox>
							</el-checkbox-group>
						</el-form-item>
					</div>
				</template>
				<!-- 模板内容 -->
				<pro-side-title title="模板内容"></pro-side-title>
				<div class="ed-body">
					<div class="ed-left">
						<el-form-item label prop="tempContent">
							<pro-editor ref="proEditorRef" v-model="form.tempContent" @blur="onBlur" />
						</el-form-item>
					</div>
					<div class="ed-right">
						<div class="ed-tips">单击变量名称插入到左侧编辑器光标处</div>
						<el-input class="mb-[15px]" v-model="filterText" placeholder="输入变量名称进行过滤" />
						<el-tree
							ref="treeRef"
							class="filter-tree"
							v-loading="treeLoading"
							:data="treeList"
							:props="defaultProps"
							:default-expand-all="false"
							:filter-node-method="filterNode"
							@node-click="nodeClick"
						/>
					</div>
				</div>
				<!-- 提交按钮 -->
				<div class="detail-submit pb-[15px]">
					<el-button @click="cancel">取消</el-button>
					<el-button type="primary" :loading="submitLoading" @click="submit">确认</el-button>
				</div>
			</el-form>
		</div>
	</div>
</template>
<script setup>
import { getFiledList, saveEditortem, editortem, getTemplateDetail } from '/@/api/template-library/index';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';
import { useMessage } from '/@/hooks/message';
import { useDicts } from '/@/hooks/useDicts';
import { useUserInfo } from '/@/stores/userInfo';

// 引入组件
import proEditor from '/@/components/pro-editor/index.vue';

// 接受父组件数据
const props = defineProps({
	paramsData: { type: Object, default: () => {} },
});

const route = useRoute();
const router = useRouter();

// 定义变量内容
const id = ref();
const form = ref({
	projectCategory: props.paramsData.tempType == 4 ? '' : [], // 项目类别 合同是单选
	tenderMode: [], // 招标方式
	purchaseMode: [], // 采购方式
	sourceMode: [], // 寻源方式
	tempContent: '', // 模板内容
});
const loading = ref(false);
const treeLoading = ref(false);
const submitLoading = ref(false);
const userName = useUserInfo().userInfos.name;

// 业务类型字典
const dictList = ref([]);
// 项目类别字典
const projectCategoryList = ref([]);
// 招标方式字典
const tenderModeList = ref([]);
// 采购方式字典
const purchaseModeList = ref([]);
// 寻源方式字典
const sourceModeList = ref([]);

const treeRef = ref();
const treeList = ref([]);
const filterText = ref('');
const defaultProps = ref({
	children: 'children',
	label: (data, node) => {
		return data.fieldName;
	},
});

const rules = ref({
	tempName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
	tempBusinessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
	tempContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
	projectCategory: [{ required: true, message: '请选择项目类别', trigger: 'change' }],
	tenderMode: [{ required: true, message: '请选择招标方式', trigger: 'change' }],
	purchaseMode: [{ required: true, message: '请选择采购方式', trigger: 'change' }],
	sourceMode: [{ required: true, message: '请选择寻源方式', trigger: 'change' }],
});

watch(
	() => filterText.value,
	(val) => {
		treeRef.value.filter(val);
	}
);

onMounted(() => init());

// 页面逻辑初始化
async function init() {
	id.value = route.query.id;

	// 模板详情
	if (id.value) {
		getTemplateDetailApi();
	}

	// tempType 4合同 5中标通知书 6公示模板 8结果公告
	// 业务类型字典
	const resList = await getDicTionaryAndValue(dictApi[props.paramsData.tempDic]);
	dictList.value = resList.data;

	// 添加模板时如果是合同 1.直接获取字段数据集合 2.业务类型不用用户选择，直接隐藏赋值
	if (props.paramsData.tempType == 4 && !id.value) {
		form.value.tempBusinessType = dictList.value[0]?.value || 41;
		getFiledListApi();
	}

	// 项目类别字典
	// 公示模板特殊处理
	if (props.paramsData.tempType == 6) {
		projectCategoryList.value = [{ value: '1', label: '工程项目' }];
		form.value.projectCategory = ['1'];
	} else {
		projectCategoryList.value = [
			{ value: '1', label: '工程项目' },
			{ value: '2', label: '采购项目' },
			{ value: '3', label: '寻源项目' },
		];
	}
	// 招标方式字典
	getDicTionaryAndValue(dictApi.engineering).then((resDic) => {
		tenderModeList.value = resDic.data;
	});
	// 采购方式字典
	getDicTionaryAndValue(dictApi.serviceData).then((resDic) => {
		purchaseModeList.value = resDic.data;
	});
	// 寻源方式字典
	getDicTionaryAndValue(dictApi.goodsSeeking).then((resDic) => {
		sourceModeList.value = resDic.data;
	});
}

// 获取详情
function getTemplateDetailApi() {
	loading.value = true;
	getTemplateDetail(id.value)
		.then((res) => {
			// 处理项目类别 招标方式 采购方式 寻源方式数据
			const etbTemplateColumnList = res.data.etbTemplateColumnList || [];
			if (etbTemplateColumnList) {
				if (props.paramsData.tempType == 4) {
					const findItem = etbTemplateColumnList.find((itemz) => itemz.columnName === 'projectCategory');
					res.data.projectCategory = findItem ? findItem.columnValue : '';
				} else {
					const strList = ['projectCategory', 'tenderMode', 'purchaseMode', 'sourceMode'];
					strList.forEach((item) => {
						const findItem = etbTemplateColumnList.find((itemz) => itemz.columnName === item);
						res.data[item] = findItem ? findItem.columnValue.split(',') : [];
					});
				}
			}

			res.data.tempBusinessType = res.data.tempBusinessType + '';
			form.value = res.data;

			// 获取字段数据集合
			getFiledListApi();
		})
		.catch((err) => {
			useMessage().error(err.msg);
		})
		.finally(() => {
			loading.value = false;
		});
}

// 模板类型改变重新获取字段数据集合
function onChange() {
	getFiledListApi();
}

// 获取字段数据集合
function getFiledListApi() {
	treeLoading.value = true;
	const datas = {
		current: 1,
		size: 200,
		businessType: form.value.tempBusinessType,
		fieldType: props.paramsData.tempType,
	};
	getFiledList(datas)
		.then((res) => {
			treeList.value = res.data.list;
		})
		.catch((err) => {
			useMessage().error(err.msg);
		})
		.finally(() => {
			treeLoading.value = false;
		});
}

// 提交
const formRef = ref();

function submit() {
	formRef.value.validate((valid) => {
		if (valid) {
			const forms = { ...form.value };

			// 合同模板组装etbTemplateColumnList字段
			if (props.paramsData.tempType === 4) {
				forms.etbTemplateColumnList = [
					{
						columnName: 'projectCategory',
						columnValue: forms.projectCategory,
					},
					{
						columnName: 'createUserName',
						columnValue: userName,
					},
				];
			} else {
				// 未选择对应的项目类别 置空对应项
				if (!forms.projectCategory.some((item) => item == 1)) {
					forms.tenderMode = [];
				}
				if (!forms.projectCategory.some((item) => item == 2)) {
					forms.purchaseMode = [];
				}
				if (!forms.projectCategory.some((item) => item == 3)) {
					forms.sourceMode = [];
				}

				// 其他模板组装etbTemplateColumnList字段 -项目类别 招标方式 采购方式 寻源方式数组
				const strList = ['projectCategory', 'tenderMode', 'purchaseMode', 'sourceMode'];
				forms.etbTemplateColumnList = [];
				strList.forEach((item) => {
					let formItem = forms[item];
					if (formItem && formItem.length != 0) {
						formItem = formItem.join(',');
						forms.etbTemplateColumnList.push({
							columnName: item,
							columnValue: formItem,
						});
					}
				});
			}

			// 如果模板内容没有包裹字体 则包裹上 宋体可以解决转换pdf时不识别的问题
			if (forms.tempContent && forms.tempContent.indexOf('custom-template') == -1) {
				forms.tempContent = `<div id="custom-template" style="font-family:'宋体'">${forms.tempContent}</div>`;
			}

			let apiFun;

			if (forms.id) {
				apiFun = editortem(forms);
			} else {
				forms.tempType = props.paramsData.tempType;
				apiFun = saveEditortem(forms);
			}

			submitLoading.value = true;
			apiFun
				.then((res) => {
					useMessage().success('提交成功');
					router.back(-1);
				})
				.catch((err) => {
					useMessage().error(err.msg);
				})
				.finally(() => {
					submitLoading.value = false;
				});
		}
	});
}

// 取消
function cancel() {
	router.back(-1);
}

// 编辑框离焦事件
function onBlur(e) {
	formRef.value?.validateField('tempContent');
}

// 切换项目类型时显示部分选项框
function setTypeShow(value) {
	const findItem = form.value.projectCategory.some((item) => item == value);
	return findItem;
}

// 对树节点进行筛选时执行的方法
function filterNode(value, data) {
	if (!value) return true;
	if (!data.children) {
		return data.fieldName.indexOf(value) !== -1;
	}
}
// 点击字段插入模板内容中
const proEditorRef = ref();

function nodeClick(item, node) {
	const attrObject = {
		field: item.fieldEnName,
		name: item.fieldName,
		type: item.labelType,
	};

	const str = `<span class="zs-custom-template" style="border-bottom: 1px solid #666;" contenteditable="false" data-label-isrenderfrom="${item.isRenderFrom}" data-label-attr="${
		item.tagAttribute
	}" data-label-base-attr='${JSON.stringify(attrObject)}' data-fieldEnName="${item.fieldEnName}">#${item.fieldName}#</span>`;

	proEditorRef.value.insert(str);
}
</script>
<style lang="scss" scoped>
.layout-padding-view {
	overflow-y: auto;
}

.detail-submit {
	display: flex;
	justify-content: center;
}

// 文本编辑器
.ed-body {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-bottom: 20px;
}

.ed-left {
	width: calc(100% - 425px);

	::v-deep .el-form-item {
		display: block;
	}

	::v-deep .el-form-item__content {
		display: block;
	}
}

.ed-right {
	width: 400px;
	height: auto;
	border: 1px solid #dcdfe6;
	padding: 10px;
	margin-left: 15px;
	box-sizing: border-box;
}

.filter-tree {
	height: 450px;
	overflow-y: auto;
}

.ed-tips {
	font-size: 12px;
	color: #999;
	padding: 10px 0;
}

:deep(.el-table__expanded-cell) {
	padding: 0;
}

:deep(.el-collapse-item__content) {
	padding-bottom: 0;
}

.label-row {
	.el-form-item {
		display: flex;

		.el-checkbox-group {
			flex-wrap: wrap;
		}

		.el-checkbox {
			margin-left: 0;
		}
	}

	:deep(.el-form-item__label) {
		flex-shrink: 0;
	}
}
</style>
