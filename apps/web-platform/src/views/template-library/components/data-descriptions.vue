<!--
   name: 描述框
   desData为对象数据 desList为字段列表 dicConfig字典对象

   eg：
   <data-descriptions :des-data="form" :des-list="desList" :dic-config="dicConfig"></data-descriptions>
-->
<template>
	<el-descriptions :column="column" border>
		<el-descriptions-item label-class-name="my-label" content-class-name="my-content" :span="item.span" :label="item.name" label-align="right" v-for="(item, index) in filterLists()" :key="index">
			<template v-if="item.isPre">
				<co-form-link v-if="desData[item.id]" :data="{ src: desData[item.id], name: desData[item.fileName] || '预览下载' }"></co-form-link>
				<span v-else>-</span>
			</template>
			<template v-else-if="item.dicKey">
				{{ showLabel(desData[item.id], dicConfig[item.dicKey]) }}
			</template>
			<template v-else>{{ desData[item.id] }}</template>
		</el-descriptions-item>
	</el-descriptions>
</template>
<script setup>
	// 引入组件
	const coFormLink = defineAsyncComponent(() => import('/@/components/co-form-link/index.vue'));

	// 接受父组件数据
	const props = defineProps({
		desData: {
			type: Object,
			default: () => {},
		},
		desList: {
			type: Array,
			default: () => [],
		},
		column: {
			type: Number,
			default: 2,
		},
		dicConfig: {
			type: Object,
			default: () => {},
		},
	});

	// 字典数据回显
	function showLabel(arrCode, dicList = []) {
		if (!arrCode) return;
		const arr = [];
		dicList = unref(dicList);
		arrCode = Object.prototype.toString.call(arrCode) === '[object Array]' ? arrCode : arrCode.split(',');

		arrCode.forEach((item) => {
			const findItem = dicList.find((itemz) => itemz.value == item);
			if (findItem) {
				arr.push(findItem.label);
			}
		});
		return arr.join(',');
	}

	function filterLists() {
		let desList = props.desList;
		return desList.filter(({ relation }) => !relation || props.desData[relation.oid] == relation.val);
	}

	function getText(val) {
		let text = Object.prototype.toString.call(val) === '[object Object]' ? val.text : val;
		return text || '-';
	}
</script>
<style lang="scss" scoped>
	:deep(.my-label) {
		width: 200px;
	}

	:deep(.el-descriptions__content) {
		min-width: 300px;
	}
</style>
