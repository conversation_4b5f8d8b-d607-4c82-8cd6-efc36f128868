<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view btn-align-start">
			<co-search ref="searchRef" inline label-position="left" :model="searchData" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table
				ref="dsTableRef"
				:config="tableConfig"
				:header="tableHeader"
				single-mode="icon-hook"
				@dicLoaded="onDicLoaded"
				@loaded="onTableLoad"
				@operation="onOperation"
				v-loading="handleLoading"
				align="left"
			/>
		</div>
	</div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { getList, del, changeState } from '/@/api/template-library/review-report.js';
import { dic, searchConfig, formatData } from './data';
import { useMessageBox, useMessage } from '/@/hooks/message';
import { useHandles } from '/@/hooks/handles';
const { handleConfirm, handleLoading } = useHandles();
const tableConfig = {
	dic,
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {},
		formatData: formatData,
	},
};
// 搜索表单
const dicData = ref({});
const searchData = ref({});
var onSearch = null;
const tableHeader = [
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ prop: 'tname', label: '评审报告模板名称' },
	{ prop: 'evalVal', label: '评标办法' },
	{
		prop: 'state',
		label: '文件状态',
		type: 'switch',
		align: 'center',
		width: 110,
		attrs: {
			'before-change': (row) => {
				return handleStatusChange(row);
			},
		},
	},
];
// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}
const onSearchHandle = (data = {}) => {
	onSearch({ params: data });
};
// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}
const router = useRouter();
// 操作事件类型
const optType = {
	add: () => {
		router.push({
			path: '/template-library/review-report-template/add',
			query: {},
		});
	},
	toView: (data) => {
		router.push({
			path: '/template-library/review-report-template/add',
			query: { id: data.id },
		});
	},
	edit: (data) => {
		router.push({
			path: '/template-library/review-report-template/add',
			query: { id: data.id, type: 1 },
		});
	},
	switch: () => {},
	remove: (data) => {
		// useMessageBox()
		// 	.confirm('确定删除吗？')
		// 	.then(() => {
		// 		del({ id: data.id }).then((res) => {
		// 			onSearchHandle();
		// 		});
		// 	});
		handleConfirm({
			params: { id: data.id },
			refresh: onSearchHandle,
			request: del,
			texts: '确定删除吗？',
		});
	},
};
// 操作事件
const onOperation = (obj) => {
	if (obj.field in optType) {
		return optType[obj.field](obj.row);
	} else {
		return optType[obj.type](obj.row);
	}
};
//判断是否修改状态
const handleStatusChange = async (row) => {
	const text = !row.state ? '确认要改为开启状态吗？' : '确认要改为关闭状态吗？';
	try {
		const confirmRes = await useMessageBox().confirm(text);
		if (!confirmRes) return false;
		const { code, msg } = await changeState({ id: row.id, state: row.state == 1 ? 0 : 1 }).catch((err) => err);
		if (code === 200) {
			useMessage().success(msg);
			onSearchHandle();
		} else {
			useMessage().warning(msg);
		}
		return code === 200;
	} catch (error) {
		return false;
	}
};
</script>

<style>
.btn-align-start .el-form--inline {
	display: flex;
	align-items: flex-start;
}
</style>
