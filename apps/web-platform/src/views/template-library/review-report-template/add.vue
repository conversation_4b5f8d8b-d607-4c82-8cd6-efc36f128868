<template>
	<div class="layout-padding">
		<pro-back-pre :title="title" />
		<div class="layout-padding-auto layout-padding-view scroll" v-if="typeCase == '新增' || typeCase == '编辑'">
			<el-form class="mt12" ref="formModelRef" :model="formModel" label-width="120px" :rules="formRules">
				<el-row>
					<el-col :span="12" class="col-mb-20">
						<el-form-item label="模板名称:" prop="tname">
							<el-input v-model="formModel.tname" placeholder="请输入模板名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="col-mb-20">
						<el-form-item label="评标办法:" prop="evalVal" v-if="methodNameList">
							<el-select v-model="formModel.evalVal" multiple placeholder="请选择评标办法" style="width: 100%">
								<el-option v-for="item in methodNameList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="col-mb-20">
						<el-form-item label="模板类型:" prop="fileKey" v-if="modelTypeList">
							<el-select :disabled="typeCase == '编辑' ? true : false" v-model.number="formModel.fileKey" placeholder="请选择模板类型" style="width: 100%">
								<el-option v-for="item in modelTypeList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24" class="col-mb-20">
						<el-form-item label="附件:" prop="files">
							<div class="width-100">
								<el-button class="mb-3" :type="currentBtnKey == item.dicValue ? 'primary' : 'default'" v-for="item in fileTypeList" :key="item.dicValue" @click="handleBtnClick(item.dicValue)">{{
									item.dicName
								}}</el-button>
								<div v-for="item in fileTypeList" :key="item.dicName">
									<upl
										v-if="currentBtnKey == item.dicValue"
										:hasMultiple="false"
										:isShowFileList="true"
										:isHiddenTable="false"
										:isOperation="true"
										:maxFiles="1"
										:file-ids="fIds"
										:autoUpload="true"
										accept=".docx,.txt,.xlsx"
										:oper="operBtn()"
										@breakUpload="breakUpload"
									></upl>
								</div>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div class="button-group justify-center" v-if="typeCase == '新增' || typeCase == '编辑'">
				<el-button @click="returnPage" pain>取消</el-button>
				<el-button @click="onSubmit" :loading="submitLoading" type="primary">提交</el-button>
			</div>
		</div>

		<div v-if="typeCase == '查看'">
			<div class="layout-padding-auto layout-padding-view px-[20px] py-[10px]">
				<div class="px-[20px] py-[20px]">
					<span class="label-width text-font">模板名称：</span><span class="px-[10px] text-font-sub">{{ formModel.tname || '' }}</span>
				</div>
				<div class="px-[20px] py-[20px]">
					<span class="label-width text-font">评标办法：</span><span class="px-[10px] text-font-sub">{{ getLabel(formModel.evalVal, methodNameList) || '' }}</span>
				</div>
				<div class="px-[20px] py-[20px]">
					<span class="label-width text-font">模板类型：</span><span class="px-[10px] text-font-sub">{{ getLabel(formModel.fileKey, modelTypeList) || '' }}</span>
				</div>
				<div class="px-[20px] py-[20px] flex">
					<span class="label-width text-font">附件：</span
					><span class="px-[10px] text-font-sub flex-1">
						<el-button class="mb-3" :type="currentBtnKey == item.dicValue ? 'primary' : 'default'" v-for="item in fileTypeList" :key="item.dicValue" @click="handleBtnClick(item.dicValue)">{{
							item.dicName
						}}</el-button>
						<div v-for="item in fileTypeList" :key="item.dicName">
							<upl
								:class="{ noId: !fIds }"
								v-if="currentBtnKey == item.dicValue"
								:hasMultiple="false"
								:isShowFileList="true"
								:isHiddenTable="false"
								:isOperation="true"
								:maxFiles="1"
								:file-ids="fIds"
								:autoUpload="true"
								:oper="operBtn()"
								@breakUpload="breakUpload"
							></upl>
						</div>
					</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { save, edit, getDetail } from '/@/api/template-library/review-report.js';
import { formRules, fileTypeList, filesType } from './data';

import { useRouter } from 'vue-router';
import { useMessage, useLoading } from '/@/hooks/message';
import { useDicts } from '/@/hooks/useDicts';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';

import { dictApi } from '/@/api/dict/index';

const upl = defineAsyncComponent(() => import('/@/components/Upload/BigFile.vue'));
const router = useRouter();
const loading = useLoading();
const _id = router.currentRoute.value.query.id || '';
const _type = router.currentRoute.value.query?.type || '';
const title = ref('评审报告');
const typeCase = computed(() => {
	if (_id && _type) {
		title.value = '修改评审报告模板';
		return '编辑';
	}
	if (_id && !_type) {
		title.value = '评审报告模板详情';
		return '查看';
	}
	if (!_id && !_type) {
		title.value = '新增评审报告模板';
		return '新增';
	}
	return null;
});
// 表格操作按钮显示
const operBtn = () => {
	return _id && !_type ? { remove: false, preview: true } : { remove: true, preview: true, download: true };
};
const returnPage = () => {
	router.back();
};

//获取详情
const getDetailInfo = async (id) => {
	loading.service('加载中......');
	try {
		let res = await getDetail(id);
		formModel.value = res.data;
		formModel.value.evalVal = res.data.evalVal.split(',');

		fileList.value = res.data.list;
		fIds.value = res.data.list[0].fileId || '';
	} catch (error) {
		useMessage().error(error.msg);
	}

	loading.close();
};
//评标办法下拉
const methodNameList = useDicts('methodName').methodName;
// 模板类型下拉
const modelTypeList = ref([]);
onMounted(async () => {
	let res1 = await getDicTionaryAndValue(dictApi['templateReporttype']);
	modelTypeList.value = res1.data;
	if (_id != '') {
		getDetailInfo(_id);
	}
});
const getLabel = (val, list) => {
	if (Array.isArray(val)) {
		return val.map((item) => list.find((item2) => item2.value == item)?.label).join('，');
	} else {
		return list.find((item) => item.value == val)?.label;
	}
};
const formModelRef = ref(); // 表单对象
const fIds = ref(); // 文件ids
const formModel = ref({
	// 表单初始数据
	tname: '',
	evalVal: [],
	fileKey: '',
	list: [],
});

// 文件类型按钮点击事件--获取对应文件id
const getCurId = (val) => {
	loading.service('加载中......');

	if (val == 'A') {
		if (fileList.value[0]) {
			fIds.value = fileList.value[0].fileId;
		}
	}
	if (val == 'B') {
		if (fileList.value[1]) {
			fIds.value = fileList.value[1].fileId;
		}
	}
	if (val == 'C') {
		if (fileList.value[2]) {
			fIds.value = fileList.value[2].fileId;
		}
	}
	loading.close();
};

const currentBtnKey = ref('A'); // 选择类型

const fileId = ref(''); // 文件id
// 文件列表默认数据
const fileList = ref([...filesType]);
//切换文件类型按钮点击事件
const handleBtnClick = (val) => {
	currentBtnKey.value = val;
	getCurId(val);
};
//赋值到对应表格
const setFileList = (data) => {
	if (currentBtnKey.value == 'A') {
		//评审报告
		Object.assign(fileList.value[0], {
			fileId: data[0].id,
			fileType: data[0].fileName.split('.').pop(),
		});
	}
	if (currentBtnKey.value == 'B') {
		//符合性评审表
		Object.assign(fileList.value[1], {
			fileId: data[0].id,
			fileType: data[0].fileName.split('.').pop(),
		});
	}
	if (currentBtnKey.value == 'C') {
		//打分性评审表
		Object.assign(fileList.value[2], {
			fileId: data[0].id,
			fileType: data[0].fileName.split('.').pop(),
		});
	}
};
// 清空指定值 的数组 文件id 把这个值清掉
const clearListVal = (id) => {
	let temp = fileList.value;
	let newTemp = temp.map((obj) => ({ ...obj, fileId: obj.fileId == id ? null : obj.fileId, fileType: null }));
	fileList.value = newTemp;
};

const breakUpload = (data) => {
	if (data.field !== 'upload' && data.field !== 'remove') return;
	if (data.field === 'remove') {
		let id = data.row.id; // 删除的文件
		clearListVal(id);
	} else {
		fileId.value = data.row[0].id;
		setFileList(data.row);
	}
};
const submitLoading = ref(false); // 提交按钮loading
//新增-保存
const onSubmit = async () => {
	let list = fileList.value;

	if (!list[0].fileId && !list[1].fileId && !list[2].fileId) {
		useMessage().error('请至少上传一个附件');
		return;
	}
	formModelRef.value.validate((valid) => {
		if (valid) {
			// evalVal 转成字符串，
			let evalVal = formModel.value.evalVal.toString();
			let list = fileList.value;

			let data = {};
			Object.assign(data, formModel.value, { list, evalVal });

			submitLoading.value = true;
			if (_id) {
				edit(data)
					.then((res) => {
						submitLoading.value = false;
						useMessage().success('修改成功');
						returnPage();
					})
					.catch((err) => {
						useMessage().error(err.msg);
					})
					.finally(() => {
						submitLoading.value = false;
					});
			} else {
				save(data)
					.then((res) => {
						useMessage().success('提交成功');
						returnPage();
					})
					.catch((err) => {
						useMessage().error(err.msg);
					})
					.finally(() => {
						submitLoading.value = false;
					});
			}
		}
	});
};
</script>

<style scoped>
.justify-center {
	display: flex;
	justify-content: center;
	padding-top: 30px;
}
.col-mb-20 {
	margin-bottom: 20px;
}
.label-width {
	width: 100px;
	display: inline-block;
	text-align: right;
}
.text-font {
	font-size: 14px;
	color: #606266;
	font-weight: 600;
}
.text-font-sub {
	font-size: 14px;
	color: #212529;
}
.flex {
	display: flex;
}
.flex-1 {
	flex: 1;
}
.width-100 {
	width: 100%;
}
</style>
<style>
.noId .simple-upload-container .total-progress {
	display: none;
}
</style>
