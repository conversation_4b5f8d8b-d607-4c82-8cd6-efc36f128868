import { dictApi } from '/@/api/dict/index';
import { useDicts } from '/@/hooks/useDicts';
// 字典
export const dic = {
	// 评标办法
	evalValSearch: {
		value: dictApi.methodName,
	},
};
export const searchConfig = {
	styles: { background: '#ffffff' },
	items: [
		{
			prop: 'TName',
			type: 'input',
			attrs: {
				placeholder: '请输入模板名称',
				clearable: true,
				label: '模板名称',
			},
		},

		{
			prop: 'evalVal',
			type: 'select',
			option: 'evalValSearch',
			attrs: {
				placeholder: '请选择评标办法',
				clearable: true,
				label: '评标办法',
			},
		},
	],
};
//表单配置
export const formList = [
	{
		name: '模板名称',
		id: 'tname',
		attrs: {
			placeholder: '请输入模板名称',
			clearable: true,
		},
		validate: 'required',
		required: true,
	},
	{
		name: '评标办法',
		id: 'evalVal',
		type: 'select',
		dicKey: 'methodName',
		multiple: true,
		attrs: {
			placeholder: '请选择评标办法',
			clearable: true,
		},
		validate: 'required',
		required: true,
	},
	{
		name: '模板类型',
		id: 'fileKey',
		type: 'select',
		dicKey: 'templateReporttype',
		attrs: {
			placeholder: '请选择模板类型',
			clearable: true,
		},
		validate: 'required',
		required: true,
	},
];
// 表单字典
export const dictConfig = {
	...useDicts('methodName', 'templateReporttype'),
};

export const formRules = {
	tname: [{ required: true, message: '请输入模板名称', trigger: ['blur', 'change'] }],
	evalVal: [{ required: true, message: '请选择评标办法', trigger: ['blur', 'change'] }],
	fileKey: [{ required: true, message: '请选择模板类型', trigger: ['blur', 'change'] }],
};
// 按钮
export const fileTypeList = [
	{
		label: '评审报告',
		value: 'A',
		dicName: '评审报告',
		dicValue: 'A',
	},
	{
		label: '符合性评审表',
		value: 'B',
		dicName: '符合性评审表',
		dicValue: 'B',
	},
	{
		label: '打分性评审表',
		value: 'C',
		dicName: '打分性评审表',
		dicValue: 'C',
	},
];
export const formatData = (data) => {
	data.forEach((v) => {
		v.state = v.state == 1;
		v.evalVal = v.evalVal == 10 ? '综合评标法' : v.evalVal == 11 ? '最低评标价法' : v.evalVal.indexOf(',') ? '综合评标法、最低评标价法' : '-';
	});
	return data;
};
export const filesType = [
	{
		dicId: 'D6251466196775788545',
		dicName: '评审报告',
		dicShowCtx: '评审报告',
		dicTypeId: 'D1036297760532238337',
		dicValue: 'A',
		grade: 1,
		idPath: 'D6251466196775788545',
		sort: 1,
		templateType: 'A',
		fileId: '',
		fileType: '',
	},
	{
		dicId: 'D6251466196775788546',
		dicName: '符合性评审表',
		dicShowCtx: '符合性评审表',
		dicTypeId: 'D1036297760532238337',
		dicValue: 'B',
		grade: 1,
		idPath: 'D6251466196775788546',
		sort: 1,
		templateType: 'B',
		fileType: '',
	},
	{
		dicId: 'D6251466196775788547',
		dicName: '打分性评审表',
		dicShowCtx: '打分性评审表',
		dicTypeId: 'D1036297760532238337',
		dicValue: 'C',
		grade: 1,
		idPath: 'D6251466196775788547',
		sort: 1,
		templateType: 'C',
		fileType: '',
	},
];
