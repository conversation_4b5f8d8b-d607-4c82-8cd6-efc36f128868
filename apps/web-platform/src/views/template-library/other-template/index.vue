<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view btn-align-center scorll">
			<co-search ref="searchRef" inline label-position="left" :config="searchConfig" :dictConfig="dictConfig" @search="onSearchHandle" />

			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" :data="tableConfig.dataList" single-mode="icon-hook" @loaded="onTableLoad" @operation="onOperation" align="left">
			</co-table>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { getList } from '/@/api/template-library/other-template.js';
import type { itemType } from '../doc-template/type';
import { searchConfig, tableHeader, dictConfig } from './data';

const dsTableRef = ref();

const tableConfig = ref({
	operation: {
		fixed: 'right',
		width: 180,
		list: [],
	},
	paging: {
		current: 1,
		size: 10,
		// total: 0,
	},
	dataList: [],
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: { classificationCode: '000001-000002' },
	},
});
var onSearch = null;
// 查询
const onSearchHandle = (data: any) => {
	onSearch({ params: data });
};

let formData = reactive({ id: null });
// table加载完成回调
function onTableLoad({ getDataList }: any) {
	onSearch = getDataList;
}
const router = useRouter();
const optType = {
	add: () => {
		router.push('/template-library/other-template/add');
	},
	toView: (data: { id: string }) => {
		router.push({
			path: '/template-library/other-template/add',
			query: { id: data.id },
		});
	},
	edit: (data: { id: string }) => {
		router.push({
			path: '/template-library/other-template/add',
			query: { id: data.id, type: 1 },
		});
	},
};

const onOperation = (obj: itemType) => {
	if (obj.field in optType) {
		return optType[obj.field](obj.row);
	} else {
		return optType[obj.type](obj.row);
	}
};
</script>
<style>
.scorll {
	overflow: scroll;
}
.btn-align-center > .ds-search-container > .el-form--inline {
	align-items: baseline;
	display: flex;
}
</style>
