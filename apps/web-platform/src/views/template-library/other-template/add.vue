<template>
	<div class="layout-padding w100">
		<template v-if="typeCase == '新增' || typeCase == '修改'">
			<pro-back-pre :title="title" />
			<div class="layout-padding-auto layout-padding-view">
				<co-form ref="coFormRef" class="form-inline-half mt15" :form-list="formList" :form-data="formData"> </co-form>

				<el-form ref="fileForm">
					<el-form-item label="附件：" v-if="!(fIds == '' && typeCase == '查看')" label-width="145px" prop="fileRecords">
						<upl
							:hasMultiple="false"
							:isShowFileList="true"
							:isHiddenTable="false"
							:isOperation="true"
							:maxFiles="1"
							:file-ids="fIds"
							accept=".docx,.txt"
							:autoUpload="true"
							:oper="operBtn()"
							@breakUpload="breakUpload"
						>
						</upl>
					</el-form-item>
				</el-form>
				<div class="button-group justify-center" v-if="typeCase == '新增' || typeCase == '修改'">
					<el-button @click="returnPage" pain>取消</el-button>
					<el-button @click="onSubmit" type="primary">确认</el-button>
				</div>
			</div>
		</template>
		<template v-else>
			<pro-back-pre title="模板列表详情" />
			<div>
				<div class="layout-padding-auto layout-padding-view px-[20px] py-[10px]">
					<pro-side-title title="模板信息"></pro-side-title>
					<div class="px-[20px] py-[20px]">
						<span class="label-width text-font">模板名称：</span><span class="px-[10px] text-font-sub">{{ formData.title || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px]">
						<span class="label-width text-font">业务key：</span><span class="px-[10px] text-font-sub">{{ formData.abstractContent || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px] flex">
						<span class="label-width text-font">附件：</span>
						<span class="px-[10px] text-font-sub flex-1">
							<upl
								:class="{ noId: !fIds }"
								:hasMultiple="false"
								:isShowFileList="true"
								:isHiddenTable="false"
								:isOperation="true"
								:maxFiles="1"
								:file-ids="fIds"
								accept=".doc,.docx,.txt"
								:autoUpload="true"
								:oper="operBtn()"
								@breakUpload="breakUpload"
							>
							</upl>
						</span>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { Addrange, detailsId, updateList } from '/@/api/template-library/knowledge.js';
import { formList, getCurTime } from './data-add';
import upl from '/@/components/Upload/BigFile.vue';
import { useMessage, useLoading } from '/@/hooks/message';
import { LocationQueryValue, useRouter } from 'vue-router';
import { useUserInfo } from '/@/stores/userInfo';
const router = useRouter();
const loading = useLoading();

const returnPage = () => {
	router.back();
};

const _id = router.currentRoute.value.query.id || '';
const _type = router.currentRoute.value.query?.type || null;
const coFormRef = ref();
const formData = ref({});
const title = ref('模板列表详情');
const typeCase = computed(() => {
	if (_id && _type) {
		title.value = '修改模板列表';
		return '修改';
	}
	if (_id && !_type) {
		title.value = '模板列表详情';
		return '查看';
	}
	if (!_id && !_type) {
		title.value = '新增模板列表';
		return '新增';
	}
	return null;
});
const operBtn = () => {
	return _id && !_type ? { remove: false, preview: true } : { remove: true, preview: true };
};
const fileForm = ref();
const fIds = ref(''); // 文件ids
//获取详情
const getDetail = async (id: string | LocationQueryValue[]) => {
	let res = await detailsId(id);
	formData.value = res.data;
	fIds.value = res.data.fileIds;
	// getFileList(res.data.fileIds);
	// console.log('详情', formData.value);
};

if (_id != '') {
	getDetail(_id);
}

// const fileObj = reactive({
// 	// 文件对象
// 	templateCode: 111,
// 	fileSize: 0,
// 	fileType: '',
// 	fileids: '',
// 	lastUpdateTime: '',
// });
// const fileRecords = ref([]);

const breakUpload = ({ fsId, field, row }) => {
	// console.log('fsId, field, row', fsId, field, row);
	if (field === 'remove') {
		fIds.value = '';
	}
	if (field === 'upload') {
		fIds.value = fsId;
	}
};

const userInfo = useUserInfo();
//新增-保存
const onSubmit = async () => {
	const rest = await coFormRef.value.getFormData();
	let temp = {
		id: rest.id || _id || null,
		releaseTime: getCurTime(),
		abstractContent: rest.abstractContent,
		classificationCode: '000001-000002', // 默认
		effectiveType: '0', //effectiveType 有效时间类别 0 永久 1 自定义过期时间
		fileIds: fIds.value,
		publisherName: userInfo.userInfos.name,
		title: rest.title,
	};
	loading.service('保存中......');
	// console.log('提交数据--》', temp);
	if (_id) {
		updateList(temp)
			.then((res) => {
				useMessage().success('提交成功');
				returnPage();
			})
			.catch((err) => {
				useMessage().error(err.msg);
			})
			.finally(() => {
				loading.close();
			});
	} else {
		Addrange(temp)
			.then((res) => {
				useMessage().success('提交成功');
				returnPage();
			})
			.catch((err) => {
				useMessage().error(err.msg);
			})
			.finally(() => {
				loading.close();
			});
	}
};
</script>

<style lang="scss">
.justify-center {
	display: flex;
	justify-content: center;
	padding-top: 30px;
}
.form-inline-half {
	display: flex;

	.el-form {
		width: 100%;
		display: flex;
		.el-form-item--default {
			width: 50%;
		}
	}
}
.label-width {
	width: 100px;
	display: inline-block;
	text-align: right;
}
.text-font {
	font-size: 14px;
	color: #606266;
	font-weight: 600;
}
.text-font-sub {
	font-size: 14px;
	color: #212529;
}
.flex {
	display: flex;
	.coUpload {
		margin-top: -10px;
	}
}
.flex-1 {
	flex: 1;
}
</style>
<style>
.noId .simple-upload-container .total-progress {
	display: none;
}
</style>
