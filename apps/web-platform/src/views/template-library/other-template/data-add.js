import { useRouter } from 'vue-router';
import { computed } from 'vue';
export const headerCellStyle = {
	backgroundColor: '#edf1fd',
	color: '#333',
	width: '1000px',
	marginTop: '20px',
};
export const formList = [
	{
		id: 'title',
		name: '模板名称',
		field: 'title',
		type: 'input',
		attrs: {
			placeholder: '请输入模板名称',
			clearable: true,
		},
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'abstractContent',
		name: '业务key',
		field: 'abstractContent',

		validate: 'required',
		required: true,
		disabled: false,
	},
];
export const createFileId = (data) => {
	let ids = data.map((item) => {
		return item.id;
	});
	return ids.join(',');
};

// 获取当前时间
export const getCurTime = () => {
	const currentTime = new Date();
	const year = currentTime.getFullYear();
	const month = ('0' + (currentTime.getMonth() + 1)).slice(-2);
	const day = ('0' + currentTime.getDate()).slice(-2);
	const hours = ('0' + currentTime.getHours()).slice(-2);
	const minutes = ('0' + currentTime.getMinutes()).slice(-2);
	const seconds = ('0' + currentTime.getSeconds()).slice(-2);
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
