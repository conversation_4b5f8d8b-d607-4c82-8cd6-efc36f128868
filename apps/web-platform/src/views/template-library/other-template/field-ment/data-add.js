export const formList = [
	{
		id: 'fieldType',
		name: '模板类型',
		field: 'fieldType',
		type: 'select',
		attrs: {
			placeholder: '请选择模板类型',
			clearable: true,
		},
		validate: 'required',
		required: true,
		list: 'templateReporttype',
	},
	{
		id: 'businessType',
		name: '业务类型',
		type: 'select',
		field: 'businessType',
		validate: 'required',
		required: false,
	},
	{
		id: 'fieldName',
		name: '字段文本名称',
		field: 'fieldName',
		validate: 'required',
		required: true,
	},
	{
		id: 'fieldEnName',
		name: '字段值',
		field: 'fieldEnName',
		validate: 'required',
		required: true,
	},
	{
		id: 'labelType',
		name: '字段标签类型',
		type: 'select',
		field: 'labelType',
		validate: 'required',
		required: true,
	},
	{
		id: 'isRenderFrom',
		name: '是否渲染表单',
		type: 'radio',
		field: 'isRenderFrom',
		list: [
			{ id: true, name: '是' },
			{ id: false, name: '否' },
		],
		validate: 'required',
		required: false,
	},
];
export const formRules = {
	fieldType: [{ required: true, message: '请选择模板类型', trigger: ['blur', 'change'] }],
	fieldName: [{ required: true, message: '字段文本名称不能为空', trigger: 'blur' }],
	fieldVal: [{ required: true, message: 'HTML标签不能为空', trigger: 'blur' }],
	fieldVariableVal: [{ required: true, message: '字段替换变量值不能为空', trigger: 'blur' }],
	labelType: [{ required: true, message: '字段类型不能为空', trigger: 'change' }],
	fieldEnName: [{ required: true, message: '字段值不能为空', trigger: 'blur' }],
};
