import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useDicts } from '/@/hooks/useDicts';
import { dictApi } from '/@/api/dict/index';
export const previewUrl = ref('');
//模板类型  templateReporttype
// 业务类型 templateBusinessType
// 搜索表单字典
export const dictConfig = {
	...useDicts('templateReporttype', 'templateBusinessType'),
};
// 搜索表单配置
export const searchConfig = {
	styles: { display: 'flex' },
	inline: 'true',
	size: 'mini',
	labelPosition: 'left',
	items: [
		{
			prop: 'fieldType',
			type: 'select',
			option: 'templateReporttype',
			attrs: {
				placeholder: '请选择模板类型',
				clearable: true,
				label: '模板类型',
			},
		},
		{
			prop: 'businessType',
			type: 'select',
			option: 'businessType',
			attrs: {
				placeholder: '请选择业务类型',
				clearable: true,
				label: '业务类型',
			},
		},
	],
};
//模板类型
// templateReporttype: 'D1198427626923073537',
// 业务类型
// templateBusinessType: 'D7665591490694209537',
export const dic = {
	//模板类型
	templateReporttype: { value: dictApi['templateBusinessType'] },
	fieldType: { value: dictApi['templateBusinessType'] },
	templateBusinessType: { value: dictApi['templateReporttype'] },

	contracTemp: { value: dictApi['contracTemp'] },
	notificationTemp: { value: dictApi['notificationTemp'] },
	noticeTemp: { value: dictApi['noticeTemp'] },
	tenderDocumentTemp: { value: dictApi['tenderDocumentTemp'] },
	announcementTemp: { value: dictApi['announcementTemp'] },
	otherTemp: { value: dictApi['otherTemp'] },
};

// table表格
export const tableHeader = [
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ prop: 'fieldName', label: '字段文本名称', width: 250 },
	{ prop: 'fieldEnName', label: '字段值' },
	{ prop: 'labelType', label: ' 字段标签类型' },
	{ prop: 'fieldType', label: '模板类型' },
	{ prop: 'businessType', label: '业务类型' },
];

export const formList = [
	{
		id: 'templateName',
		name: '模板名称',
		type: 'input',
		attrs: {
			placeholder: '请输入模板名称',
			clearable: true,
		},
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'projectType',
		name: '项目类型',
		field: 'projectType',
		type: 'select',
		dicKey: 'projectType',
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'templateType',
		name: '文件类型',
		field: 'templateType',
		type: 'select',
		dicKey: 'templateType',
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'templateDescription',
		name: '模板说明',
		type: 'textarea',
		attrs: {
			placeholder: '请输入模板说明',
			clearable: true,
		},
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'izEnable',
		name: '模板状态',
		field: 'izEnable',
		type: 'switch',
		option: [
			{ activeValue: true, activeText: '是' },
			{ inactiveValue: false, inactiveText: '否' },
		],
		validate: 'required',
		required: false,
		disabled: false,
	},
];

export const rule = reactive({
	fileRecords: [{ required: true, message: '请选择附件', trigger: 'change' }],
});

const router = useRouter();
export const refreshPage = () => {
	//  const currentRoute = router.currentRoute.value;
	//  router.replace(currentRoute);
	window.location.reload();
};

export const inputType = {
	input: '输入框',
	textarea: '文本框',
	select: '选择框',
	radio: '单选按钮',
	checkbox: '多选按钮',
	datepicker: '时间选择器',
	cascader: '级联选择器',
	upload: '上传',
	customFormItem: '自定义',
};

// 字段类型下拉：
export const fieldType = [
	{ value: 'input', label: '输入框' },
	{ value: 'select', label: '选择框' },
	{ value: 'radio', label: '单选按钮' },
	{ value: 'checkbox', label: '多选按钮' },
	{ value: 'datepicker', label: '时间选择器' },
	{ value: 'cascader', label: '级联选择器' },
	{ value: 'upload', label: '上传' },
	{ value: 'customFormItem', label: '自定义' },
];
