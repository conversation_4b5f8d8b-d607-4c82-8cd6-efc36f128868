<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view btn-align-center btn-align-start scorll">
			<co-search ref="searchRef" inline label-position="left" :config="searchConfig" :model="searchData" :dic="dicData" @change="searchItemChange" @search="onSearchHandle" />

			<co-table
				ref="dsTableRef"
				:config="tableConfig"
				:header="tableHeader"
				:data="tableConfig.dataList"
				single-mode="icon-hook"
				@loaded="onTableLoad"
				@operation="onOperation"
				@dicLoaded="onDicLoaded"
				align="left"
			>
				<template #businessType="{ row }">
					{{ getTransName(row) }}
				</template>
			</co-table>
		</div>
	</div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { dictApi } from '/@/api/dict/index';
import { getList, tempTypeDel } from '/@/api/template-library/template-fields';

import { searchConfig, tableHeader, dic, inputType } from './data';
import { useMessageBox } from '/@/hooks/message';
import { useHandles } from '/@/hooks/handles';
const { handleConfirm, handleLoading } = useHandles();
const dicData = ref({});
const searchData = ref({});

const allTypeObj = ref({});
const allTypeArr = ref([]);
//合并所有业务类型
const mergeAllType = () => {
	allTypeObj.value.contracTemp = dicData.value['contracTemp'];
	allTypeObj.value.notificationTemp = dicData.value['notificationTemp'] || [];
	allTypeObj.value.noticeTemp = dicData.value['noticeTemp'] || [];
	allTypeObj.value.tenderDocumentTemp = dicData.value['tenderDocumentTemp'] || [];
	allTypeObj.value.announcementTemp = dicData.value['announcementTemp'] || [];
	allTypeObj.value.otherTemp = dicData.value['otherTemp'] || [];
	allTypeArr.value = [
		...allTypeObj.value.contracTemp,
		...allTypeObj.value.notificationTemp,
		...allTypeObj.value.noticeTemp,
		...allTypeObj.value.tenderDocumentTemp,
		...allTypeObj.value.announcementTemp,
		...allTypeObj.value.otherTemp,
	];
};

const searchItemChange = (prop, val) => {
	//      4: 'contracTemp', // 合同
	// 			5: 'notificationTemp', // 通知
	// 			6: 'noticeTemp', // 公示
	// 			7: 'tenderDocumentTemp', // 招采文件
	// 			8: 'announcementTemp', // 公告
	// 			9: 'otherTemp'// 其他

	if (prop == 'fieldType') {
		let dicList = [];
		if (val == 4) {
			dicList = dicData.value['contracTemp'];
		}
		if (val == 5) {
			dicList = dicData.value['notificationTemp'];
		}
		if (val == 6) {
			dicList = dicData.value['noticeTemp'];
		}
		if (val == 7) {
			dicList = dicData.value['tenderDocumentTemp'];
		}
		if (val == 8) {
			dicList = dicData.value['announcementTemp'];
		}
		if (val == 9) {
			dicList = dicData.value['otherTemp'];
		}
		searchData.value.businessType = undefined;

		dicData.value['businessType'] = dicList;
	}
};

onMounted(() => {});

function onDicLoaded(data) {
	dicData.value = data;
	mergeAllType();
}
const dsTableRef = ref();

const getTransName = (v) => {
	let a = allTypeArr.value.filter((item) => item.value == v.businessType);

	if (a.length >= 1) {
		return a[0].label;
	} else {
		return '-';
	}
};

const tableConfig = ref({
	dic,
	operation: {
		fixed: 'right',
		width: 220,
	},
	paging: {
		current: 1,
		size: 10,
		// total: 0,
	},
	dataList: [],
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: { classificationCode: '000001-000002' },
		// formatData: formatData,
	},
});
var onSearch = null;
// 查询
const onSearchHandle = (data) => {
	onSearch({ params: data });
};

// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}
const router = useRouter();
const optType = {
	add: () => {
		router.push('/template-library/other-template/field-ment/add');
	},
	toView: (data) => {
		router.push({
			path: '/template-library/other-template/field-ment/add',
			query: { id: data.id },
		});
	},
	edit: (data) => {
		router.push({
			path: '/template-library/other-template/field-ment/add',
			query: { id: data.id, type: 1 },
		});
	},
	remove: (data) => {
		// 删除
		handleConfirm({
			params: [data.id],
			refresh: onSearchHandle,
			request: tempTypeDel,
			texts: '确定删除吗？',
		});
	},
};

const onOperation = (obj) => {
	if (obj.field in optType) {
		return optType[obj.field](obj.row);
	} else {
		return optType[obj.type](obj.row);
	}
};
</script>

<style>
.btn-align-start .el-form--inline {
	display: flex;
	align-items: flex-start;
}
</style>
