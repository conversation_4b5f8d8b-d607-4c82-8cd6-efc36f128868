<template>
	<div class="layout-padding">
		<pro-back-pre :title="title" />
		<div class="layout-padding-auto layout-padding-view scroll" v-if="typeCase == '新增' || typeCase == '编辑'">
			<el-form class="mt12" ref="formModelRef" :model="formModel" label-width="120px" :rules="formRules">
				<el-row>
					<el-col :span="12">
						<el-form-item label="模板类型:" prop="fieldType">
							<el-select v-model="formModel.fieldType" @change="changeType" placeholder="请选择模板类型" style="width: 100%">
								<el-option v-for="item in modelTypeList" :key="item.value" :label="item.label" :value="Number(item.value)" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="业务类型:" prop="businessType">
							<el-select v-model.number="formModel.businessType" placeholder="请选择业务类型" style="width: 100%">
								<el-option v-for="item in businessTypeList" :key="item.value" :label="item.label" :value="Number(item.value)" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="字段文本名称:" prop="fieldName">
							<el-input v-model="formModel.fieldName" placeholder="字段文本名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="字段值:" prop="fieldEnName">
							<el-input v-model="formModel.fieldEnName" placeholder="请填写字段值" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="字段类型:" prop="labelType">
							<el-select v-model="formModel.labelType" placeholder="请选择字段类型" style="width: 100%">
								<el-option v-for="item in fieldType" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="是否渲染表单:" prop="isRenderFrom">
							<el-radio-group v-model="formModel.isRenderFrom">
								<el-radio :label="true" :value="true">是</el-radio>
								<el-radio :label="false" :value="false">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="标签属性:" prop="tagAttribute" label-width="120px">
					<div class="editor-wrapper">
						<proAceEditor ref="editorRef" v-model:value="formModel.tagAttribute" />
						<div class="warning-info">
							注：编写标签属性时，需要遵循以下几点，否则可能造成模板渲染出错 <br />
							1、默认的自执行函数不可删除。<br />
							2、尽量不敲击空格，尽量使用单引号（''）包裹属性值。<br />
							3、涉及到获取this变量时，要用that替换，例：that.dic.abc
						</div>
					</div>
				</el-form-item>
			</el-form>
			<div class="button-group justify-center">
				<template v-if="typeCase == '新增' || typeCase == '编辑'">
					<el-button @click="returnPage" pain>取消</el-button>
					<el-button @click="onSubmit" type="primary">确认</el-button>
				</template>
			</div>
		</div>

		<div v-if="typeCase == '查看'">
			<div class="layout-padding-auto layout-padding-view px-[20px] py-[10px]">
				<pro-side-title title="字段信息"></pro-side-title>
				<div class="flex-half">
					<div class="px-[20px] py-[20px] flex-item-half">
						<span class="label-width text-font">模板类型:</span><span class="px-[10px] text-font-sub">{{ getLabel(formModel.fieldType, modelTypeList) || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px] flex-item-half">
						<span class="label-width text-font">业务类型:</span><span class="px-[10px] text-font-sub">{{ getLabel(formModel.businessType, businessTypeList) || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px] flex-item-half">
						<span class="label-width text-font">字段文本名称:</span><span class="px-[10px] text-font-sub">{{ formModel.fieldName || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px] flex-item-half">
						<span class="label-width text-font">字段类型:</span><span class="px-[10px] text-font-sub">{{ getLabel(formModel.labelType, fieldType) || '' }}</span>
					</div>
					<div class="px-[20px] py-[20px] flex-item-half">
						<span class="label-width text-font">是否渲染表单:</span><span class="px-[10px] text-font-sub">{{ formModel.isRenderFrom ? '是' : '否' || '' }}</span>
					</div>
				</div>
				<div class="px-[20px] py-[20px] flex-half">
					<span class="label-width text-font">标签属性:</span>
					<span class="px-[10px] text-font-sub flex-1">
						<proAceEditor ref="editorRef2" :readonly="true" v-model:value="formModel.tagAttribute" />
					</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { fieldEdit, fieldToView, fieldChange } from '/@/api/template-library/template-fields';
// import { getFiles } from '/@/api/common/upload';
import { fieldType } from './data.js';
import { formRules } from './data-add.js';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import proAceEditor from '/@/components/pro-ace-editor/index.vue';
import { useMessage } from '/@/hooks/message';
import { useRouter } from 'vue-router';
import { dictApi } from '/@/api/dict/index';
// import { useDicts } from '/@/hooks/useDicts';

const router = useRouter();
const _id = router.currentRoute.value.query.id || '';
const _type = router.currentRoute.value.query?.type || '';

const returnPage = () => {
	router.back();
};
//表单数据
const formModel = ref({
	fieldType: null, //模板类型
	fieldName: null, //字段文本名称
	fieldEnName: null, //字段值
	labelType: null, //字段类型
	businessType: null, //业务类型
	isRenderFrom: true, //是否渲染表单
	//标签属性
	tagAttribute: `(function(){
	  return {
	  };
	})();`,
});

const title = ref('字段管理');
const formData = ref();
const typeCase = computed(() => {
	if (_id && _type) {
		title.value = '修改字段管理';
		return '编辑';
	}
	if (_id && !_type) {
		title.value = '字段管理详情';
		return '查看';
	}
	if (!_id && !_type) {
		title.value = '新增字段管理';
		return '新增';
	}
	return null;
});

// 模板类型下拉
const modelTypeList = ref([]);
//业务类型下拉
const businessTypeList = ref([]);
onMounted(async () => {
	let res1 = await getDicTionaryAndValue(dictApi['templateBusinessType']);

	modelTypeList.value = res1.data;
});
const getLabel = (val, list) => {
	return list.find((item) => item.value == val)?.label;
};
//模板类型修改，更换业务类型下拉
const changeType = async (val) => {
	formModel.value.businessType = null;
	let list = null;
	if (val == 4) {
		list = await getDicTionaryAndValue(dictApi['contracTemp']);
	}
	if (val == 5) {
		list = await getDicTionaryAndValue(dictApi['notificationTemp']);
	}
	if (val == 6) {
		list = await getDicTionaryAndValue(dictApi['noticeTemp']);
	}
	if (val == 7) {
		list = await getDicTionaryAndValue(dictApi['tenderDocumentTemp']);
	}
	if (val == 8) {
		list = await getDicTionaryAndValue(dictApi['announcementTemp']);
	}
	if (val == 9) {
		list = await getDicTionaryAndValue(dictApi['otherTemp']);
	}
	businessTypeList.value = list.data ? list.data : [];
};

const editorRef = ref();
const editorRef2 = ref();
//获取详情
const getDetail = async (id) => {
	//
	try {
		let res = await fieldToView(id);

		changeType(res.data.fieldType);
		formModel.value = res.data;
	} catch (err) {
		useMessage().error(err.msg);
	}
};

if (_id != '') {
	getDetail(_id);
}
const submitLoading = ref(false); // 提交按钮loading
// const fileForm = ref();
const formModelRef = ref();
//新增-保存
const onSubmit = async () => {
	let valid = await formModelRef.value.validate().catch(() => {});
	if (!valid) return;
	submitLoading.value = true;
	if (_id) {
		fieldChange(formModel.value)
			.then((res) => {
				useMessage().success('修改成功');
				returnPage();
			})
			.catch((err) => {
				useMessage().error(err.msg);
			})
			.finally(() => {
				submitLoading.value = false;
			});
	} else {
		fieldEdit(formModel.value)
			.then((res) => {
				useMessage().success('提交成功');
				returnPage();
			})
			.catch((err) => {
				useMessage().error(err.msg);
			})
			.finally(() => {
				submitLoading.value = false;
			});
	}
};
</script>

<style lang="scss" scoped>
.justify-center {
	display: flex;
	justify-content: center;
	padding-top: 30px;
}
.editor-wrapper {
	// width: 1300px;
	width: 100%;
}
.warning-info {
	color: #ff0000;
}
.scroll {
	overflow: scroll;
}
.co-form-inline {
	margin: auto;
	.el-form {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.el-form-item {
			margin-bottom: 20px;
			.el-input__inner {
				width: 600px;
			}
			.el-select {
				width: 600px;
			}
		}
	}
}
.el-col {
	margin-bottom: 20px;
}
.label-width {
	width: 100px;
	display: inline-block;
	text-align: right;
}
.text-font {
	font-size: 14px;
	color: #606266;
	font-weight: 600;
}
.text-font-sub {
	font-size: 14px;
	color: #212529;
}
.flex-half {
	display: flex;
	flex-wrap: wrap;
}
.flex-item-half {
	width: 50%;
}
.flex-1 {
	flex: 1;
}
</style>
