import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useDicts } from '/@/hooks/useDicts';
import { dictApi } from '/@/api/dict/index';
export const previewUrl = ref('');
// 搜索表单配置
export const searchConfig = {
	styles: { display: 'flex' },
	inline: 'true',
	size: 'mini',
	labelPosition: 'left',
	items: [
		{
			prop: 'title',
			type: 'input',

			attrs: {
				placeholder: '请输入模板名称',
				clearable: true,
				label: '模板名称',
			},
		},
		{
			prop: 'abstractContent',
			type: 'input',

			attrs: {
				placeholder: '请输入业务key',
				clearable: true,
				label: '业务key',
			},
		},
	],
};

// table表格
export const tableHeader = [
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ prop: 'title', label: '模板名称' },
	{ prop: 'abstractContent', label: '业务key' },
	{ prop: 'publisherName', label: '发布人' },
	{ prop: 'insertTime', label: '创建时间', width: 200 },
	{ prop: 'releaseTime', label: '更新时间', width: 200 },
];
export const dictConfig = {
	...useDicts('projectType', 'templateType'),
};

export const formList = [
	{
		id: 'templateName',
		name: '模板名称',
		type: 'input',
		attrs: {
			placeholder: '请输入模板名称',
			clearable: true,
		},
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'projectType',
		name: '项目类型',
		field: 'projectType',
		type: 'select',
		dicKey: 'projectType',
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'templateType',
		name: '文件类型',
		field: 'templateType',
		type: 'select',
		dicKey: 'templateType',
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'templateDescription',
		name: '模板说明',
		type: 'textarea',
		attrs: {
			placeholder: '请输入模板说明',
			clearable: true,
		},
		validate: 'required',
		required: true,
		disabled: false,
	},
	{
		id: 'izEnable',
		name: '模板状态',
		field: 'izEnable',
		type: 'switch',
		option: [
			{ activeValue: true, activeText: '是' },
			{ inactiveValue: false, inactiveText: '否' },
		],
		validate: 'required',
		required: false,
		disabled: false,
	},
];

export const rule = reactive({
	fileRecords: [{ required: true, message: '请选择附件', trigger: 'change' }],
});
