<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<co-search ref="searchRef" inline label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" single-mode="icon-hook" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left" />
			<!-- 复制 -->
			<copy-dialog ref="copyDialogRef" @getList="onSearchHandle" />
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { getList, delObjs, dataFormEdit } from '/@/api/template-library/data-form';
import { searchConfig } from './data';
import { dictApi } from '/@/api/dict/index';
import { useMessage, useMessageBox } from '/@/hooks/message';

const router = useRouter();
const dicData = ref({});
// 定义的变量
var onSearch = null;
const tableHeader = ref([
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ label: '模板名称', prop: 'templateName' },
	{ label: '创建者', prop: 'insertPersonName' },
	{
		label: '模板状态',
		prop: 'izEnable',
		type: 'switch',
		width: '100px',
		attrs: {
			'before-change': (row) => {
				return handleStatusChange(row);
			},
		},
	},
]);
// 表格配置
const tableConfig = ref({
	dic: {
		contentStatus: { value: dictApi['contentStatus'] },
	},
	operation: {
		fixed: 'right',
		width: 300,
		list: [],
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {}, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
		safeNullParams: dicData.value, // - 参数受保护
	},
});

// 查询
function onSearchHandle(data) {
	onSearch({ params: data });
}

// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}

// 字典加载完毕
function onDicLoaded(data) {
	data.contentStatus.map((i) => {
		i.value = i.value == '1';
		return i;
	});
	dicData.value = data;
}

const copyDialog = defineAsyncComponent(() => import('./component/copy-dialog.vue')); // 导出订单
const copyDialogRef = ref();
// 监听表格搜索操作
function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	if (field == 'edit') {
		// 编辑
		router.push({ path: '/template-library/list-template/edit', query: { type: 2, id: row.id } });
	} else if (field == 'toView') {
		// 查看
		router.push({ path: '/template-library/list-template/detail', query: { id: row.id } });
	} else if (field == 'remove') {
		//  删除
		useMessageBox()
			.confirm('是否确认删除此数据?')
			.then((is) => {
				if (!is) {
					return;
				}
				delObjs({ id: row.id })
					.then((res) => {
						onSearchHandle();
						useMessage().success(res.msg || '操作成功');
					})
					.catch((err) => {
						useMessage().error(err.msg);
					});
			})
			.catch(() => null);
	} else if (field == 'copy') {
		// 复制
		copyDialogRef.value.openDialog(row);
	} else if (field == 'add') {
		// 新增
		router.push({ path: '/template-library/list-template/edit', query: { type: 1 } });
	} else if (field == 'izEnable') {
		// 模板状态修改
		// handleStatusChange(row)
	}
}
//判断是否修改状态
const handleStatusChange = async (row) => {
	const text = !row.izEnable ? '确认要改为启用状态吗？' : '确认要改为禁用状态吗？';
	try {
		const confirmRes = await useMessageBox().confirm(text);
		if (!confirmRes) return false;
		const { code, msg } = await dataFormEdit({ id: row.id, izEnable: !row.izEnable }).catch((err) => err);
		if (code === 200) {
			useMessage().success('操作成功');
			onSearchHandle();
		} else {
			useMessage().warning(msg);
		}
		return code === 200;
	} catch (error) {
		return false;
	}
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.btn-list {
	margin-bottom: 10px;
}
</style>
