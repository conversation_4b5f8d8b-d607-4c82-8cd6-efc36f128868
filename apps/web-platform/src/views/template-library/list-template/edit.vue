<template>
	<div id="list-template-edit">
		<pro-back-pre :title="title" />
		<div class="co-bg-class layout-padding-view px-[20px] py-[10px]">
			<h3 class="title"><b>模板信息</b></h3>
			<co-form ref="formRef" label-width="150px" class="editform" :form-list="fromList" :form-data="formData"> </co-form>
			<!-- v-if="type != 1" -->
			<div id="formtabler">
				<el-button class="mb15" type="primary" @click="addRow">添加</el-button>
				<el-form ref="tableFormRef" :model="formAll" :rules="tableFormRules">
					<el-table :data="tableData" :header-cell-style="tableStyle.headerCellStyle" border style="width: 100%">
						<el-table-column prop="fieldName" label="名称">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].fieldName'" :rules="tableFormRules.fieldName" style="position: relative; height: 65px">
									<el-input v-model="row.fieldName" placeholder="请输入名称" type="text" />
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column label="数字类型">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].fieldType'" :rules="tableFormRules.fieldType" style="position: relative; height: 65px">
									<el-select v-model="row.fieldType" placeholder="请选择数字类型" style="width: 120px">
										<el-option v-for="item in fieldType" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column prop="bidPriceType" label="字段类型">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].bidPriceType'" :rules="tableFormRules.bidPriceType" style="position: relative; height: 65px">
									<el-select v-model="row.bidPriceType" placeholder="请选择字段类型" style="width: 120px" @change="handlerSelect(row)">
										<el-option v-for="item in bidPriceType" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column prop="fieldUnit" label="单位">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].fieldUnit'">
									<el-input v-model="row.fieldUnit" placeholder="请输入名称" v-if="row.bidPriceType != 0" />
									<el-select v-model="row.fieldUnit" placeholder="请选择字段类型" style="width: 120px" v-else>
										<el-option v-for="item in fieldUnit" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column prop="ifRequired" label="是否必填">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].ifRequired'">
									<el-switch v-model="row.ifRequired" />
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column prop="note" label="备注说明">
							<template #default="{ row, $index }">
								<el-form-item :prop="'items[' + $index + '].note'">
									<el-input v-model="row.note" type="textarea" placeholder="请输入备注说明" />
								</el-form-item>
							</template>
						</el-table-column>
						<el-table-column prop="action" label="操作" width="180">
							<template #default="scope">
								<el-button type="text" @click="delRow(scope.row, scope.$index)"> 删除 </el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-form>
			</div>
			<!-- 提交按钮 -->
			<div class="detail-submit pb-[15px]">
				<el-button class="mt20 w-[120px]" type="primary" @click="handlerOk" :loading="vloading">保存</el-button>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getDetail, dataFormAdd, dataFormEdit } from '/@/api/template-library/data-form';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';
import { getTableStyle } from '/@/hooks/table';
import { fromList } from './data.js';

const title = ref('');
const router = useRouter();
const route = useRoute();
const formData = ref({});
const chooseId = ref();
const type = ref(1);
const tableStyle = getTableStyle();

// 下拉列表
const fieldType = ref();
const bidPriceType = ref();
const fieldUnit = ref();
const tableFormRules = ref({
	fieldName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
	fieldType: [{ required: true, message: '请选择数字类型', trigger: ['blur', 'change'] }],
	bidPriceType: [{ required: true, message: '请选择字段类型', trigger: ['blur', 'change'] }],
});

const tableData = ref([]);
const formAll = ref({ items: [] });

watch(
	() => formAll.value.items,
	(newVal) => {
		tableData.value = newVal;
	},
	{ deep: true }
);

const addRow = function () {
	// tableData.value.push({
	//   fieldName: '',
	//   fieldType: '',
	//   ifRequired: false,
	//   bidPriceType: '',
	//   fieldUnit: '',
	//   note:''
	// })
	formAll.value.items.push({
		fieldName: '',
		fieldType: 1,
		ifRequired: false,
		bidPriceType: 0,
		fieldUnit: '',
		note: '',
	});
};

const delRow = function (row, index) {
	useMessageBox()
		.confirm('是否确认删除此数据?')
		.then((is) => {
			if (is) {
				// tableData.value.splice(index, 1)
				formAll.value.items.splice(index, 1);
			}
		});
};

const formRef = ref();
const tableFormRef = ref();
const vloading = ref(false);

function addfun(data, tabledata) {
	const obj = Object.assign({}, data, { formTemplateItemDTOList: tabledata || [] });
	dataFormAdd(obj)
		.then((res) => {
			vloading.value = false;
			useMessage().success(res.msg || '操作成功');
			router.push({ path: '/template-library/list-template/index' });
		})
		.catch(() => {
			vloading.value = false;
			useMessage().error(err.msg);
		});
}
function editfun(data, tabledata) {
	let obj = {
		id: chooseId.value,
		...formData.value,
		...data,
		formTemplateItemDTOList: tabledata || [],
	};
	dataFormEdit(obj)
		.then((res) => {
			vloading.value = false;
			useMessage().success(res.msg || '操作成功');
			router.push({ path: '/template-library/list-template/index' });
		})
		.catch(() => {
			vloading.value = false;
			useMessage().error(err.msg);
		});
}
const handlerOk = async () => {
	vloading.value = true;
	let form = await formRef.value.getFormData().catch(() => {
		vloading.value = false;
	});
	if (!form) {
		vloading.value = false;
		return false;
	}

	// if (type.value == 1) {
	//   // 直接是表单提交
	//   addfun(form)
	// } else {
	//   // 保单校验
	//   if (formAll.value.items?.length <= 0) {
	//     editfun(form)
	//   } else {
	//     tableFormRef.value?.validate((valid) => {
	//       if (valid) {
	//         editfun(form, formAll.value.items)
	//       } else {
	//         vloading.value = false;
	//         return false
	//       }
	//     })
	//   }
	// }
	if (formAll.value.items?.length <= 0) {
		if (type.value == 1) {
			addfun(form);
		} else {
			editfun(form);
		}
	} else {
		tableFormRef.value?.validate((valid) => {
			if (valid) {
				if (type.value == 1) {
					addfun(form, formAll.value.items);
				} else {
					editfun(form, formAll.value.items);
				}
				// editfun(form, formAll.value.items)
			} else {
				vloading.value = false;
				return false;
			}
		});
	}
};
function handlerSelect(row) {
	if (row.bidPriceType != 0) {
		row.fieldUnit = '';
	}
}

function onGetDetail(id) {
	getDetail(id)
		.then((res) => {
			if (res.code == 200) {
				// tableData.value = res.data.formTemplateItemDTOList || []
				formAll.value.items = res.data.formTemplateItemDTOList || [];
				formData.value = res.data;
			}
		})
		.catch(() => {});
}
async function init() {
	const resList = await getDicTionaryAndValue(dictApi['numType']);
	fieldType.value = resList.data.map((v) => {
		return { label: v.label, value: Number(v.value) };
	});
	const resList1 = await getDicTionaryAndValue(dictApi['bidPriceType']);
	bidPriceType.value = resList1.data.map((v) => {
		return { label: v.label, value: Number(v.value) };
	});
	const resList2 = await getDicTionaryAndValue(dictApi['dataFormUnit']);
	fieldUnit.value = resList2.data;
}
onMounted(async () => {
	type.value = route.query.type;
	await init();
	if (type.value == 1) {
		formData.value = {};
		title.value = '新增开标一览表模板';
	} else {
		title.value = '修改开标一览表模板';

		if (!route.query.id) {
			return;
		}
		chooseId.value = route.query.id;
		onGetDetail(route.query.id);
	}
});
</script>

<style lang="scss" scoped>
h3.title {
	border-bottom: 1px solid #f1f1f1;
	height: 45px;
	line-height: 50px;
	padding-left: 5px;
	margin-bottom: 20px;
	font-weight: 700;
	font-size: 18px;
	color: #000;
}
.co-bg-c-class {
	text-align: center;
}
</style>
<style lang="scss">
.detail-submit {
	display: flex;
	justify-content: center;
}
#list-template-edit {
	padding: 15px 15px 5px 15px !important;
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}
	.editform .el-form-item.co-form-span12 {
		flex: 0 0 50%;
	}
	.editform .co-form-block {
		flex: 0 0 100%;
	}
}
.redsmall {
	color: red;
	font-size: 12px;
	position: absolute;
	top: 43px;
	height: 20px;
}
#list-template-edit #formtabler .el-form-item__error {
	top: 51px;
}
</style>
