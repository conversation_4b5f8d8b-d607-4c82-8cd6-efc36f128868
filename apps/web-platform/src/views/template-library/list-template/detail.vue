<template>
  <div class="layout-padding" id="list-template-detail">
    <pro-back-pre :title="title"/>
    <div class="layout-padding-view px-[20px] py-[10px]">
      <el-collapse v-model="activeNames" class="detailbody">
        <el-collapse-item title="模板信息" name="1">
          <el-descriptions :column="2" :size="small" border>
            <el-descriptions-item v-for="(item) in formList2" :label="item.name" :key="item.id">
              <div v-if="item.id !='izEnable'">{{ chooseRow[item.id] || '' }}</div>
              <div v-else>{{ chooseRow[item.id] ? '启用' : '禁用' }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
        <el-collapse-item title="表单内容" name="2">
          <el-form ref="tableFormRef" :model="formAll" :rules="tableFormRules">
            <el-table :data="tableData" :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle" border style="width: 100%">
              <el-table-column prop="fieldName" label="名称">
                <template #default="{row, $index}">
                  <el-form-item :prop="'items['+$index+'].fieldName'" :rules="tableFormRules.fieldName">
                    <el-input v-model="row.fieldName" placeholder="请输入名称" type="text" disabled/>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="数字类型">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'items['+ $index+'].fieldType'" :rules="tableFormRules.fieldType">
                    <el-select v-model="row.fieldType" placeholder="请选择数字类型" style="width: 120px" disabled>
                      <el-option v-for="item in fieldType" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="bidPriceType" label="字段类型">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'items['+ $index+'].bidPriceType'" :rules="tableFormRules.bidPriceType">
                    <el-select v-model="row.bidPriceType" placeholder="请选择字段类型" style="width: 120px" @change="handlerSelect(row)" disabled>
                      <el-option v-for="item in bidPriceType" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="fieldUnit" label="单位">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'items['+ $index+'].fieldUnit'">
                    <el-input v-model="row.fieldUnit" placeholder="请输入名称" v-if="row.bidPriceType != 0" disabled/>
                    <el-select v-model="row.fieldUnit" placeholder="请选择字段类型" style="width: 120px" v-else disabled>
                      <el-option v-for="item in fieldUnit" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="ifRequired" label="是否必填">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'items['+ $index+'].ifRequired'">
                    <el-switch v-model="row.ifRequired" disabled/>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="note" label="备注说明">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'items['+ $index+'].note'">
                    <el-input v-model="row.note" type="textarea" placeholder="请输入备注说明" disabled/>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted} from 'vue';
import {getDetail} from '/@/api/template-library/data-form';
import {getDicTionaryAndValue} from '/@/api/common/dic_response';
import {dictApi} from '/@/api/dict/index';
import {formList2} from './data.js'
import { getTableStyle } from '/@/hooks/table';


const route = useRoute();
const tableStyle = getTableStyle();

const chooseRow = ref({})

const activeNames = ref(['1', '2'])

const title = ref('新增开标一览表模板')
// 下拉列表
const fieldType = ref()
const bidPriceType = ref()
const fieldUnit = ref()

const tableFormRules = reactive({
  fieldName: [{required: true, message: '名称不能为空', trigger: 'blur'}],
  fieldType: [{required: true, message: '请选择数字类型', trigger: ['blur', 'change']}],
  bidPriceType: [{required: true, message: '请选择字段类型', trigger: ['blur', 'change']}]
});

const tableData = ref([]);
const formAll = ref({items: []});


function onGetDetail(id) {
  getDetail(id).then((res) => {
    if (res.code == 200) {
      tableData.value = res.data.formTemplateItemDTOList || []
      formAll.value.items = res.data.formTemplateItemDTOList || []
      chooseRow.value = res.data
    }
  }).catch(() => {
    // useMessage().error('')
  })
}

async function init() {
  const resList = await getDicTionaryAndValue(dictApi['numType']);
  fieldType.value = resList.data.map((v) => {
    return {label: v.label, value: Number(v.value)}
  });
  const resList1 = await getDicTionaryAndValue(dictApi['bidPriceType']);
  bidPriceType.value = resList1.data.map((v) => {
    return {label: v.label, value: Number(v.value)}
  });
  const resList2 = await getDicTionaryAndValue(dictApi['dataFormUnit']);
  fieldUnit.value = resList2.data
}

onMounted(async () => {
  title.value = '开标一览表模板详情'
  await init()
  if (!route.query.id) {
    return
  }
  onGetDetail(route.query.id)

})
</script>

<style lang="scss" scoped>

.co-bg-c-class {
  text-align: center;
}

</style>
<style lang="scss">
#list-template-edit .el-form {
  display: flex;
  flex-wrap: wrap;
}

#list-template-edit .editform .el-form-item.co-form-span12 {
  flex: 0 0 50%;
}

#list-template-edit .editform .co-form-block {
  flex: 0 0 100%;
}

.co-bg-class {
  .el-collapse-item__header {
    font-size: 20px;
    font-weight: bold;
    margin: 10px;
  }

  .el-collapse-item__header.is-active {
    border-bottom-color: none
  }

  .el-descriptions__cell.el-descriptions__label {
    width: 120px;
  }
}
</style>