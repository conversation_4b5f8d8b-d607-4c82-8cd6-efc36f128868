<template>
	<div class="copytemp">
		<el-dialog title="复制模板" v-model="copyInfo.isCopyDialog" width='600px' @beforeClose="beforeClose">
			<el-descriptions class="margin-top" :column="1" border>
				<el-descriptions-item label="被复制模板名称">
					{{ copyInfo.copyName }}
				</el-descriptions-item>
				<el-descriptions-item label="新模板名称">
					<el-input v-model="templateName" placeholder="请输入新模板名称" />
				</el-descriptions-item>
			</el-descriptions>
			<div class="co-text-warning mt10">温馨提示：复制的新模板默认状态为禁用状态，支持修改和启用</div>
			<div class="co-footer">
				<el-button @click="confirm(0)">确认复制稍后编辑</el-button>
				<el-button type="primary" @click="confirm(1)">确认复制并进入编辑</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script setup>
import { ref, defineEmits} from 'vue'
import api from '/@/api/template-library/data-form'
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['getList']);

const router = useRouter();

const copyInfo = ref({
	isCopyDialog: false,
	copyId: '',
	copyName: ''
})

const templateName = ref()

const confirm = (type)=> {
	if (templateName.value == '') {
		useMessage().warning('请输入新模板名称!');
		return
	}
	const data = {
		id: copyInfo.value.copyId,
		catalogName: templateName.value
	}
	api.dataFormCopy(data).then(res => {
		if (type == 1) {
			router.push({ path:'/template-library/list-template/edit', query: { id: res.data, type: 2 } })
		} else {
			emit('getList')
		}
		beforeClose()
		useMessage().success(res.msg || '操作成功')
	})
}
const beforeClose = () => {
	copyInfo.value.isCopyDialog = false
	emit('getList')
}

function openDialog(row) {
	copyInfo.value.isCopyDialog = true
	copyInfo.value.copyId = row.id
	copyInfo.value.copyName = row.templateName
	templateName.value = ''
}
defineExpose({ openDialog });

</script>
<style lang="scss" scoped>
.copytemp{
	.co-text-warning{
		color: #fc9b45
	}
}
.co-footer{
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
</style>
