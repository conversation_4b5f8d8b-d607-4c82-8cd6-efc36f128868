export const searchConfig = {
	styles: { background: '#ffffff' },
	inline: true,
	items: [
		{
			prop: 'templateName',
			type: 'input',
			attrs: {
				placeholder: '请输入模板名称',
				clearable: true,
				label: '模板名称',
			},
		},
		{
			prop: 'izEnable',
			type: 'select',
			option: 'contentStatus',
			attrs: {
				placeholder: '请选择模板状态',
				clearable: true,
				label: '模板状态',
			},
		},
	],
};

// 新增，编辑
export const fromList = [
	{ name: '模板名称', id: 'templateName', css: 'co-form-span12', max: 50 },
	{ name: '模板状态', id: 'izEnable', type: 'switch', css: 'co-form-span12', value: false },
	{ name: '备注', id: 'remark', type: 'textarea', max: 100, required: false, css: 'co-form-block' },
];
// 详情
export const formList2 = [
	{ name: '模板名称', id: 'templateName' },
	{ name: '模板状态', id: 'izEnable' },
	{ name: '创建者', id: 'insertPersonName' },
	{ name: '备注', id: 'remark' },
];
