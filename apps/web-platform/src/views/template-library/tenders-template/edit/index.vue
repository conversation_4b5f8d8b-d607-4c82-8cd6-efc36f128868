<!--招标文件模板添加 编辑 -->
<template>
	<div class="layout-padding">
		<pro-back-pre :title="id ? '修改招标文件模板' : '新增招标文件模板'" />
		<div class="layout-padding-view px-[20px] py-[10px]" v-loading="loading">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
				<!-- 模板基本信息 -->
				<pro-side-title title="模板基本信息"></pro-side-title>
				<!-- 模板名称 -->
				<el-form-item label="模板名称：" prop="templateName">
					<el-input v-model="form.templateName" placeholder="请输入" style="width: 400px" :maxlength="50" />
				</el-form-item>
				<el-form-item label="项目类别：" prop="projectCategory">
					<el-radio-group v-model="form.projectCategory">
						<el-radio v-for="(item, index) in projectCategoryList" :key="index" :label="item.value" border>
							{{ item.label }}
						</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="招标方式：" prop="tenderMode" v-if="form.projectCategory === '1'">
					<el-radio-group v-model="form.tenderMode">
						<el-radio v-for="(item, index) in engineering" :key="index" :label="item.value" border>
							{{ item.label }}
						</el-radio>
					</el-radio-group>
				</el-form-item>
				<template v-if="form.projectCategory === '2'">
					<el-form-item label="采购方式：" prop="purchaseMode">
						<el-checkbox-group v-model="form.purchaseMode" @change="selectChange($event, 'purchaseMode')">
							<el-checkbox v-for="(item, index) in serviceData" :key="index" :label="item.value" :disabled="item.disabled" border> {{ item.label }}</el-checkbox>
						</el-checkbox-group>
					</el-form-item>
					<el-form-item label="邀请方式：" prop="invitationMode">
						<el-radio-group v-model="form.invitationMode" :disabled="invitationModeDis">
							<el-radio v-for="(item, index) in invitationMethod" :key="index" :label="item.value" border>
								{{ item.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</template>
				<el-form-item label="公告模板：" prop="noticeTemplateFileId">
					<!-- 上传组件 -->
					<upl
						:upload-arguments="{ pvcId: systemPvcid }"
						:hasMultiple="false"
						:isShowFileList="false"
						:isHiddenTable="false"
						:isOperation="true"
						:auto-upload="true"
						:fileIds="form.noticeTemplateFileId || ''"
						:max-files="1"
						:accept="'.docx'"
						:oper="{ remove: true, download: true }"
						@breakUpload="
							(data) => {
								return breakUpload({ data, type: 'notice' });
							}
						"
					></upl>
				</el-form-item>
				<el-form-item label="招采文件模板：" prop="files">
					<!-- 招采文件模板 -->
					<div class="btn-list">
						<el-button :type="currentBtnKey == `${bidMethodData.value}-docx` ? 'primary' : ''" @click="handleBtnClick(bidMethodData.value, 'docx')">{{ bidMethodData.label }}-文件模板</el-button>
						<el-button :type="currentBtnKey == `${bidMethodData.value}-pdf` ? 'primary' : ''" @click="handleBtnClick(bidMethodData.value, 'pdf')">{{ bidMethodData.label }}-PDF</el-button>
					</div>
					<!-- 上传组件 -->
					<upl
						:key="uploadKey"
						:upload-arguments="{ pvcId: systemPvcid }"
						:hasMultiple="false"
						:isShowFileList="false"
						:isHiddenTable="false"
						:isOperation="true"
						:auto-upload="true"
						:fileIds="fileIds"
						:max-files="1"
						:accept="currentBtnKey.indexOf('docx') != -1 ? '.docx' : '.pdf'"
						:oper="{ remove: true, download: true }"
						@breakUpload="
							(data) => {
								return breakUpload({ data, type: 'tenderFile' });
							}
						"
					></upl>
					<!-- 穿梭框 -->
					<el-transfer
						v-if="currentBtnKey.indexOf('docx') != -1"
						v-model="fieldIds"
						class="mt-[20px]"
						filterable
						:data="allTemplateFields"
						:titles="['未关联字段', '模板关联字段']"
						:button-texts="['取消选中', '选中']"
						:props="{
							key: 'id',
							label: 'fieldName',
						}"
						@change="handleFieldsChange"
					/>
				</el-form-item>
				<!-- 提交按钮 -->
				<div class="detail-submit pb-[15px]">
					<el-button @click="cancel">取消</el-button>
					<el-button type="primary" :loading="submitLoading" @click="submit">确认</el-button>
				</div>
			</el-form>
		</div>
	</div>
</template>
<script setup>
import { getDetail, getFieldList, save, edit } from '/@/api/template-library/tender-template.js';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict/index';
import { useMessage } from '/@/hooks/message';
import { useDicts } from '/@/hooks/useDicts';
import { useUserInfo } from '/@/stores/userInfo';
import { useEnvStore } from '/@/stores/env';
// 引入组件
const upl = defineAsyncComponent(() => import('/@/components/Upload/BigFile.vue'));

// 引入字典
const { engineering, serviceData, invitationMethod } = useDicts('engineering', 'serviceData', 'invitationMethod');

// 项目类别字典
const projectCategoryList = [
	{ value: '1', label: '工程项目' },
	{ value: '2', label: '采购项目' },
];

// 定义变量内容
const route = useRoute();
const router = useRouter();

const id = ref();
const formRef = ref();
const allTemplateFields = ref([]); // 全部字段列表
const fieldIds = ref([]); // 选择的字段列表
const fileIds = ref(''); // pdf或word的文件id
const bidMethodList = ref([]); // 步骤类型
const currentBtnKey = ref(''); // 选中的招采文件模板按钮
const invitationModeDis = ref(false); // 邀请方式是否不可选
const loading = ref(false);
const submitLoading = ref(false);
const form = ref({
	templateName: undefined, // 模板名称
	projectCategory: undefined, // 项目类别
	tenderMode: [], // 招标方式
	purchaseMode: [], // 采购方式
	invitationMode: undefined, // 邀请方式
	noticeTemplateFileId: '', // 公告模板
	files: [], // 招采文件模板
});

// 招标方式数据
const bidMethodData = {
	dicId: 'D6206425793899094017',
	label: '招采文件',
	dicShowCtx: '招采文件',
	dicTypeId: 'D5981217528043458561',
	value: '10',
	grade: 1,
	idPath: 'D6206425793899094017',
	sort: 1,
};
// 获取pvcid
const systemPvcid = useEnvStore().getEnv['VUE_APP_FILE_CLOUD_SERVE_SYSTEM_PVCID'];
const rules = ref({
	templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
	projectCategory: [{ required: true, message: '请选择项目类别', trigger: 'change' }],
	purchaseMode: [{ required: true, message: '请选择采购方式', trigger: 'change' }],
	tenderMode: [{ required: true, message: '请选择招标方式', trigger: 'change' }],
	invitationMode: [{ required: true, message: '请选择邀请方式', trigger: 'change' }],
	noticeTemplateFileId: [{ required: true, message: '请选择公告模板', trigger: 'change' }],
	files: [{ required: true, message: '附件不能为空', trigger: 'change' }],
});

onMounted(() => {
	init();
	getFieldListApi();
});

// 页面逻辑初始化
async function init() {
	id.value = route.query.id;

	// 数据格式初始化
	if (!id.value) {
		currentBtnKey.value = `${bidMethodData.value}-docx`;
		form.value.files = [
			{
				bidMethod: bidMethodData.value,
				templateFileId: '', // 招标文件Word
				previewFileId: '', // 招标文件PDF
				fieldIds: [], // 选择的字段列表
			},
		];
	} else {
		// 获取详情
		getDetailApi();
	}
}

// 获取详情
function getDetailApi() {
	loading.value = true;
	getDetail(id.value)
		.then((res) => {
			// 采购方式
			const purchaseMode = res.data.purchaseMode || '';
			res.data.purchaseMode = purchaseMode ? purchaseMode.split(',') : [];

			// 采购目录
			const purchaseCatalog = res.data.purchaseCatalog || '';
			res.data.purchaseCatalog = purchaseCatalog ? purchaseCatalog.split(',') : [];

			res.data.files = res.data.files?.map((ele) => {
				if (ele.fieldIds === null) {
					ele.fieldIds = [];
				}
				return ele;
			});
			form.value = res.data;

			// 多选框选择改变
			selectChange(res.data.purchaseMode, 'purchaseMode');

			// 附件类型切换回调
			handleBtnClick(bidMethodData.value, 'docx');
		})
		.catch((err) => {
			useMessage().error(err.msg);
		})
		.finally(() => {
			loading.value = false;
		});
}

// 多选框选择改变
function selectChange(value, field) {
	switch (field) {
		// 采购方式
		case 'purchaseMode':
			const listA = [2, 4]; // 邀请招标 单一来源
			const listB = [1, 3, 5, 6, 9]; // 其他采购方式

			// 选中邀请招标或单一来源 邀请方式只能是邀请且不可更改
			if (listA.includes(+value[0])) {
				invitationModeDis.value = true;
				form.value.invitationMode = '2';
			} else {
				invitationModeDis.value = false;
			}

			// 选中邀请招标或单一来源 就不能选择其他的采购方式 反之亦然
			serviceData.value.forEach((item) => {
				item.disabled = false;
				if (listA.includes(+value[0])) {
					item.disabled = listB.includes(+item.value);
				}
				if (listB.includes(+value[0])) {
					item.disabled = listA.includes(+item.value);
				}
			});
			break;
	}
}

// 获取字段列表
function getFieldListApi() {
	getFieldList({
		fieldType: 7,
		businessType: 71,
	}).then((res) => {
		allTemplateFields.value = res.data;
	});
}

// 字段
function handleFieldsChange(value) {
	if (form.value.files[0] === undefined) {
		form.value.files[0] = {};
	}
	form.value.files[0].fieldIds = value;
}

// 上传文件回调
function breakUpload({ data, type }) {
	if (data.field !== 'upload' && data.field !== 'remove') return;

	if (type === 'notice') {
		// 公告模板
		form.value.noticeTemplateFileId = data.fsId;
	} else {
		// 招采文件模板
		// 根据 currentBtnKey 区分 eg: 10-docx
		const arr = currentBtnKey.value.split('-');
		if (form.value.files[0] === undefined) {
			form.value.files[0] = {};
		}
		form.value.files[0][arr[1] === 'docx' ? 'templateFileId' : 'previewFileId'] = data.fsId;
	}
}

// 附件类型切换回调-展示对应的文件
const uploadKey = ref();

function handleBtnClick(method, type) {
	currentBtnKey.value = `${method}-${type}`;
	uploadKey.value = Math.random();
	const filesData = form.value.files[0];
	fileIds.value = filesData[type === 'docx' ? 'templateFileId' : 'previewFileId'];
	fieldIds.value = filesData['fieldIds'];
}

/**
 * 提交表单数据
 * @function
 * @async
 */
async function submit() {
	// 验证表单是否符合规则
	const valid = await formRef.value.validate().catch(() => {});
	if (!valid) return false;

	const datas = { ...form.value };

	// 验证是否完善完上传文件信息
	const flag = datas.files.some((el) => {
		const keys = Object.keys(el);
		return keys.some((key) => {
			return el[key] === '' || el[key] === undefined || (Array.isArray(el[key]) && el[key].length === 0);
		});
	});
	if (flag) {
		useMessage().warning('请完善招采文件模板信息');
		return;
	}

	// 如果没有选择对应的项目类别则清空对应的选择项
	// 选择工程项目 清空采购方式、邀请方式
	// 选择采购项目 清空招标方式
	if (datas.projectCategory == 1) {
		datas.purchaseMode = '';
		datas.invitationMode = '';
	}
	if (datas.projectCategory == 2) {
		datas.tenderMode = '';
		datas.purchaseMode = datas.purchaseMode ? datas.purchaseMode.join(',') : '';
	}
	datas.purchaseCatalog = datas.purchaseCatalog ? datas.purchaseCatalog.join(',') : '';
	datas.enabled = id.value ? datas.enabled : 0;

	try {
		submitLoading.value = true;
		id.value ? await edit(datas) : await save(datas);

		useMessage().success('提交成功');
		cancel();
	} catch (err) {
		console.log(err);
		useMessage().error(err.msg || '操作失败');
	} finally {
		submitLoading.value = false;
	}
}

// 取消
function cancel() {
	router.back(-1);
}
</script>
<style lang="scss" scoped>
.layout-padding-view {
	overflow-y: auto;
}

.detail-submit {
	display: flex;
	justify-content: center;
}

.btn-list {
	width: 100%;
	margin-right: 10px;
	margin-bottom: 10px;
}

:deep(.el-transfer-panel__body) {
	height: 400px;
}

:deep(.el-transfer-panel__list.is-filterable) {
	height: 340px;
}

:deep(.coUpload) {
	max-width: 1100px;
}
</style>
