<!-- 招标文件模板 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-search ref="searchRef" inline label-position="left" :model="searchData" :config="searchConfig" :dic="dicData" @change="onChange" @search="onSearchHandle" />
			<co-table ref="dsTableRef" v-loading="handleLoading" :config="tableConfig" :header="tableHeaderNew" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left" />
		</div>
	</div>
</template>

<script setup name="TendersTemplate">
import { getList, changeState, delRpTemplate } from '/@/api/template-library/tender-template.js';
import { dictApi } from '/@/api/dict/index';
import { useHandles } from '/@/hooks/handles';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { searchConfig, tableHeader } from './data';

// 定义变量内容
var onSearch = null;
const router = useRouter();
const { handleConfirm, handleLoading } = useHandles();

// 项目类别字典
const projectCategoryList = [
	{ value: '1', label: '工程项目' },
	{ value: '2', label: '采购项目' },
];

// 搜索表单
const dicData = ref({});
const searchData = ref({});
const tableHeaderNew = tableHeader(onSwitch);

// 表格配置
const tableConfig = ref({
	dic: {
		projectCategory: { data: projectCategoryList }, // 项目类别字典
		bidModeCode: { data: [] }, // 招标/采购方式
		purchaseCatalog: { value: dictApi['purchaseCatalog'] }, // 采购目录字典
		bidMethodType: { value: dictApi['methodName'] }, // 评标办法
		purchaseMode: { value: dictApi['serviceData'] }, // 采购方式
		tenderMode: { value: dictApi['engineering'] }, // 招标方式字典
	},
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getListApi, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {
			templateName: undefined, // 模板名称
			projectCategory: undefined, // 项目类别
			bidModeCode: undefined, // 招标/采购方式
		},
	},
});

onMounted(() => {});

// 获取列表
function getListApi(params) {
	const _params = { ...params };
	if (_params.projectCategory == 1) {
		_params.tenderMode = _params.bidModeCode;
	} else {
		_params.purchaseMode = _params.bidModeCode;
	}

	return getList(_params).then((res) => {
		// 回显招标方式、采购方式、采购目录字典
		res.data.list.forEach((item) => {
			const strList = ['tenderMode', 'purchaseMode', 'purchaseCatalog'];
			strList.forEach((str) => {
				if (item[str]) {
					item[str] = showLabel(item[str], dicData.value[str]);
				} else {
					item[str] = '';
				}
			});
			item.bidModeStr = item.tenderMode + ' ' + item.purchaseMode;
		});
		return res;
	});
}

// 查询
function onSearchHandle(data, type) {
	if (type === 'reset') {
		dicData.value['bidModeCode'] = [];
	}
	onSearch({ params: searchData.value });
}

// 搜索框更改回调
function onChange(prop, value) {
	if (prop === 'projectCategory') {
		let dicList = [];
		if (value == 1) dicList = dicData.value['tenderMode'];
		if (value == 2) dicList = dicData.value['purchaseMode'];
		searchData.value.bidModeCode = undefined;
		dicData.value['bidModeCode'] = dicList;
	}
}

// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}

// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}

// 字典数据回显
function showLabel(arrCode, dicList) {
	const arr = [];
	arrCode = arrCode.split(',');
	arrCode.forEach((item) => {
		const findItem = dicList.find((itemz) => itemz.value === item);
		if (findItem) {
			arr.push(findItem.label);
		}
	});
	return arr.join(',');
}

// switch状态改变
async function onSwitch(row) {
	// 确认框提示
	try {
		await useMessageBox().confirm(row.enabled == 0 ? '确认开启状态？' : '确认关闭状态？');
	} catch {
		return;
	}

	try {
		// 调用开关接口
		await changeState({ id: row.id, enabled: row.enabled == 1 ? 0 : 1 });
		// 开关后提示
		useMessage().success(row.enabled == 0 ? '开启状态成功' : '关闭状态成功');
		return true;
	} catch (err) {
		useMessage().error(err.msg);
		return false;
	}
}

// 监听表格点击操作
function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	switch (field) {
		case 'add':
			router.push({
				path: '/template-library/tenders-template/edit',
			});
			break;
		case 'edit':
			router.push({
				path: '/template-library/tenders-template/edit',
				query: {
					id: row.id,
				},
			});
			break;
		case 'toView':
			router.push({
				path: '/template-library/tenders-template/detail',
				query: {
					id: row.id,
				},
			});
			break;
		case 'delete':
			handleConfirm({
				texts: '是否确认删除？',
				params: { id: row.id, listName: 'getList' },
				request: delRpTemplate,
				refresh: onSearchHandle,
			});
			break;
	}
}
</script>
<style></style>
