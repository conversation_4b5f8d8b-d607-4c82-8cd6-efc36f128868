// 搜索表单配置
export const searchConfig = {
	items: [
		{
			prop: 'templateName',
			type: 'input',
			attrs: {
				placeholder: '请输入模板名称',
				clearable: true,
				label: '模板名称',
			},
		},
		{
			prop: 'projectCategory',
			type: 'select',
			option: 'projectCategory',
			attrs: {
				placeholder: '请选择项目类别',
				clearable: true,
				label: '项目类别',
			},
		},
		{
			prop: 'bidModeCode',
			type: 'select',
			option: 'bidModeCode',
			attrs: {
				placeholder: '请选择招标/采购方式',
				clearable: true,
				label: '招标/采购方式',
			},
		},
	],
};

// table表格
export const tableHeader = (onSwitch) => {
	return [
		{ type: 'index', label: '序号', width: 60, align: 'center' },
		{ prop: 'templateName', label: '模板名称' },
		{ prop: 'projectCategory', label: '项目类别' },
		{ prop: 'bidModeStr', label: '招标/采购方式' },
		{
			prop: 'enabled',
			label: '文件状态',
			type: 'switch',
			width: 100,
			attrs: {
				'active-value': 1,
				'inactive-value': 0,
				'before-change': onSwitch,
			},
		},
	];
};
