<!--招标文件模板查看 -->
<template>
	<div class="layout-padding w100">
		<pro-back-pre title="招标文件模板详情" />
		<div class="layout-padding-view px-[20px] py-[10px]" v-loading="loading">
			<!-- 基本信息 -->
			<pro-side-title title="基本信息"></pro-side-title>
			<data-descriptions :des-data="form" :des-list="desList" :dic-config="dicConfig" class="max-w-[1400px]"></data-descriptions>
			<!-- 公告模板 -->
			<pro-side-title title="公告模板" class="mt-[15px]"></pro-side-title>
			<pro-filelist :file-list-id="form.noticeTemplateFileId"></pro-filelist>
			<!-- 招标文件模板 -->
			<pro-side-title title="招标文件模板" class="mt-[15px]"></pro-side-title>
			<div v-for="(item, index) in files" :key="index">
				<dict-tag class="mb-[15px]" :options="dicConfig.methodName" :value="item.bidMethod"></dict-tag>
				<pro-filelist class="mb-[20px]" :file-list-id="`${item.templateFileId},${item.previewFileId}`"></pro-filelist>
				<co-table :config="tableConfig" :data="item.fieldsTableConfig" :header="tableHeader" align="left" />
			</div>
		</div>
	</div>
</template>
<script setup>
import { getDetail, getFieldList } from '/@/api/template-library/tender-template.js';
import { dictApi } from '/@/api/dict/index';
import { useMessage } from '/@/hooks/message';
import { useDicts } from '/@/hooks/useDicts';

// 引入组件
const dataDescriptions = defineAsyncComponent(() => import('../../components/data-descriptions.vue'));

// 获取字典内容
const dicConfig = {
	...useDicts('engineering', 'serviceData', 'invitationMethod', 'methodName'),
	projectCategory: ref([
		{ value: '1', label: '工程项目' },
		{ value: '2', label: '采购项目' },
	]),
};

// 描述列表
const desList = [
	{ id: 'templateName', name: '模板名称' },
	{ id: 'projectCategory', name: '项目类别', dicKey: 'projectCategory' },
	{ id: 'tenderMode', name: '招标/采购方式', dicKey: 'engineering', relation: { oid: 'projectCategory', val: '1' } },
	{ id: 'purchaseMode', name: '招标/采购方式', dicKey: 'serviceData', relation: { oid: 'projectCategory', val: '2' } },
	{ id: 'invitationMode', name: '邀请方式', dicKey: 'invitationMethod' },
];

// 表格配置
const tableConfig = {
	pagination: false,
};
const tableHeader = [
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{ prop: 'fieldName', label: '字段名称' },
	{ prop: 'fieldEnName', label: '字段值' },
	{ prop: 'labelType', label: '字段标签类型' },
];

// 定义变量内容
const route = useRoute();
const router = useRouter();

const id = ref();
const fieldIds = ref([]); // 选择的字段列表
const fileId = ref(''); // pdf或word的文件id
const files = ref([]); // pdf或word的文件id
const bidMethodList = ref([]); // 步骤类型
const loading = ref(false);
const form = ref({});
const tableData = ref([]);

onMounted(() => {
	init();
	getFieldListApi();
});

// 业务逻辑初始化
function init() {
	id.value = route.query.id;
	loading.value = true;

	getDetail(id.value)
		.then((res) => {
			form.value = res.data;

			// 获取字段列表
			getFieldListApi(res.data.files);
		})
		.catch((err) => {
			useMessage().error(err.msg);
		})
		.finally(() => {
			loading.value = false;
		});
}

// 获取字段列表
function getFieldListApi(_files) {
	getFieldList({
		fieldType: 7,
		businessType: 71,
	}).then((res) => {
		const allTemplateFieldData = {};
		res.data.forEach((ele) => {
			allTemplateFieldData[ele.id] = ele;
		});

		files.value = _files?.map((item) => {
			item.fieldIds.forEach((ele) => {
				tableData.value.push(allTemplateFieldData[ele]);
			});
			item.fieldsTableConfig = tableData.value;
			return item;
		});
	});
}

// 取消
function cancel() {
	router.back(-1);
}
</script>
<style lang="scss" scoped>
.layout-padding-view {
	overflow-y: auto;
}

.detail-submit {
	display: flex;
	justify-content: center;
}

.btn-list {
	display: inline-block;
	margin-right: 10px;
	margin-bottom: 10px;
}
</style>
