<template>
	<list :params="params"></list>
</template>
<script setup name="NoticeTemplate">
import list from '../components/index.vue';

// 搜索表单
const params = {
	tempType: 6, // 公示模板
	tempDic: 'noticeTemp',
	editPath: '/template-library/notice-template/edit',
	searchConfig: {
		items: [
			{
				prop: 'tempName',
				type: 'input',
				attrs: {
					placeholder: '请输入模板名称',
					clearable: true,
					label: '模板名称',
				},
			},
			{
				prop: 'tempBusinessType',
				type: 'select',
				option: 'tempBusinessType',
				attrs: {
					placeholder: '请选择业务类型',
					clearable: true,
					label: '业务类型',
				},
			},
			{
				prop: 'projectCategory',
				type: 'select',
				option: 'projectCategory',
				attrs: {
					placeholder: '请选择项目类别',
					clearable: true,
					label: '项目类别',
				},
			},
			{
				prop: 'bidModeCode',
				type: 'select',
				option: 'bidModeCode',
				attrs: {
					placeholder: '请选择招标/采购方式',
					clearable: true,
					label: '招标/采购方式',
				},
			},
		],
	},
	tableHeader: () => {
		return [
			{ type: 'index', label: '序号', width: 60, align: 'center' },
			{ prop: 'tempName', label: '模板名称' },
			{ prop: 'tempBusinessType', label: '业务类型' },
			{ prop: 'projectCategoryStr', label: '项目类别' },
			{ prop: 'bidModeStr', label: '招标/采购方式' },
		];
	},
};
</script>
<style></style>
