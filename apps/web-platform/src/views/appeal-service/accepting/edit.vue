<template>
	<div class="co-page px-[20px] py-[20px]">
		<pro-back-pre @click="backEmit" title="诉求解决" />
		<div class="layout-padding-view px-[20px] py-[10px]">
			<el-collapse v-model="activeNames">
				<el-collapse-item name="1">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">企业信息</span>
					</template>
					<vert-table :config="formInfoData.InformationList" :data="formInfoData.formData" :column="2"> </vert-table>
				</el-collapse-item>
				<el-collapse-item name="2">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">诉求信息</span>
					</template>
					<vert-table :config="formInfoData.appealList" :data="formInfoData.formData" :column="2"> </vert-table>
					<div class="mt10">
						<span>附件:</span>
					</div>
				</el-collapse-item>
				<el-collapse-item name="3" v-if="!route.query.type">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">诉求解决</span>
					</template>
					<el-form ref="formRef" :model="form" :rules="rules">
						<el-row :gutter="100" type="flex" style="flex-wrap: wrap">
							<el-col :span="24">
								<el-form-item label="处理结果：" prop="resource">
									<el-radio-group v-model="form.resource">
										<el-radio :label="item.label" v-for="item in processingResults" :key="item.label">{{ item.label
										}}</el-radio>
									</el-radio-group>
								</el-form-item></el-col>
							<el-col :span="24">
								<el-form-item label prop="tempContent">
									<pro-editor v-model="form.tempContent" @blur="onBlur" style="width: 100%" />
								</el-form-item>
							</el-col>
						</el-row>
						<div class="text-center py-[20px]">
							<el-button @click="openAuditFu('cancel')">返回</el-button>
							<el-button type="primary" :loading="submitLoading" @click="submit">确定</el-button>
						</div>
					</el-form>
				</el-collapse-item>
			</el-collapse>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/message';
import { getDetail, } from '/@/api/information/theme';
import { tabControl } from '/@/stores/tabControl';
const vertTable = defineAsyncComponent(() => import('../components/vert-table/index.vue'));
// 引入组件
import proEditor from '/@/components/pro-editor/index.vue';
const router = useRouter();
const route = useRoute();
const tid = ref();
const activeNames = reactive(['1', '2', '3']);
// 详情数据
const formInfoData = reactive<any>({
	formData: {
		//详情数据
		auditStatus: 0,
		extendedFields: '{}',
		businessName: "郑州方特电子科技有限公司",
		businessType: "石家庄",
		unifiedSocialCreditCode: "91410100MA3X81M849",
		industryType: "1601040533448721",
		foundAddress: "",
		officeAddress: "41,4101",
		quHuaAddress: "河南省,郑州市",
		detailedAddress: "河南省郑州市高新技术产业开发区金盏街16号亿达科技新城9号楼102-202号",
		businessScope: "电子产品的技术开发、生产及销售；集成电路的设计、研发、生产；销售：电子元器件、机电产品、计算机软硬件、电动汽车配件、电子设备、仪器仪表、电源板、线路板；LED配件的销售及技术服务；电子电器的维修服务。",
	},
	InformationList: [
		{
			title: '企业名称',
			field: 'businessName',
		},
		{
			title: '注册地址',
			field: 'businessName',
		},
		{
			title: '联系人',
			field: 'businessName',
		},
		{
			title: '联系方式',
			field: 'businessName',
		},
		{
			title: '经营范围',
			field: 'businessScope',
			colspan: 3,
		},
	],
	appealList: [
		{
			title: '诉求',
			field: 'businessName',
		},
		{
			title: '提交时间',
			field: 'businessName',
		},
		{
			title: '受理状态',
			field: 'businessName',
		},
		{
			title: '事件发生地',
			field: 'businessName',
		},
		{
			title: '内容描述',
			field: 'businessScope',
			colspan: 3,
		},
	],
});
const tabCon = tabControl();

// 表单
const processingResults = ref([
	{ label: '同意调解', value: 0 },
	{ label: '不同意调解', value: 1 },
	{ label: '无需调解(不在业务范围内)', value: 2 },
]) //处理结果
const rules = ref({
	tempContent: [{ required: true, message: '请输入处理内容', trigger: 'blur' }],
	resource: [{ required: true, message: '请选择处理结果', trigger: 'change' }],
});//校验规则

const formRef = ref();
const form = ref({
	resource: '',//处理结果
	tempContent: '', // 处理内容
});
const submitLoading = ref(false);


onMounted(() => {
	tid.value = route.query.id;
	getInfoDetail();
});

// 回退
const backEmit = () => {
	tabCon.setTenderOrganizationInfoIndexTab(route.query.type as string);
};

// 获取详情
const getInfoDetail = () => {
	getDetail(tid.value).then((res) => {
		let data = res.data;
		formInfoData.formData = res.data;
		if (data.fileJson) {
			let dataList = JSON.parse(data.fileJson);
			dataList.forEach((item: any, index: number) => {
				item.indexSort = index + 1;
			});
		}
	});
};

// 编辑框离焦事件
function onBlur(e) {
	formRef.value?.validateField('tempContent');
}

// 返回
function openAuditFu(type: string) {
	router.back(-1);
}

// 确定
function submit() {
	formRef.value.validate((valid) => {
		if (valid) {
			const forms = { ...form.value };

			// 如果模板内容没有包裹字体 则包裹上 宋体可以解决转换pdf时不识别的问题
			if (forms.tempContent && forms.tempContent.indexOf('custom-template') == -1) {
				forms.tempContent = `<div id="custom-template" style="font-family:'宋体'">${forms.tempContent}</div>`;
			}
			console.log(forms, '11111111111');

			// let apiFun;

			// if (forms.id) {
			// 	apiFun = editortem(forms);
			// } else {
			// 	forms.tempType = props.paramsData.tempType;
			// 	apiFun = saveEditortem(forms);
			// }

			// submitLoading.value = true;
			// apiFun
			// 	.then((res) => {
			// 		useMessage().success('提交成功');
			// 		router.back(-1);
			// 	})
			// 	.catch((err) => {
			// 		useMessage().error(err.msg);
			// 	})
			// 	.finally(() => {
			// 		submitLoading.value = false;
			// 	});
		}
	});
}

</script>

<style lang="scss" scoped>
.layout-padding-view {
	overflow-y: auto;
}

.el-form-item {
	margin-top: 20px;
}

::v-deep .el-form-item__label {
	font-weight: bold;
}
</style>
