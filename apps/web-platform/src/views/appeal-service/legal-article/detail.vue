<template>
	<el-dialog title="详细信息" width="40%" v-model="visible" append-to-body :close-on-click-modal="false"
		:close-on-press-escape="false" @close="cancel()">
		<el-descriptions  size="large" column='1'>
			<el-descriptions-item label="法律标题">{{ detailsData.name }}</el-descriptions-item>
			<el-descriptions-item label="是否上架">{{ detailsData.name }}</el-descriptions-item>
			<el-descriptions-item label="pc端图片">{{ detailsData.name }}</el-descriptions-item>
			<el-descriptions-item label="小程序图片">{{ detailsData.name }}</el-descriptions-item>
			<el-descriptions-item label="法律内容">{{ detailsData.name }}</el-descriptions-item>
		</el-descriptions>
	</el-dialog>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { getGenInfo } from '/@/api/system/number-management';
import { useMessage } from '/@/hooks/message';

const visible = ref(false);
const detailsData = ref({
});

const view = (row) => {
	console.log(row, '111111111111');
	visible.value = true;
	// getGenInfo(row.id).then((res) => {
	// 	if (res.code == 200) {
	// 		detailsData.value = res.data
	// 	} else {
	// 		useMessage().error(res?.msg);
	// 	}
	// })
	// .catch((error) => {
	// 	console.error('Error submitting detailsData:', error);
	// });
};

const cancel = () => {
	visible.value = false;
};



onMounted(() => {
	view
});
defineExpose({
	view
});
</script>
