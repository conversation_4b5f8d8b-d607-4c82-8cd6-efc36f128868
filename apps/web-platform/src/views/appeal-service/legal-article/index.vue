<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="ml10" v-show="showSearch">
				<el-form :inline="true" :model="state.queryForm" @keyup.enter="onSearchHandle" ref="queryRef">
					<el-form-item prop="searchValue">
						<el-input v-model="state.queryForm.searchValue" placeholder="请填写法律标题" />
					</el-form-item>
					<el-form-item prop="raeTypeId">
						<el-select placeholder="请选择上下架" clearable v-model="state.queryForm.raeTypeId">
							<el-option :key="item.id" :label="item.orderTypeName" :value="item.id" v-for="item in receiptsOption" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button @click="onSearchHandle" icon="search" type="primary"> 查询 </el-button>
						<el-button @click="resetQuery" icon="Refresh">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<div class="mb-2.5">
				<el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
			</div>
			<el-table :data="dataList" style="width: 100%" v-loading="state.loading" border :header-cell-style="tableStyle.headerCellStyle">
				<el-table-column label="序号" type="index" width="70" align="center" />
				<el-table-column label="pc端图片" prop="projectName" show-overflow-tooltip />
				<el-table-column label="小程序图片" prop="projectNum" show-overflow-tooltip />
				<el-table-column label="法律标题" prop="orderNum" show-overflow-tooltip />
				<el-table-column label="法律内容" prop="revenueSideName" show-overflow-tooltip />
				<el-table-column prop="status" label="状态">
					<template #default="scope">
						<el-switch v-model="scope.row.status" @change="handleStatusChange(scope.row)" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button type="primary" size="small" link icon="view" @click="handleInfo(scope.row)"> 查看 </el-button>
						<el-button type="primary" size="small" link icon="edit" @click="editFun(scope.row)"> 修改 </el-button>
						<el-button type="danger" size="small" link icon="delete" @click="deleteRow(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>
		<co-preview v-model="previewImgUrl" width="80%" :layer="true" download />

		<el-dialog class="custom-dialog" title="新增" v-model="dialogVisible" width="980px" append-to-body v-loading="loading" :destroy-on-close="true" :close-on-click-modal="false" @closed="resetFun">
			<el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
				<el-form-item label="法律标题" prop="title">
					<el-input v-model="formData.title" placeholder="请填写法律标题"></el-input>
				</el-form-item>
				<el-form-item label="是否上架" prop="status">
					<el-switch v-model="formData.status" />
				</el-form-item>
				<el-form-item label="pc端图片" prop="pcImgUrl">
					<upload-img v-model:id="formData.pcImgUrl" :fileSize="1">
						<template #tip>
							<div>支持格式：.png,.jpg,.jpeg，单个文件不能超过1MB</div>
						</template>
					</upload-img>
				</el-form-item>
				<el-form-item label="小程序图片" prop="imgUrl">
					<upload-img v-model:id="formData.imgUrl" :fileSize="1">
						<template #tip>
							<div>支持格式：.png,.jpg,.jpeg，单个文件不能超过1MB</div>
						</template>
					</upload-img>
				</el-form-item>
				<el-form-item label="法律内容" prop="tempContent">
					<pro-editor v-model="formData.tempContent" @blur="onBlur" style="width: 100%" />
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
			</template>
		</el-dialog>

		<!-- 查看详情 -->
		<CheckDetails ref="checkDetailsRef" />
	</div>
</template>

<script setup lang="ts" name="systemServer">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { raeList, raeStatistics, raeTypeList } from '/@/api/financial/income';
import { getFiles, downLoadFile } from '/@/api/common/upload';
import { useDicts } from '/@/hooks/useDicts';
import { useMessage, useMessageBox } from '/@/hooks/message';
// 引入组件
import proEditor from '/@/components/pro-editor/index.vue';
import CheckDetails from './detail.vue';
const router = useRouter();

const dataList = ref([{ status: 0 }, { status: 1 }]);

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 查看
let previewImgUrl = ref('');

// 变量
const info = ref();
const receiptsOption = ref([]);
const dateArr = ref([]);

const state: BasicTableProps = reactive<BasicTableProps>({
	pageList: raeList,
	props: {
		item: 'list',
		totalCount: 'total',
	},
});

console.log(state, '11111111');
const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref();
const rules = ref({
	title: [{ required: true, message: '请填写法律标题', trigger: 'blur' }],
	tempContent: [{ required: true, message: '请输入处理内容', trigger: 'blur' }],
	pcImgUrl: [{ required: true, message: '请上传pc端图片', trigger: 'blur' }],
	imgUrl: [{ required: true, message: '请上传小程序图片', trigger: 'blur' }],
	status: [{ required: true, message: '请选择是否上架', trigger: 'change' }],
});

// 搜索条件
const queryForm = reactive({
	title: '',
	status: '',
	startTime: '',
	endTime: '',
});
const formData: any = ref({
	id: undefined,
	title: '',
	status: false,
	tempContent: '',
	pcImgUrl: '',
	imgUrl: '',
});
const checkDetailsRef = ref(null);
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle, downBlobFile } = useTable(state);

onMounted(() => {
	// getStatistics();
	// getType();
});

// 获取统计
const getStatistics = async () => {
	const { data } = await raeStatistics();
	info.value = data;
};
// 获取收支类型
const getType = async () => {
	const { data } = await raeTypeList();
	receiptsOption.value = data;
};
// 查询
function onSearchHandle() {
	state.queryForm.startTime = dateArr.value[0];
	state.queryForm.endTime = dateArr.value[1];
	getDataList();
}
// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	state.queryForm.startTime = '';
	state.queryForm.endTime = '';
	dateArr.value = '';
	queryRef.value.resetFields();
	getDataList();
};
// 删除
const deleteRow = (row: any) => {
	useMessageBox()
		.confirm('此操作将永久删除, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {
			console.log(row, 'row');
		});
};
// 查看
const handleInfo = (row: any) => {
	checkDetailsRef.value.view(row);
};
// 解决

// 新增
const handleAdd = () => {
	dialogVisible.value = true;
};

const editFun = (row: any) => {
	dialogVisible.value = true;
};

const handleStatusChange = (row: any) => {
	console.log(row);
};

// 编辑框离焦事件
function onBlur(e) {
	formRef.value?.validateField('tempContent');
}
//清空表单
const resetFun = () => {
	formData.value = {
		id: undefined,
		title: '',
		status: false,
	};
};

const submitForm = () => {
	formRef.value.validate((valid: any) => {
		if (valid) {
			loading.value = true;
			// formData.value.regionCode = formData.value.regionCode.join(',');
			// formData.value.newestClassifyIdList = formData.value.newestClassifyIdList.join(',');
			// if (formData.value.id) {
		}
	});
};
</script>
<style scoped lang="scss">
.card {
	width: 300px;
	text-align: center;

	div:last-child {
		color: #2e5cf6;
		font-weight: bold;
		font-size: 18px;
		margin-top: 10px;
	}
}
</style>
