<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="ml10" v-show="showSearch">
				<el-form :inline="true" :model="state.queryForm" @keyup.enter="onSearchHandle" ref="queryRef">
					<el-form-item prop="searchValue">
						<el-input v-model="state.queryForm.searchValue" placeholder="请填写企业名称" />
					</el-form-item>
					<el-form-item prop="searchValue">
						<el-input v-model="state.queryForm.searchValue" placeholder="请填写联系方式" />
					</el-form-item>
					<el-form-item prop="dateArr">
						<el-date-picker key="dateArr" v-model="dateArr" type="daterange" value-format="YYYY-MM-DD"
							range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期" />
					</el-form-item>
					<!-- <el-form-item prop="searchKey">
						<el-select placeholder="请选择" clearable v-model="state.queryForm.searchKey">
							<el-option :key="item.dicValue" :label="item.dicName" :value="item.dicValue" v-for="item in financeRaePageSearch" />
						</el-select>
					</el-form-item> -->
					<el-form-item prop="raeTypeId">
						<el-select placeholder="请选择状态" clearable v-model="state.queryForm.raeTypeId">
							<el-option :key="item.id" :label="item.orderTypeName" :value="item.id" v-for="item in receiptsOption" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button @click="onSearchHandle" icon="search" type="primary"> 查询 </el-button>
						<el-button @click="resetQuery" icon="Refresh">重置</el-button>
						<el-button icon="Download" @click="exportExcel">导出</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-table :data="dataList" style="width: 100%" v-loading="state.loading" border @selection-change="selectedInfo"
				:header-cell-style="tableStyle.headerCellStyle">
				<!-- <el-table :data="state.dataList" style="width: 100%" v-loading="state.loading" border @selection-change="selectedInfo" :header-cell-style="tableStyle.headerCellStyle"> -->
				<el-table-column type="selection" width="55" />
				<el-table-column label="序号" type="index" width="70" align="center" />
				<el-table-column label="企业名称" prop="projectName" show-overflow-tooltip />
				<el-table-column label="诉求" prop="projectNum" show-overflow-tooltip />
				<el-table-column label="事件发生地" prop="orderNum" show-overflow-tooltip />
				<el-table-column label="内容描述" prop="revenueSideName" show-overflow-tooltip />
				<el-table-column label="联系人" prop="expenditureSideName" show-overflow-tooltip />
				<el-table-column label="联系方式" prop="raeAmount" show-overflow-tooltip />
				<el-table-column label="提交时间" prop="transactionTypeName" show-overflow-tooltip />
				<el-table-column label="受理状态" prop="status" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.status == 0" type="danger">未受理</el-tag>
						<el-tag v-if="scope.row.status == 1" type="warning">受理中</el-tag>
						<el-tag v-if="scope.row.status == 2" type="success">已受理</el-tag>
						<el-tag v-if="scope.row.status == 3" type="info">已关闭</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button type="primary" size="small" link icon="view" @click="handleInfo(scope.row)"> 详情 </el-button>
						<!-- <el-button type="primary" size="small" link icon="Download" @click="handleDown(scope.row)"> 下载 </el-button> -->
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>
		<co-preview v-model="previewImgUrl" width="80%" :layer="true" download />
	</div>
</template>

<script setup lang="ts" name="systemServer">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { raeList, raeStatistics, raeTypeList, raeOrderExport } from '/@/api/financial/income';
import { getFiles, downLoadFile } from '/@/api/common/upload';
import { useDicts } from '/@/hooks/useDicts';
const router = useRouter();

const dataList = ref([
	{ status: 0 },
	{ status: 1 },
	{ status: 2 },
	{ status: 3 },
]);

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 查看
let previewImgUrl = ref('');

let selectTableList = ref('');
// 变量
const info = ref();
const receiptsOption = ref([]);
const dateArr = ref([]);

// 引入字典
const { financeRaePageSearch } = useDicts('financeRaePageSearch');

const state: BasicTableProps = reactive<BasicTableProps>({
	pageList: raeList,
	props: {
		item: 'list',
		totalCount: 'total',
	},
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle, downBlobFile } = useTable(state);

onMounted(() => {
	// getStatistics();
	// getType();
});

// 获取统计
const getStatistics = async () => {
	const { data } = await raeStatistics();
	info.value = data;
};
// 获取收支类型
const getType = async () => {
	const { data } = await raeTypeList();
	receiptsOption.value = data;
};
// 查询
function onSearchHandle() {
	state.queryForm.startTime = dateArr.value[0];
	state.queryForm.endTime = dateArr.value[1];
	getDataList();
}
// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	state.queryForm.startTime = '';
	state.queryForm.endTime = '';
	dateArr.value = '';
	queryRef.value.resetFields();
	getDataList();
};
// 下载
const handleDown = (row: any) => {
	downLoadFile(row.voucher);
};
// 查看
const handleInfo = (row: any) => {
	router.push({ path: '/appealService/appealManagement/details', query: { id: 1 } });
	// getFiles([row.voucher]).then(({ data }) => {
	// 	previewImgUrl.value = data[0].fileUrl;
	// });
};
// 导出
const exportExcel = async () => {
	state.queryForm.ids = selectTableList.value.lenght ? selectTableList.value.join('') : '';
	downBlobFile('/brace/raeOrder/export', state.queryForm, '收支明细.xlsx');
};
// 多选事件
const selectedInfo = (objs: { id: string }[]) => {
	selectTableList.value = objs.map(({ id }) => id);
};
</script>
<style scoped lang="scss">
.card {
	width: 300px;
	text-align: center;

	div:last-child {
		color: #2e5cf6;
		font-weight: bold;
		font-size: 18px;
		margin-top: 10px;
	}
}
</style>
