<template>
	<div class="co-page px-[20px] py-[20px]">
		<pro-back-pre @click="backEmit" title="诉求管理详情" />
		<div class="layout-padding-view px-[20px] py-[10px]">
			<el-collapse v-model="activeNames" @change="handleChange">
				<el-collapse-item name="1">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">企业信息</span>
					</template>
					<vert-table :config="form.InformationList" :data="form.formData" :column="2">
						<!-- <template #currency>
							{{ '人民币' }}
						</template>
						<template #industryType>
							{{ JSON.parse(form.formData.extendedFields).industryTypeStr }}
						</template>
						<template #businessType>
							{{ JSON.parse(form.formData.extendedFields).businessTypeStr }}
						</template>
						<template #legalPersonIdType="{ field }">
							{{legalCode.find((v) => v.dicValue == form.formData[field])?.dicName || ''}}
						</template> -->
					</vert-table>
				</el-collapse-item>
				<el-collapse-item name="2">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">诉求信息</span>
					</template>
					<vert-table :config="form.appealList" :data="form.formData" :column="2">

					</vert-table>

					<div class="mt10">
						<span>附件:</span>
					</div>
				</el-collapse-item>
				<el-collapse-item name="3" v-if="!route.query.type">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">诉求解决</span>
					</template>
					<vert-table :config="form.resolutionList1" :data="form.formData" :column="2"> </vert-table>
					<vert-table :config="form.resolutionList2" :data="form.formData" :column="2"> </vert-table>
					<vert-table :config="form.resolutionList3" :data="form.formData" :column="2"> </vert-table>
				</el-collapse-item>
			</el-collapse>
			<div class="text-center py-[20px]">
				<el-button @click="openAuditFu('cancel')">返回</el-button>
				<el-button type="primary" @click="passAuditFu(true, form.formData.id)">受理诉求</el-button>
				<!-- <template v-if="form.formData.auditStatus === 1 && route.query.type === 'examine'">
					<el-button type="primary" @click="passAuditFu(true, form.formData.id)">通过</el-button>
					<el-button @click="passAuditFu(false, form.formData.id)">拒绝</el-button>
				</template> -->
			</div>
		</div>
		<!-- 预览 -->
		<co-preview v-if="previewImgUrl" v-model="previewImgUrl" :layer="true" />
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
import { getDetail, flowRecord, complete, reject } from '/@/api/information/theme';
import { getFiles } from '/@/api/common/upload.js';
import { useDicts } from '/@/hooks/useDicts';
import { tabControl } from '/@/stores/tabControl';
// import vertTable from '../../../components/vert-table/index.vue';
const vertTable = defineAsyncComponent(() => import('../components/vert-table/index.vue'));
const router = useRouter();
const route = useRoute();
const tid = ref();
const activities = ref(); // 操作记录
const activeNames = reactive(['1', '2', '3']);
const { legalCode }: any = useDicts('legalCode');
const previewImgUrl = ref('');
// 详情数据
const form = reactive<any>({
	formData: {
		//详情数据
		auditStatus: 0,
		extendedFields: '{}',

		businessName: "郑州方特电子科技有限公司",
		businessType: "石家庄",
		unifiedSocialCreditCode: "91410100MA3X81M849",
		industryType: "1601040533448721",
		foundAddress: "",
		officeAddress: "41,4101",
		quHuaAddress: "河南省,郑州市",
		detailedAddress: "河南省郑州市高新技术产业开发区金盏街16号亿达科技新城9号楼102-202号",
		businessScope: "电子产品的技术开发、生产及销售；集成电路的设计、研发、生产；销售：电子元器件、机电产品、计算机软硬件、电动汽车配件、电子设备、仪器仪表、电源板、线路板；LED配件的销售及技术服务；电子电器的维修服务。",
	},
	InformationList: [
		{
			title: '企业名称',
			field: 'businessName',
		},
		{
			title: '注册地址',
			field: 'businessName',
		},
		{
			title: '联系人',
			field: 'businessName',
		},
		{
			title: '联系方式',
			field: 'businessName',
		},
		{
			title: '经营范围',
			field: 'businessScope',
			colspan: 3,
		},
	],
	appealList: [
		{
			title: '诉求',
			field: 'businessName',
		},
		{
			title: '提交时间',
			field: 'businessName',
		},
		{
			title: '受理状态',
			field: 'businessName',
		},
		{
			title: '事件发生地',
			field: 'businessName',
		},
		{
			title: '内容描述',
			field: 'businessScope',
			colspan: 3,
		},
	],
	resolutionList1: [
		{
			title: '受理律师',
			field: 'businessName',
		},
		{
			title: '解决时间',
			field: 'businessName',
		},
	],
	resolutionList2: [
		{
			title: '处理结果',
			field: 'businessScope',
			colspan: 3,
		},
	],
	resolutionList3: [
		{
			title: '处理内容',
			field: 'businessScope',
			colspan: 3,
		},
	],
	tableData: {
		//企业资料信息表格数据
		dataList: [],
	},
});
const tabCon = tabControl();
onMounted(() => {
	tid.value = route.query.id;
	getInfoDetail();
});
const backEmit = () => {
	tabCon.setTenderOrganizationInfoIndexTab(route.query.type as string);
};
const getInfoDetail = () => {
	getDetail(tid.value).then((res) => {
		res.data.ifBusnissText = res.data.ifBusiness === 0 ? '短期' : '长期';

		let data = res.data;
		form.formData = res.data;
		// form.formData.ifBusnissText = (data.ifBusiness === 0 ? '短期' : '长期');
		// 企业资料信息
		if (data.fileJson) {
			let dataList = JSON.parse(data.fileJson);
			dataList.forEach((item: any, index: number) => {
				item.indexSort = index + 1;
			});
			form.tableData.dataList = dataList;
		}
		getFlowRecord(data.id);
	});
};
// 操作记录
const getFlowRecord = (id: string) => {
	flowRecord({ businessKey: id, desc: true }).then((res) => {
		activities.value = res.data;
	});
};
const handleChange = (val: any) => {
	console.log(val);
};
// 预览
function review(index: number, row: any) {
	previewFUn(row.attachmentId);
}
function previewFUn(attachmentId: string): void {
	getFiles([attachmentId]).then((res: any) => {
		const obj = res.data[0];
		previewImgUrl.value = obj.fileUrl;
	});
}
function openAuditFu(type: string) {
	if (type === 'cancel') {
		router.replace('/appealService/appealManagement/index');
	}
}
// 受理诉求
function passAuditFu(type: boolean, id: string) {
	router.replace('/appealService/acceptingAppeals/edit');
}
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}

.detail-container {
	padding: 20px;
	border-radius: 8px;
	background-color: #fff;
}
</style>
