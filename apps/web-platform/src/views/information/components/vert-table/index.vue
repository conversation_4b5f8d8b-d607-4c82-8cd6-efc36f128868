<template>
	<div class="vert-table">
		<table border="1" cellspacing="0" cellpadding="0">
			<tbody>
				<tr class="vert-table__row" v-for="(item, index) in _config" :key="index">
					<template v-for="(innerItem, v) in item" :key="v">
						<th class="vert-table__row__title" :style="{ width: thWidth + 'px' }">{{ innerItem.title }}</th>
						<td class="vert-table__row__content" :style="{ width: tdWidth ? tdWidth + 'px' : 'auto' }" :colspan="innerItem.colspan || 1">
							<slot v-if="innerItem.field" :name="innerItem.field" :field="innerItem.field" :data="data">{{ data[innerItem.field] }}</slot>
						</td>
					</template>
				</tr>
			</tbody>
		</table>
	</div>
</template>
<script lang="ts" setup name="vertTable">
interface IConfig {
	title: string;
	field: string;
	colspan?: number;
	hidden?: string;
}
import { computed } from 'vue';

const { config, data, column } = defineProps({
	thWidth: {
		type: [Number],
		default: 180,
	},
	tdWidth: {
		type: [Number, String],
		default: '',
	},
	config: {
		type: [Array<IConfig>],
		default: () => [],
	},
	data: {
		type: [Object],
		default: () => ({}),
	},
	column: {
		type: [Number],
		default: 2,
	},
});
const _config = computed(() => {
	let filterArr = [];
	const neArr = [];
	// 过滤掉隐藏的列
	for (let u = 0; u < config.length; u++) {
		if (!config[u].hidden) {
			filterArr.push(config[u]);
		}
	}
	for (let i = 0; i < filterArr.length; i += column) {
		neArr.push(filterArr.slice(i, i + column));
	}
	return neArr;
});
</script>

<style lang="scss" scoped>
.vert-table {
	table {
		width: 100%;
	}
	table,
	th.vert-table__row__title,
	td.vert-table__row__content {
		border: 1px solid var(--el-border-color-lighter);
		padding: 8px 11px;
		border-collapse: collapse; /* 移除单元格之间的间隔 */
		text-align: left;
	}
	th.vert-table__row__title {
		height: 45px;
		font-weight: 700;
		color: var(--el-text-color-regular);
		background: var(--el-fill-color-light);
	}
	td.vert-table__row__content {
		color: var(--el-text-color-primary);
	}
}
</style>
