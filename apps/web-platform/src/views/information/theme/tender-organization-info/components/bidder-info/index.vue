<template>
	<div class="layout-padding w-100">
		<div class="layout-padding-auto no-t-l-radius">
			<co-table :header="tableHeader" :config="tableConfig" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left">
				<template #operation="{ row }">
					<el-button text type="primary" @click="onOperation({ field: 'toView', row })"
						><el-icon class="mx-[6px]"><View /></el-icon>详情</el-button
					>
				</template>
			</co-table>
		</div>
	</div>
</template>

<script setup lang="ts">
import { getList } from '/@/api/information/theme';
import { ref } from 'vue';
import { tableHeader, dic } from './data.js';
const emit = defineEmits(['onDicLoaded', 'onTableLoad']);
const router = useRouter();
const props = defineProps({
	searchData: {
		type: Object,
		default: () => ({}),
	},
});
// 表格配置
let tableConfig = ref({
	dic,
	operation: {
		fixed: 'right',
		width: 110,
		// list: [
		// 	{ className: 'text-primary', type: 'text', name: '查看', mark: 'toView', inTable: 1, rule: '' },
		// ],
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: { ...props.searchData.value, roleType: 'IdentityRole_8' }, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
	},
});
// table加载完成回调
function onTableLoad({ getDataList }: any) {
	emit('onTableLoad', { getDataList });
}
// 字典加载完毕
function onDicLoaded(data: any) {
	emit('onDicLoaded', data);
}
// 监听表格搜索操作
function onOperation({ field, row }: any) {
	switch (field) {
		case 'toView':
			router.push({
				path: '/information/theme/tender-organization-info/details/bidder-detail',
				query: {
					id: row.id,
					type: 'third',
				},
			});
			break;
	}
}
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}
.w-100 {
	width: 100%;
}
</style>
