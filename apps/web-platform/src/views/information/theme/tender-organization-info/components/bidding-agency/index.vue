<template>
	<div class="layout-padding w-100">
		<div class="layout-padding-auto no-t-l-radius">
			<co-table :header="tableHeader" :config="tableConfig" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" @expand-change="onExpandChange" align="left">
				<template #expand="{ row }">
					<div class="layout-padding-auto">
						<!-- {{ row.childData.dataList }} -->
						<el-table class="co-table" :data="row?.childData?.dataList || []" border max-height="500" style="width: 100%" v-loading="row.childData.loading">
							<el-table-column prop="indexSort" label="子类信息" min-width="80" />
							<el-table-column prop="businessName" label="招标代理名称" min-width="230" />
							<el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码" min-width="230" />
							<el-table-column prop="legalPersonName" label="企业法人" min-width="180" />
							<el-table-column prop="businessContactName" label="联系人" min-width="180" />
							<el-table-column prop="businessContactPhone" label="联系方式" min-width="180" />
							<el-table-column prop="auditStatus" label="状态" min-width="120" #="{ row }">
								<span v-if="row.auditStatus === 0" class="color-info">草稿</span>
								<span v-else-if="row.auditStatus === 1">待审核</span>
								<span v-else-if="row.auditStatus === 2" class="color-success">审核通过</span>
								<span v-else-if="row.auditStatus === 3" class="color-danger">审核失败</span>
							</el-table-column>
							<el-table-column prop="submitTime" min-width="180" label="审核时间" />
						</el-table>
					</div>
				</template>
			</co-table>
		</div>
	</div>
</template>

<script setup>
import { getList, businessInfoAlter } from '/@/api/information/theme';
import { ref } from 'vue';
import { tableHeader, dic } from './data.js';
const emit = defineEmits(['onDicLoaded', 'onTableLoad']);
const router = useRouter();
const props = defineProps({
	searchData: {
		type: Object,
		default: () => ({}),
	},
});
// 表格配置
let tableConfig = ref({
	dic,
	operation: {
		fixed: 'right',
		// width: 220,
		// list: [
		// 	{ className: 'text-primary', type: 'text', name: '详情', mark: 'toView', inTable: 1, rule: '' },
		// 	{ className: 'text-primary', type: 'text', name: '审核', mark: 'examine', inTable: 1, rule: 'row.auditStatus===1' }
		// ],
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: { ...props.searchData.value, roleType: 'IdentityRole_2' }, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
	},
});
// table加载完成回调
function onTableLoad({ getDataList }) {
	emit('onTableLoad', { getDataList });
}
// 字典加载完毕
function onDicLoaded(data) {
	emit('onDicLoaded', data);
}
// 监听表格搜索操作
function onOperation({ field, row }) {
	switch (field) {
		case 'toView':
			router.push({
				path: '/information/theme/tender-organization-info/details/index',
				query: {
					id: row.id,
					type: 'first',
				},
			});
			break;
		case 'examine':
			router.push({
				path: '/information/theme/tender-organization-info/details/index',
				query: {
					id: row.id,
					type: 'examine',
				},
			});
			break;
	}
}
// 监听表格展开操作
function onExpandChange(row) {
	if (row) {
		// this.rowId = row.id
		if (row?.childData?.dataList) return;
		businessInfoAlterFun(row.id, row);
	}
}
// 获取子级表格数据
function businessInfoAlterFun(id, row) {
	row.childData = {
		loading: true,
		dataList: [],
	};
	businessInfoAlter({ biId: id })
		.then((res) => {
			row.childData.loading = false;
			if (res.data) {
				res.data.forEach((item, index) => {
					item.indexSort = index + 1;
				});
			}
			row.childData.dataList = res.data || [];
		})
		.catch(() => {
			row.childData.loading = false;
		});
}
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}
.w-100 {
	width: 100%;
}
</style>
