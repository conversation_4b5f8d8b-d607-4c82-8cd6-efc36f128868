import { dictApi } from '/@/api/dict/index';
// table表格
export const tableHeader = [
	{ type: 'index', label: '排序', width: 60, align: 'center' },
	{ prop: 'businessName', label: '企业名称', 'min-width': 250, showOverflowTooltip: true },
	{ prop: 'unifiedSocialCreditCode', label: '统一社会信用代码', 'min-width': 250, showOverflowTooltip: true },
	{ prop: 'businessType', label: '单位性质', 'min-width': 180, showOverflowTooltip: true },
	{ prop: 'businessContactName', label: '联系人', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'businessContactPhone', label: '联系方式', 'min-width': 150, showOverflowTooltip: true },
	{ prop: 'auditStatus', label: '状态', 'min-width': 100, showOverflowTooltip: true },
	{ prop: 'submitTime', label: '审核时间', 'min-width': 200, showOverflowTooltip: true },
];
// 字典
export const dic = {
	auditStatus: { value: dictApi['businessStatus'], color: { 0: 'color-info', 2: 'color-success', 3: 'color-danger' } },
};
