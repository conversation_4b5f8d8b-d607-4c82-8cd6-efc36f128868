<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<co-search ref="searchRef" inline :model="searchData" label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<el-tabs v-model="tabCon.tenderOrganizationInfoIndexTab" class="infomation-tabs" @tab-click="handleClick">
				<el-tab-pane label="招标代理机构信息" name="first" style="width: 100%; height: 100%">
					<bidding-agency v-if="tabCon.tenderOrganizationInfoIndexTab === 'first'" ref="biddingAgencyListRef" :searchData="searchData" @onTableLoad="onTableLoad" @onDicLoaded="onDicLoaded" />
				</el-tab-pane>
				<el-tab-pane label="招标人信息" name="second">
					<tender-info v-if="tabCon.tenderOrganizationInfoIndexTab === 'second'" ref="tenderInfoListRef" :searchData="searchData" @onTableLoad="onTableLoad" @onDicLoaded="onDicLoaded" />
				</el-tab-pane>
				<el-tab-pane label="投标人信息" name="third">
					<bidder-info v-if="tabCon.tenderOrganizationInfoIndexTab === 'third'" ref="bidderInfoListRef" :searchData="searchData" @onTableLoad="onTableLoad" @onDicLoaded="onDicLoaded" />
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup name="tenderOrganizationInfo">
import biddingAgency from './components/bidding-agency/index.vue';
import tenderInfo from './components/tender-info/index.vue';
import bidderInfo from './components/bidder-info/index.vue';
import { tabControl } from '/@/stores/tabControl';
import { ref } from 'vue';
import { searchConfig } from './data.js';
var onSearch = null;
// 搜索表单
const dicData = ref({});
let searchData = ref({
	businessName: '',
	unifiedSocialCreditCode: '',
	auditStatus: '',
});

const tabCon = tabControl();
// onUnmounted(() => {
// 	mittBus.off('tenderOrganizationInfoIndex');
// });

const searchRef = ref();
// 查询
function onSearchHandle(data) {
	onSearch({ params: data });
}
// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}
// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}
const handleClick = (tab, event) => {
	searchData.value.businessName = '';
	searchData.value.unifiedSocialCreditCode = '';
	searchData.value.auditStatus = '';
};
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}
.infomation-tabs {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.infomation-tabs ::v-deep .el-tabs__content {
	padding: 32px;
	//color: #6b778c;
	//font-size: 32px;
	//font-weight: 600;
	flex: auto;
}
</style>
