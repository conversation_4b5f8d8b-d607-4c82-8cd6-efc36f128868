<template>
	<div class="co-page px-[20px] py-[20px]">
		<pro-back-pre @click="backEmit" title="投标人详情" />
		<div class="layout-padding-view px-[20px] py-[10px]">
			<el-collapse v-model="activeNames">
				<el-collapse-item name="1">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">工单申请详情</span>
					</template>
					<vert-table :config="form.list" :data="form.formData" :column="3">
						<template #creditRate="{ field }">
							{{ CreditRate.find((v) => v.dicValue == form.formData[field])?.dicName || '' }}
						</template>
						<template #businessType="{ field }">
							{{ businessType.find((v) => v.dicValue == form.formData[field])?.dicName || '' }}
						</template>
					</vert-table>
				</el-collapse-item>
				<el-collapse-item name="2">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">三证合一扫描件</span>
					</template>
					<el-image :src="previewImgUrl" fit="cover" :preview-src-list="[previewImgUrl]" style="width: 170px; height: 170px" />
				</el-collapse-item>
				<el-collapse-item name="3">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">账户信息</span>
					</template>
					<!-- 账户信息 -->
					<vert-table :config="form.accountInformation" :data="form.formData" :column="3"></vert-table>
				</el-collapse-item>
				<el-collapse-item name="4">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">企业联系人</span>
					</template>
					<!-- 企业联系人 -->
					<vert-table :config="form.businessContact" :data="form.formData" :column="3">
						<template #businessContactIdType="{ field }">
							<!-- 申报人证件类型 -->
							{{ legalCode.find((v) => v.dicValue == form.formData[field])?.dicName || '' }}
						</template>
					</vert-table>
				</el-collapse-item>
			</el-collapse>
			<div class="text-center py-[20px]">
				<!-- <el-button @click="openAuditFu('cancel')">返回</el-button> -->
				<template v-if="form.formData.auditStatus === 1">
					<el-button type="danger" @click="passAuditFu(false, form.formData.id)">审核不通过</el-button>
					<el-button type="primary" @click="passAuditFu(true, form.formData.id)">审核通过</el-button>
				</template>
			</div>
		</div>
		<!-- 审核意见弹框 -->
		<el-dialog v-model="openAuditDialog" title="审核意见" width="50%" center>
			<el-form ref="auditFormRef" :model="auditForm" :rules="rules" label-width="auto">
				<el-form-item prop="comment">
					<el-input v-model="auditForm.comment" type="textarea" :rows="5" placeholder="请输入审核意见" />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="openAuditDialog = false">取 消</el-button>
					<el-button type="primary" @click="refuseFu">提 交</el-button>
				</span>
			</template>
		</el-dialog>
		<!-- 预览 -->
		<!-- <co-preview v-if="previewImgUrl" v-model="previewImgUrl" :layer="true" /> -->
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
import { getDetail, getCodeName, complete, reject } from '/@/api/information/theme';
import { getFiles } from '/@/api/common/upload.js';
import { useDicts } from '/@/hooks/useDicts';
// import vertTable from '../../../components/vert-table/index.vue';
import { GET_NAME_BY_CODE } from '/@/api/common/constants.js';
import { getDicTionaryAndValue } from '/@/api/common/dic_response.js';
import { dictApi } from '/@/api/dict/index';
import { tabControl } from '/@/stores/tabControl';
const vertTable = defineAsyncComponent(() => import('../../../components/vert-table/index.vue'));
// const router = useRouter();
const route = useRoute();
const tid = ref();
const auditFormRef = ref();
const openAuditDialog = ref(false); //审核弹窗
const auditForm = reactive({
	comment: '',
}); //审核表单
const rules = reactive({
	comment: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
});
const activeNames = reactive(['1', '2', '3', '4']);
const { legalCode, CreditRate, businessType }: any = useDicts('legalCode', 'CreditRate', 'businessType');
const previewImgUrl = ref('');
let registerAddress: any = [];
// 详情数据
const form = reactive<any>({
	formData: {
		//详情数据
		auditStatus: 0,
		extendedFields: '{}',
		officeAddress: {},
	},
	list: [
		{
			title: '单位名称',
			field: 'businessName',
		},
		{
			title: '统一社会信用代码',
			field: 'unifiedSocialCreditCode',
		},
		{
			title: '法定代表人',
			field: 'legalPersonName',
		},
		{
			title: '法人电话',
			field: 'legalPersonPhone',
		},
		{
			title: '单位性质',
			field: 'businessType',
		},
		{
			title: '行业类型',
			field: 'industryType1',
		},
		{
			title: '注册资本(万元)',
			field: 'amount',
		},
		{
			title: '单位所在地',
			field: 'officeAddress',
		},
		{
			title: '资信等级',
			field: 'creditRate',
		},
		{
			title: '邮编',
			field: 'zipCode',
		},
		{
			title: 'CA证书编号',
			field: 'caCode',
		},
		{
			title: '详细地址',
			field: 'detailedAddress',
		},
		{
			title: '经营范围',
			field: 'businessScope',
			colspan: 5,
		},
	],
	accountInformation: [
		{
			title: '开户银行名称',
			field: 'businessBank',
		},
		{
			title: '开户银行账号',
			field: 'businessBankCode',
		},
		{
			title: '基本账户',
			field: 'basicBankCode',
			colspan: 5,
		},
	],
	businessContact: [
		{ title: '信息申报责任人', field: 'businessContactName' },
		{ title: '申报人电话', field: 'businessContactPhone' },
		{ title: '申报人证件号类型', field: 'businessContactIdType' },
		{ title: '申报人证件号', field: 'businessContactIdCode' },
		{ title: '电子邮箱', field: 'businessEmail' },
		{ title: '联系地址', field: 'businessContactAddress', colspan: 5 },
	],
	tableData: {
		//企业资料信息表格数据
		dataList: [],
	},
});
const tabCon = tabControl();
onMounted(() => {
	tid.value = route.query.id;
	// 获取字典内容
	getAddress();
	getInfoDetail();
});
const backEmit = () => {
	tabCon.setTenderOrganizationInfoIndexTab(route.query.type as string);
};
const getAddress = async () => {
	let res = await getDicTionaryAndValue(dictApi.registerAddres);
	registerAddress = res;
};
const getInfoDetail = () => {
	getDetail(tid.value).then((res) => {
		// res.data.ifBusnissText=(res.data.ifBusiness === 0 ? '短期' : '长期');

		let data = res.data;
		form.formData = res.data;
		if (res.data.officeAddress) {
			let addr = getOfficeAddress(res.data.officeAddress);
			form.formData.officeAddress = addr;
		}
		form.formData.a12 = res.data.busnissStartTime + '-' + res.data.busnissEndTime;
		// 获取图片地址
		if (res.data.fileIds) {
			getCurrentFiles([data.fileIds]);
		}
		// 获取行业类型
		if (res.data.industryType) {
			const code = res.data.industryType.split(',').join('-');
			getCodeName({ code: code, atiId: GET_NAME_BY_CODE.industry.atiId }).then(({ data }) => {
				form.formData.industryType1 = data;
			});
		}
	});
};
const getOfficeAddress = (val: string) => {
	if (val.includes(',')) {
		let str = '';
		const code = val.split(',');
		registerAddress[0]?.children.forEach((item: any) => {
			if (item.dicValue == code[0]) {
				str += item.dicName;
				item?.children.forEach((item1: any) => {
					if (item1.dicValue == code[1]) {
						str += item1.dicName;
					}
				});
			}
		});
		return str;
	}
};

// 获取图片地址
function getCurrentFiles(data: any) {
	getFiles(data).then((res: any) => {
		previewImgUrl.value = res.data[0]?.fileUrl || '';
	});
}
// function openAuditFu(type: string) {
// 	if (type === 'cancel') {
// 		router.replace('/information/theme/tender-organization-info/index');
// 	}
// }
// 审核
function passAuditFu(type: boolean, id: string) {
	if (type) {
		ElMessageBox.confirm(`此操作将审核通过, 是否继续?`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}).then(() => {
			complete({ businessKey: id })
				.then((res) => {
					if (res.code == 200) {
						useMessage().success(res.msg);
						getInfoDetail();
						// router.back()
					} else {
						useMessage().error(res.msg);
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '服务器错误');
				});
		});
	} else {
		openAuditDialog.value = true;
	}
}
function refuseFu() {
	auditFormRef.value.validate((valid: boolean) => {
		if (valid) {
			ElMessageBox.confirm(`此操作将审核不通过, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					reject({ ...auditForm, businessKey: tid.value })
						.then((res) => {
							useMessage().success(res.msg || '操作成功');
							openAuditDialog.value = false;
							getInfoDetail();
						})
						.catch((err) => {
							useMessage().error(err.msg || '服务器错误');
						});
				})
				.catch(() => {
					useMessage().info('已取消');
				});
		}
	});
}
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}
.detail-container {
	padding: 20px;
	border-radius: 8px;
	background-color: #fff;
}
</style>
