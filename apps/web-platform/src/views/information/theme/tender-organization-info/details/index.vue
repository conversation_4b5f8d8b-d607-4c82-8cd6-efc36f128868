<template>
	<div class="co-page px-[20px] py-[20px]">
		<pro-back-pre @click="backEmit" :title="route.query.type == 'first' ? '招标代理机构详情' : '招标人详情'" />
		<div class="layout-padding-view px-[20px] py-[10px]">
			<el-collapse v-model="activeNames" @change="handleChange">
				<el-collapse-item name="1">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">基本信息</span>
					</template>
					<vert-table :config="form.list" :data="form.formData" :column="2">
						<template #currency>
							{{ '人民币' }}
						</template>
						<template #industryType>
							{{ JSON.parse(form.formData.extendedFields).industryTypeStr }}
						</template>
						<template #businessType>
							{{ JSON.parse(form.formData.extendedFields).businessTypeStr }}
						</template>
						<template #legalPersonIdType="{ field }">
							{{ legalCode.find((v) => v.dicValue == form.formData[field])?.dicName || '' }}
						</template>
					</vert-table>
				</el-collapse-item>
				<el-collapse-item name="2">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">企业资料信息</span>
					</template>
					<el-table class="co-table" :data="form.tableData.dataList || []" border style="width: 100%">
						<el-table-column prop="indexSort" label="排序" width="60" align="center" />
						<el-table-column prop="documentName" label="资料名称" />
						<el-table-column fixed="right" label="操作" width="120">
							<template #default="scope">
								<el-button link type="primary" size="small" @click.prevent="review(scope.$index, scope.row)">预览</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-collapse-item>
				<el-collapse-item name="3" v-if="!route.query.type">
					<template #title>
						<span class="text-[#333] font-bold text-[18px]">操作记录</span>
					</template>
					<!-- 操作记录 -->
					<div class="block">
						<el-timeline>
							<el-timeline>
								<el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.createTime" placement="top">
									<el-card>
										<h4 class="text-[#333] font-bold text-[14px]">
											{{ activity.assigneeName ? `(${activity.assigneeName})` : '' }}
											{{ activity.businessOrgName ? `(${activity.businessOrgName})` : '' }}
											<span class="valueText">{{ activity.jbr ? `(${activity.jbr})` : '' }}</span>
										</h4>
										<p>
											事项：
											<span v-if="activity.comment" class="valueText1">{{ activity.businessName }}</span>
										</p>
										<p>
											审核结果：
											<span v-if="activity.comment" class="valueText2">{{ activity.comment.commentRemark }}</span>
										</p>
										<p>
											备注：
											<span v-if="activity.comment">{{ activity.comment.comment }}</span>
										</p>
									</el-card>
								</el-timeline-item>
							</el-timeline>
						</el-timeline>
					</div>
				</el-collapse-item>
			</el-collapse>
			<div class="text-center py-[20px]">
				<!-- <el-button @click="openAuditFu('cancel')">返回</el-button> -->
				<template v-if="form.formData.auditStatus === 1 && route.query.type === 'examine'">
					<el-button type="primary" @click="passAuditFu(true, form.formData.id)">通过</el-button>
					<el-button @click="passAuditFu(false, form.formData.id)">拒绝</el-button>
				</template>
			</div>
		</div>
		<!-- 预览 -->
		<co-preview v-if="previewImgUrl" v-model="previewImgUrl" :layer="true" />
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
import { getDetail, flowRecord, complete, reject } from '/@/api/information/theme';
import { getFiles } from '/@/api/common/upload.js';
import { useDicts } from '/@/hooks/useDicts';
import { tabControl } from '/@/stores/tabControl';
// import vertTable from '../../../components/vert-table/index.vue';
const vertTable = defineAsyncComponent(() => import('../../../components/vert-table/index.vue'));
const router = useRouter();
const route = useRoute();
const tid = ref();
const activities = ref(); // 操作记录
const activeNames = reactive(['1', '2', '3']);
const { legalCode }: any = useDicts('legalCode');
const previewImgUrl = ref('');
// 详情数据
const form = reactive<any>({
	formData: {
		//详情数据
		auditStatus: 0,
		extendedFields: '{}',
	},
	list: [
		{
			title: '单位名称',
			field: 'businessName',
		},
		{
			title: '单位性质',
			field: 'businessType',
		},
		{
			title: '统一社会信用代码',
			field: 'unifiedSocialCreditCode',
		},
		{
			title: '所属行业',
			field: 'industryType',
		},
		{
			title: '注册地区',
			field: 'quHuaAddress',
		},
		{
			title: '详细地址',
			field: 'detailedAddress',
		},
		{
			title: '法定代表人',
			field: 'legalPersonName',
		},
		{
			title: '法定代表人证件类型',
			field: 'legalPersonIdType',
		},
		{
			title: '法定代表人证件号',
			field: 'legalPersonIdCode',
		},
		{
			title: '法人邮箱',
			field: 'legalPersonEmail',
		},
		{
			title: '法人电话',
			field: 'legalPersonPhone',
		},
		{
			title: '注册币种',
			field: 'currency',
		},
		{
			title: '营业期限',
			field: 'ifBusnissText',
		},
		{
			title: '注册资本(万元)',
			field: 'amount',
		},
		{
			title: '经营范围',
			field: 'businessScope',
			colspan: 3,
		},
	],
	tableData: {
		//企业资料信息表格数据
		dataList: [],
	},
});
const tabCon = tabControl();
onMounted(() => {
	tid.value = route.query.id;
	getInfoDetail();
});
const backEmit = () => {
	tabCon.setTenderOrganizationInfoIndexTab(route.query.type as string);
};
const getInfoDetail = () => {
	getDetail(tid.value).then((res) => {
		res.data.ifBusnissText = res.data.ifBusiness === 0 ? '短期' : '长期';

		let data = res.data;
		form.formData = res.data;
		// form.formData.ifBusnissText = (data.ifBusiness === 0 ? '短期' : '长期');
		// 企业资料信息
		if (data.fileJson) {
			let dataList = JSON.parse(data.fileJson);
			dataList.forEach((item: any, index: number) => {
				item.indexSort = index + 1;
			});
			form.tableData.dataList = dataList;
		}
		getFlowRecord(data.id);
	});
};
// 操作记录
const getFlowRecord = (id: string) => {
	flowRecord({ businessKey: id, desc: true }).then((res) => {
		activities.value = res.data;
	});
};
const handleChange = (val: any) => {
	console.log(val);
};
// 预览
function review(index: number, row: any) {
	previewFUn(row.attachmentId);
}
function previewFUn(attachmentId: string): void {
	getFiles([attachmentId]).then((res: any) => {
		const obj = res.data[0];
		previewImgUrl.value = obj.fileUrl;
	});
}
// function openAuditFu(type: string) {
// 	if (type === 'cancel') {
// 		router.replace('/information/theme/tender-organization-info/index');
// 	}
// }
// 审核
function passAuditFu(type: boolean, id: string) {
	ElMessageBox.confirm(`此操作将审核${type === true ? '通过' : '拒绝'}, 是否继续?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(() => {
		if (type === true) {
			complete({ businessKey: id })
				.then((res) => {
					if (res.code === 200) {
						useMessage().success(res.msg);
						router.back();
					} else {
						useMessage().error(res.msg);
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '操作失败');
				});
		} else {
			reject({ businessKey: id })
				.then((res) => {
					if (res.code === 200) {
						useMessage().success(res.msg);
						router.back();
					} else {
						useMessage().error(res.msg);
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '操作失败');
				});
		}
	});
}
</script>

<style lang="scss" scoped>
::v-deep .co-table.el-table th {
	background-color: var(--el-table-row-hover-bg-color);
	color: var(--el-text-color-primary);
}
.detail-container {
	padding: 20px;
	border-radius: 8px;
	background-color: #fff;
}
</style>
