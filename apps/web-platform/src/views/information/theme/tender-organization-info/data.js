// import { dictApi } from '/@/api/dict/index';
export const searchConfig = {
	// styles: { background: '#ffffff' },
	items: [
		{
			prop: 'businessName',
			type: 'input',
			attrs: {
				label: '企业名称',
				placeholder: '请输入企业名称',
				clearable: true,
			},
		},
		{
			prop: 'unifiedSocialCreditCode',
			type: 'input',
			attrs: {
				label: '统一社会信用代码',
				placeholder: '请输入统一社会信用代码',
				clearable: true,
			},
		},
		{
			prop: 'auditStatus',
			type: 'select',
			option: 'auditStatus',
			attrs: {
				label: '审核状态',
				placeholder: '请选择审核状态',
				clearable: true,
			},
		},
	],
}; // 搜索配置
// table表格
// export const tableHeader = [
//   { type: 'index', label: '排序', width: 55 },
//   { prop: 'businessName', label: '招标代理名称' },
//   { prop: 'unifiedSocialCreditCode', label: '统一社会信用代码' },
//   { prop: 'insertTime', label: '注册时间' },
//   { prop: 'legalPersonName', label: '企业法人' },
//   { prop: 'businessContactName', label: '联系人' },
//   { prop: 'businessContactPhone', label: '联系方式' },
//   { prop: 'auditStatus', label: '状态' },
//   { prop: 'submitTime', label: '审核时间' }
// ]
// // 字典
// export const dic = {
// 		auditStatus: { value: dictApi['businessStatus'],color: { 0: 'color-info', 2: 'color-success', 3: 'color-danger' } }
// }
