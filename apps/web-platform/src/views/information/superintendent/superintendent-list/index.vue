<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<!-- <co-search ref="searchRef" inline :model="searchData" label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle"/> -->
			<co-table :header="tableHeader" :config="tableConfig" @dicLoaded="onDicLoaded" @loaded="onTableLoad" @operation="onOperation" align="left">
				<template #status1="{ row }">
					{{ row.status.value === 1 ? '开启' : '关闭' }}
				</template>
			</co-table>
		</div>
		<superintendentDialog ref="superintendentDialogRef" @refresh="onRefresh()" />
		<resetPasswordDialog ref="resetPasswordDialogRef" @refresh="onRefresh()" />
	</div>
</template>

<script setup lang="ts" name="superintendentInfo">
import { getSuperIntendentList } from '/@/api/information/superintendent';
import { ref } from 'vue';
import { tableHeader, dic } from './data.js';
// 引入组件
const superintendentDialog = defineAsyncComponent(() => import('./components/superintendent-edit.vue'));
const resetPasswordDialog = defineAsyncComponent(() => import('./components/reset-password-dialog.vue'));
var onSearch = null;
// 搜索表单
const dicData = ref({});
const searchData = ref({});
// 表格配置
let tableConfig = ref({
	dic,
	page: {
		response: {
			records: 'records', // 数据集合
		},
	},
	operation: {
		fixed: 'right',
		width: 200,
		// list: [
		// 	{ className: 'text-primary', type: 'text', name: '编辑', mark: 'edit', inTable: 1, rule: '' },
		// ],
	},
	request: {
		apiName: getSuperIntendentList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		// params: {...searchData.value,total:0}, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
	},
});
// 弹窗
const superintendentDialogRef = ref();
const resetPasswordDialogRef = ref();
// 查询
// function onSearchHandle(data) {
// 	onSearch({ params: data });
// }
// table加载完成回调
function onTableLoad({ getDataList }: any) {
	onSearch = getDataList;
}
// 字典加载完毕
function onDicLoaded(data: any) {
	dicData.value = data;
}
function onRefresh() {
	onSearch({ params: searchData.value });
}
// 监听表格搜索操作
function onOperation({ field, row }: any) {
	// console.log(field, row, 'ssss');
	switch (field) {
		case 'add':
			onOpenAddDialog('add', row);
			// router.push({ name: 'tenderOrganizationInfoDetails', params: { id: row.id } });
			break;
		case 'edit':
			onOpenEditDialog('edit', row);
			// router.push({ name: 'tenderOrganizationInfoDetails', params: { id: row.id } });
			break;
		case 'resetPassword':
			resetPasswordDialogRef.value.openDialog(row);
			break;
		default:
			break;
	}
}
// 打开新增监管人弹窗
const onOpenAddDialog = (type: string, row: object) => {
	superintendentDialogRef.value.openDialog(type, row);
};
// 打开编辑监管人弹窗
const onOpenEditDialog = (type: string, row: object) => {
	superintendentDialogRef.value.openDialog(type, row);
};
</script>

<style></style>
