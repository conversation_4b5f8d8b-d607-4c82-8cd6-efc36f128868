<template>
	<el-dialog v-model="pageConifg.dialogVisible" :title="pageConifg.title" width="500" destroy-on-close>
		<co-form ref="dialogFormRef" :form-list="pageConifg.formList" labelWidth="120" :form-data="pageConifg.form"></co-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="pageConifg.dialogVisible = false">取消</el-button>
				<el-button type="primary" :loading="pageConifg.submitLoading" @click="onSubmit" :disabled="pageConifg.submitLoading"> 提交 </el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { resetPassword } from '/@/api/information/user';
import { useMessage } from '/@/hooks/message';
import { encryptedData } from '/@/utils/encrypt';

const dialogFormRef = ref(null);

const validatePass = (rule: any, value: string, callback: Function) => {
	if (value === '' || value === null || value === undefined) {
		callback(new Error('密码不能为空'));
	} else if (value === pageConifg.form.loginName) {
		callback(new Error('账号和密码不能相同!'));
	} else {
		if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/.test(value)) {
			callback(new Error('密码为包含字母和数字6-26位字符'));
		}
		callback();
	}
};

const pageConifg = reactive({
	title: '重置密码',
	dialogVisible: false,
	formList: [{ id: 'password', name: '重置密码', type: 'input', required: true, validate: validatePass }],
	submitLoading: false,
	form: {
		password: '',
	},
	row: null,
});

const emits = defineEmits(['refresh']);

async function onSubmit() {
	const result = await dialogFormRef.value.getFormData().catch(() => {});
	if (!result) return false;
	pageConifg.submitLoading = true;
	resetPassword({ userId: pageConifg.row.userId, newPassword: encryptedData(result.password), notice: false })
		.then((res) => {
			if (res.code === 200) {
				useMessage().success('重置密码成功');
				pageConifg.dialogVisible = false;
				emits('refresh');
			}
		})
		.catch((err) => {
			useMessage().error(err?.msg || '重置密码失败');
		})
		.finally(() => {
			pageConifg.submitLoading = false;
		});
}

//清空表单
const reset = () => {
	dialogFormRef.value?.resetFields();
	pageConifg.form = {
		password: '',
	};
};

// 打开弹窗
const openDialog = (row: any) => {
	pageConifg.row = row;
	pageConifg.dialogVisible = true;
	reset();
};

// 关闭弹窗
const closeDialog = () => {
	pageConifg.dialogVisible = false;
};

defineExpose({
	openDialog,
	closeDialog,
});
</script>
