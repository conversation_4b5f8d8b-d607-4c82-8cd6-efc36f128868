<template>
	<el-dialog v-model="dialogVisible" :title="title" width="800" destroy-on-close>
		<co-form ref="dialogFormRef" :form-list="formList" labelWidth="180" :form-data="state.form" :dict-config="dictConfig"></co-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" :loading="submitLoading" @click="onSubmit" :disabled="submitLoading"> 提交 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useDicts } from '/@/hooks/useDicts';
import { useMessage } from '/@/hooks/message';
import { Addregister, updateUser } from '/@/api/information/superintendent';
import { encryptedData } from '/@/utils/encrypt.js';
const submitLoading = ref(false);
const title = ref();
const dialogVisible = ref(false);
const dialogFormRef = ref();
let editType = 'add';
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 定义需要的数据
let state = reactive({
	form: {
		businessName: '',
		unifiedSocialCreditCode: '',
		regulatoryProjectType: '',
		name: '',
		phone: '',
		loginName: '',
		password: '',
		password1: '',
		status: 1,
		a6: '',
		identityRoleEnum: 'IdentityRole_6',
		dataFromSourceId: null,
		userId: null, //修改时需要
	},
});
const validatePass = (rule: any, value: string, callback: Function) => {
	// if (editType === 'edit') {
	// 	if (value === '' || value === null || value === undefined) {
	// 		callback();
	// 	} else {
	// 		if (value === state.form.loginName) {
	// 			callback(new Error('账号和密码不能相同!'));
	// 		} else {
	// 			if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/.test(value)) {
	// 				callback(new Error('密码为包含字母和数字6-26位字符'));
	// 			} else {
	// 				callback();
	// 			}
	// 		}
	// 	}
	// } else {
	if (value === '' || value === null || value === undefined) {
		callback(new Error('密码不能为空'));
	} else if (value === state.form.loginName) {
		callback(new Error('账号和密码不能相同!'));
	} else {
		if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/.test(value)) {
			callback(new Error('密码为包含字母和数字6-26位字符'));
		}
		callback();
	}
	// }
};
// 用户名格式验证
const isUserName = (rule: any, value: string, callback: Function) => {
	const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/;
	if (!reg.test(value)) {
		callback(new Error('账号为包含字母和数字6-26位字符'));
	}
	callback();
};
let formList = [
	{ id: 'businessName', name: '监管人姓名', type: 'input', css: 'w-[50%]', required: true },
	{
		id: 'unifiedSocialCreditCode',
		name: '统一社会信用代码',
		type: 'input',
		css: 'w-[50%]',
		required: true,
		disabled: false,
		validate: 'checkSocialCreditCode',
	},
	{
		id: 'regulatoryProjectType',
		name: '监管项目类型',
		type: 'select',
		dicKey: 'noticeSourceProType',
		css: 'w-[50%]',
		required: true,
	},
	{ id: 'name', name: '联系人', type: 'input', css: 'w-[50%]', required: true },
	{ id: 'phone', name: '联系方式', type: 'input', css: 'w-[50%]', validate: 'phone' },
	{ id: 'loginName', name: '登录账号', type: 'input', css: 'w-[50%]', required: true, validate: isUserName },
	{ id: 'password1', name: '登录密码', type: 'input', css: 'w-[50%]', required: true, validate: validatePass },
	{ id: 'status', name: '状态', type: 'switch', css: 'w-[50%]', required: true, activeValue: 1, inactiveValue: 0 },
	{ id: 'a6', name: '备注', type: 'input', required: false, css: 'w-[50%]' },
];
// 引入字典
const dictConfig = {
	...useDicts('noticeSourceProType'),
};

// 打开弹窗
const openDialog = (type: string, row?: any) => {
	editType = type;
	nextTick(() => {
		dialogVisible.value = true;
		title.value = type === 'add' ? '新增监管人信息' : '修改监管人信息';
	});
	// 重置表单数据
	reset();
	formList = [
		{ id: 'businessName', name: '监管人姓名', type: 'input', css: 'w-[50%]', required: true },
		{
			id: 'unifiedSocialCreditCode',
			name: '统一社会信用代码',
			type: 'input',
			css: 'w-[50%]',
			required: true,
			disabled: false,
			validate: 'checkSocialCreditCode',
		},
		{
			id: 'regulatoryProjectType',
			name: '监管项目类型',
			type: 'select',
			dicKey: 'noticeSourceProType',
			css: 'w-[50%]',
			required: true,
		},
		{ id: 'name', name: '联系人', type: 'input', css: 'w-[50%]', required: true },
		{ id: 'phone', name: '联系方式', type: 'input', css: 'w-[50%]', validate: 'phone' },
		{ id: 'loginName', name: '登录账号', type: 'input', css: 'w-[50%]', required: true, validate: isUserName },
		{ id: 'password1', name: '登录密码', type: 'input', css: 'w-[50%]', required: true, validate: validatePass },
		{ id: 'status', name: '状态', type: 'switch', css: 'w-[50%]', required: true, activeValue: 1, inactiveValue: 0 },
		{ id: 'a6', name: '备注', type: 'input', required: false, css: 'w-[50%]' },
	];
	if (type === 'edit') {
		// state.ruleForm.fatherIdS = row.menuId;
		// 获取当前节点菜单信息
		formList.forEach((it) => {
			if (it.id === 'loginName' || it.id === 'unifiedSocialCreditCode') {
				it.disabled = true;
			}
		});
		state.form = JSON.parse(JSON.stringify(row));
		if (row.status.value === 1) {
			state.form.status = 1;
		} else {
			state.form.status = 0;
		}
		// formList.find(it => it.id === 'password1').required = false;
		formList.forEach((it, i) => {
			if (it.id === 'password1' || it.id === 'phone') {
				it.required = false;
			}
			if (it.id === 'password1') {
				formList.splice(i, 1);
			}
		});
		// state.form.status = row?.status?.value;
		state.form.unifiedSocialCreditCode = row.ecode;
	} else {
		formList.find((it) => {
			if (it.id === 'loginName' || it.id === 'unifiedSocialCreditCode') {
				it.disabled = false;
			}
		});
		formList.forEach((it) => {
			if (it.id === 'password1' || it.id === 'phone') {
				it.required = true;
			}
		});
	}
};
//清空表单
const reset = () => {
	dialogFormRef.value?.resetFields();
	state.form = {
		businessName: '',
		unifiedSocialCreditCode: '',
		regulatoryProjectType: '',
		name: '',
		phone: '',
		loginName: '',
		password: '',
		password1: '',
		status: 1,
		a6: '',
		identityRoleEnum: 'IdentityRole_6',
		dataFromSourceId: null,
		userId: null,
	};
};
/**
 * 提交表单数据
 * @function
 * @async
 */
const onSubmit = async () => {
	// 验证表单是否符合规则
	const result = await dialogFormRef.value.getFormData().catch(() => {});
	if (!result) return false;
	result.password = result.password1 ? encryptedData(result.password1) : '';
	result.identityRoleEnum = 'IdentityRole_6';
	if (editType === 'edit') {
		if (!result.password) {
			delete result.password;
			delete result.password1;
		}
		result.userId = state.form.userId;
	}
	// 表单数据
	try {
		submitLoading.value = true;
		editType === 'edit' ? await updateUser(result) : await Addregister(result);
		useMessage().success(editType === 'edit' ? '修改成功' : '提交成功');
		dialogVisible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		submitLoading.value = false;
	}
};
// 暴露变量 只有暴漏出来的变量 父组件才能使用
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.co-form :deep(.el-form) {
	display: flex;
	flex-wrap: wrap;
}
</style>
