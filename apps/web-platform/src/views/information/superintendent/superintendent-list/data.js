import { dictApi } from '/@/api/dict/index';
export const searchConfig = {
	// styles: { background: '#ffffff' },
	items: [
		{
			prop: 'businessName',
			type: 'input',
			attrs: {
				label: '监管人名称',
				placeholder: '请输入监管人名称',
				clearable: true,
			},
		},
		// {
		//       prop: 'unifiedSocialCreditCode',
		//   type: 'input',
		//   attrs: {
		//         label:'统一社会信用代码',
		//           placeholder: '请输入统一社会信用代码',
		//           clearable: true
		//       }
		//   },
		//   {
		//       prop: 'regulatoryProjectType',
		//       type: 'select',
		//       option: 'regulatoryProjectType',
		//     attrs: {
		//           label:'监管项目类型',
		//           placeholder: '请选择监管项目类型',
		//           clearable: true
		//       }
		//   }
	],
}; // 搜索配置
// table表格
export const tableHeader = [
	{ type: 'index', label: '排序', width: 60, align: 'center' },
	{ prop: 'businessName', label: '监管人名称', 'min-width': 180, showOverflowTooltip: true },
	{ prop: 'ecode', label: '统一社会信用代码', 'min-width': 180, showOverflowTooltip: true },
	{ prop: 'regulatoryProjectType', label: '监管项目类型', 'min-width': 140, showOverflowTooltip: true },
	{ prop: 'name', label: '联系人', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'phone', label: '联系方式', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'loginName', label: '登录账号', 'min-width': 180, showOverflowTooltip: true },
	{ prop: 'insertTime', label: '创建时间', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'status1', label: '状态', 'min-width': 100, showOverflowTooltip: true },
];
// 字典
export const dic = {
	regulatoryProjectType: { value: dictApi['noticeSourceProType'] }, //,color: { 0: '#E6A23C', 1: '#67C23A' }
};
