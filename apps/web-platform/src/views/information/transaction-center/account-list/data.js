import { dictApi } from '/@/api/dict/index';
export const searchConfig = {
	// styles: { background: '#ffffff' },
	items: [
		{
			prop: 'tradingCenterName',
			type: 'input',
			attrs: {
				label: '交易中心名称',
				placeholder: '请输入交易中心名称',
				clearable: true,
			},
		},
	],
};
// table表格
export const tableHeader = [
	{ type: 'index', label: '排序', width: 60, align: 'center' },
	{ prop: 'tradingCenterName', label: '交易中心名称', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'tradingCenterId', label: '交易中心代码', 'min-width': 200, showOverflowTooltip: true },
	{ prop: 'name', label: '联系人', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'phone', label: '联系方式', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'loginName', label: '登录账号', 'min-width': 180, showOverflowTooltip: true },
	{ prop: 'insertTime', label: '创建时间', 'min-width': 120, showOverflowTooltip: true },
	{ prop: 'status', label: '是否启用', 'min-width': 120, showOverflowTooltip: true },
];
// 字典
export const dic = {
	auditStatus: { value: dictApi['noticeSourceProType'], color: { 0: '#E6A23C', 1: '#67C23A' } },
};
