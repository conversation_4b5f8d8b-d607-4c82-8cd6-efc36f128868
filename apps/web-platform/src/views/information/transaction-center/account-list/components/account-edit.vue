<template>
	<el-dialog v-model="dialogVisible" :title="title" width="700" destroy-on-close>
		<co-form ref="dialogFormRef" :form-list="formList" labelWidth="150" :form-data="state.form" :dict-config="dictConfig" @event="onEmitEvent">
			<template #passwordSlot="{ data }">
				<el-input v-model="data.password1" type="password" placeholder="请输入登录密码" show-password />
			</template>
		</co-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" :loading="submitLoading" @click="onSubmit" :disabled="submitLoading"> 提交 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
// import { useDicts } from '/@/hooks/useDicts';
import { useMessage } from '/@/hooks/message';
import { Addregister, updateUser, firstlist } from '/@/api/information/transaction-center';
import { encryptedData } from '/@/utils/encrypt.js';
const submitLoading = ref(false);
const title = ref();
const dialogVisible = ref(false);
const dialogFormRef = ref();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
let editType = 'add';
// 定义需要的数据
const state = reactive({
	form: {
		parentId: '',
		tradingCenterId: '',
		name: '',
		phone: '',
		loginName: '',
		password1: '',
		status: 1,
		a6: '',
		identityRoleEnum: 'IdentityRole_4',
		dataFromSourceId: null,
		userId: null, // 用户id
	},
});
const validatePass = (rule: any, value: string, callback: Function) => {
	if (editType === 'edit') {
		if (value === '' || value === null || value === undefined) {
			callback();
		} else {
			if (value === state.form.loginName) {
				callback(new Error('登录账号和登录密码不能相同!'));
			} else {
				if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/.test(value)) {
					callback(new Error('登录密码为包含字母和数字6-26位字符'));
				} else {
					callback();
				}
			}
		}
	} else {
		if (value === '' || value === null || value === undefined) {
			callback(new Error('登录密码不能为空'));
		} else if (value === state.form.loginName) {
			callback(new Error('登录账号和登录密码不能相同!'));
		} else {
			if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/.test(value)) {
				callback(new Error('登录密码为包含字母和数字6-26位字符'));
			}
			callback();
		}
	}
};
// 用户名格式验证
const isUserName = (rule: any, value: string, callback: Function) => {
	const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,26}$/;
	if (!reg.test(value)) {
		callback(new Error('登录账号为包含字母和数字6-26位字符'));
	}
	callback();
};
let formList = [
	{
		id: 'parentId',
		name: '交易一级分类',
		type: 'select',
		dicKey: 'transactionClassify',
		css: 'w-[50%]',
		required: true,
	},
	{
		id: 'tradingCenterId',
		name: '交易中心',
		type: 'select',
		dicKey: 'tradingCenterClassify',
		css: 'w-[50%]',
		required: true,
	},
	{ id: 'name', name: '联系人', type: 'input', css: 'w-[50%]', required: false },
	{ id: 'phone', name: '联系方式', type: 'input', css: 'w-[50%]', required: false, validate: 'phone' },
	{ id: 'loginName', name: '登录账号', type: 'input', css: 'w-[50%]', required: true, disabled: false, validate: isUserName },
	{ id: 'password1', name: '登录密码', type: 'input', css: 'w-[50%]', required: true, validate: validatePass, slot: 'passwordSlot' },
	{ id: 'status', name: '是否启用', type: 'switch', css: 'w-[50%]', required: true, activeValue: 1, inactiveValue: 0 },
	{ id: 'remarks', name: '备注', type: 'input', required: false, css: 'w-[50%]' },
];
// 引入字典
const dictConfig = {
	// ...useDicts('noticeSourceProType')
	transactionClassify: ref([]),
	tradingCenterClassify: ref([]),
};
const onEmitEvent = (row: any, val: any) => {
	if (row.id == 'parentId') {
		dictConfig.tradingCenterClassify.value = [];
		firstlist({ level: 2, parentId: val }).then((res) => {
			dictConfig.tradingCenterClassify.value = res.data;
		});
	}
};
// 打开弹窗
const openDialog = (type: string, row?: any) => {
	// dictConfig.transactionClassify.value = [
	//   {
	//     id: "1601197476101505026",
	//     name: "河北省",
	//     code: "130000",
	//     parentId: null,
	//     izEnable: true,
	//     level: 1,
	//     izJoin: false,
	//     joinCountDown: null,
	//     tradingCenterSonDTOList: null,
	//     parentTradingCenter: null,
	//     unifiedSocialCreditCode: null
	//   }
	// ];
	editType = type;
	firstlist({ level: 1 }).then((res) => {
		dictConfig.transactionClassify.value = res.data;
	});
	nextTick(() => {
		dialogVisible.value = true;
		title.value = type === 'add' ? '新增交易中心账号' : '修改交易中心账号';
	});
	// 重置表单数据
	reset();
	formList = [
		{
			id: 'parentId',
			name: '交易一级分类',
			type: 'select',
			dicKey: 'transactionClassify',
			css: 'w-[50%]',
			required: true,
		},
		{
			id: 'tradingCenterId',
			name: '交易中心',
			type: 'select',
			dicKey: 'tradingCenterClassify',
			css: 'w-[50%]',
			required: true,
		},
		{ id: 'name', name: '联系人', type: 'input', css: 'w-[50%]', required: false },
		{ id: 'phone', name: '联系方式', type: 'input', css: 'w-[50%]', required: false, validate: 'phone' },
		{ id: 'loginName', name: '登录账号', type: 'input', css: 'w-[50%]', required: true, disabled: false, validate: isUserName },
		{ id: 'password1', name: '登录密码', type: 'input', css: 'w-[50%]', required: true, validate: validatePass, slot: 'passwordSlot' },
		{ id: 'status', name: '是否启用', type: 'switch', css: 'w-[50%]', required: true, activeValue: 1, inactiveValue: 0 },
		{ id: 'remarks', name: '备注', type: 'input', required: false, css: 'w-[50%]' },
	];
	if (row?.tradingCenterId && type === 'edit') {
		// state.ruleForm.fatherIdS = row.menuId;
		// 获取当前节点菜单信息
		formList.find((it) => it.id === 'loginName').disabled = true;
		formList.find((it) => it.id === 'password1').required = false;
		const index = formList.findIndex((it) => it.id === 'password1');
		if (index !== -1) {
			formList.splice(index, 1);
		}

		state.form = JSON.parse(JSON.stringify(row));
		state.form.status = row?.status?.value;
		state.form.password1 = row?.password;
		firstlist({ level: 2, parentId: row.parentTradingCenterId }).then((res) => {
			dictConfig.tradingCenterClassify.value = res.data;
		});
		state.form.parentId = row.parentTradingCenterId; // 父级id
	} else {
		formList.find((it) => it.id === 'loginName').disabled = false;
		formList.find((it) => it.id === 'password1').required = true;
		dictConfig.tradingCenterClassify.value = [];
	}
};
//清空表单
const reset = () => {
	dialogFormRef.value?.resetFields();
	state.form = {
		parentId: '',
		tradingCenterId: '',
		name: '',
		phone: '',
		loginName: '',
		password1: '',
		status: 1,
		a6: '',
		identityRoleEnum: 'IdentityRole_4',
		dataFromSourceId: null,
		userId: null,
	};
};
/**
 * 提交表单数据
 * @function
 * @async
 */
const onSubmit = async () => {
	// 验证表单是否符合规则
	const result = await dialogFormRef.value.getFormData().catch(() => {});
	if (!result) return false;
	result.password = result.password1 ? encryptedData(result.password1) : '';
	result.identityRoleEnum = 'IdentityRole_4';
	// 表单数据
	if (editType === 'edit') {
		if (!result.password) {
			delete result.password;
			delete result.password1;
		}
		result.userId = state.form.userId;
	}

	try {
		submitLoading.value = true;
		editType === 'edit' ? await updateUser(result) : await Addregister(result);
		useMessage().success('提交成功');
		dialogVisible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		submitLoading.value = false;
	}
};
// 暴露变量 只有暴漏出来的变量 父组件才能使用
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.co-form :deep(.el-form) {
	display: flex;
	flex-wrap: wrap;
}

.co-form :deep(.el-form-item--default) {
	padding-bottom: 30px;
}
</style>
