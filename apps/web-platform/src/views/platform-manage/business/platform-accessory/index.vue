<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-form ref="searchFormRef" :model="searchForm" :inline="true">
				<el-form-item label="附件名称" prop="documentName">
					<el-input v-model.trim="searchForm.documentName" placeholder="附件名称" clearable />
				</el-form-item>
				<el-form-item label="是否必传" prop="mandatory">
					<el-select v-model="searchForm.mandatory" placeholder="是否必传" clearable style="width: 192px">
						<el-option v-for="item in pageConfig.isMandatory" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="附件状态" prop="listingStatus">
					<el-select v-model="searchForm.listingStatus" placeholder="附件状态" clearable style="width: 192px">
						<el-option v-for="item in pageConfig.listingStatusArr" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="业务类型" prop="businessType">
					<el-select v-model="searchForm.businessType" placeholder="业务类型" clearable style="width: 192px" @change="changeBusinessType">
						<el-option v-for="item in pageConfig.businessTypeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="业务节点" prop="businessNode">
					<el-select v-model="searchForm.businessNode" placeholder="业务节点" clearable style="width: 192px">
						<el-option v-for="item in pageConfig.businessNodeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="search" @click="search">查询</el-button>
					<el-button type="default" icon="Refresh" @click="resetForm">重置</el-button>
				</el-form-item>
			</el-form>
			<co-table :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" @loaded="dsTableLoaded" @operation="onOperation" align="left">
				<template #mandatory="{ row }">
					<span>{{ row.mandatory == '0' ? '否' : '是' }}</span>
				</template>
				<template #attachmentType="{ row }">
					{{ getLabels(row.attachmentType, pageConfig.fileType) }}
				</template>
				<template #businessType="{ row }">
					{{ findNameById(pageConfig.businessTypeData, row.businessType) || '-' }}
				</template>
				<template #businessNode="{ row }">
					{{ findNameById(pageConfig.businessTypeData, row.businessNode) || '-' }}
				</template>
				<template #listingStatus="{ row }">
					<span>{{ row.listingStatus == '0' ? '未上架' : '上架' }}</span>
				</template>
			</co-table>
			<formData ref="formDataRef" @refresh="search" />
		</div>
	</div>
</template>

<script setup>
import DIC_ID from '/@/api/dict/dict-business';
import formData from './components/form-data.vue';
import { getDocumentConfigList } from '/@/api/platform-manage/platform-accessory';
import { getDictionary } from '/@/api/common/dictionary';
import { reactive } from 'vue';
import datas from './index.data.js';

const searchFormRef = ref(null);
const formDataRef = ref(null);

const searchForm = reactive({
	documentName: '',
	mandatory: '',
	listingStatus: '',
	businessType: '',
	businessNode: '',
	configuringParty: 1,
	documentType: 1,
});

const pageConfig = reactive({
	isMandatory: [
		{
			dicValue: '0',
			dicName: '否',
		},
		{
			dicValue: '1',
			dicName: '是',
		},
	],
	listingStatusArr: [
		{
			dicValue: '0',
			dicName: '未上架',
		},
		{
			dicValue: '1',
			dicName: '上架',
		},
	],
	fileType: [],
	// 业务类型
	businessTypeData: [],
	// 业务节点
	businessNodeData: [],
	tableConfig: {
		operation: {
			fixed: 'right',
			width: 220,
		},
		request: {
			apiName: getDocumentConfigList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
			params: { documentName: '', mandatory: '', listingStatus: '', businessType: '', businessNode: '', configuringParty: 1, documentType: 1 },
		},
	},
	tableHeader: datas.headers,
});

function resetForm() {
	searchFormRef.value.resetFields();
	search();
}

// 监听表格搜索操作
function onOperation({ field, row }) {
	switch (field) {
		case 'add':
			handleAdd();
			break;
		case 'edit':
			edit(row);
			break;
	}
}

// 获取字典
async function getInit() {
	// 业务类型
	const businessType = await getDictionary(DIC_ID.reportType);
	pageConfig.businessTypeData = businessType;
	// 附件类型
	const attachmentType = await getDictionary(DIC_ID.attachmentType);
	pageConfig.fileType = attachmentType.dicList;
}
getInit();

// 新增
function handleAdd() {
	formDataRef.value.addDialog();
}

// 编辑
function edit(row) {
	formDataRef.value.editDialog(row.id);
}

function changeBusinessType(value) {
	searchForm.businessNode = '';
	pageConfig.businessTypeData.forEach((item) => {
		if (item.dicValue == value) {
			pageConfig.businessNodeData = item.children;
		}
	});
}

let onSearch = null;

// dsTable加载完成回调
function dsTableLoaded({ getDataList }) {
	onSearch = getDataList;
}

function search() {
	onSearch({ params: searchForm });
}

function getLabels(ids, data) {
	const idArray = ids.split(',').map(Number);
	const labels = [];

	for (let i = 0; i < idArray.length; i++) {
		const id = idArray[i];
		const item = data.find((obj) => obj.dicValue == id);

		if (item) {
			labels.push(item.dicName);
		}
	}

	return labels.join('、');
}

function findNameById(tree, id) {
	for (let i = 0; i < tree.length; i++) {
		const node = tree[i];
		if (node.dicValue == id) {
			return node.dicName;
		}
		if (node.children && node.children.length > 0) {
			const result = findNameById(node.children, id);
			if (result) {
				return result;
			}
		}
	}

	return null;
}
</script>

<style lang="scss" scoped>
.pagination {
	margin: 15px;
	text-align: right;
}
</style>
