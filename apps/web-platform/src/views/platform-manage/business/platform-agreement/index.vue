<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-form :model="searchForm" :inline="true">
				<el-form-item label="业务类型">
					<el-select v-model="searchForm.businessType" clearable placeholder="业务类型" style="width: 240px" @change="changeBusinessType">
						<el-option v-for="item in pageConfig.businessTypeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="业务节点">
					<el-select v-model="searchForm.businessNode" clearable placeholder="业务节点" style="width: 240px">
						<el-option v-for="item in pageConfig.businessNodeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="协议名称">
					<el-input v-model.trim="searchForm.documentName" placeholder="协议名称" clearable />
				</el-form-item>
				<el-form-item label="是否签署">
					<el-select v-model="searchForm.signingStatus" clearable placeholder="是否签署" style="width: 240px">
						<el-option v-for="item in pageConfig.signingStatusList" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="协议状态">
					<el-select v-model="searchForm.listingStatus" clearable placeholder="协议状态" style="width: 240px">
						<el-option v-for="item in pageConfig.listingStatusList" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="生效日期">
					<el-date-picker v-model="searchForm.timeList" type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="生效开始日期" end-placeholder="生效结束日期" />
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="search" @click="onSearch">查询</el-button>
					<el-button type="default" icon="Refresh" @click="onReset">重置</el-button>
				</el-form-item>
			</el-form>
			<co-table ref="depositRef" :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" @loaded="dsTableLoaded" @operation="onOperation" align="left">
				<template #businessType="{ row }">
					{{ findNameById(pageConfig.businessTypeData, row.businessType) || '-' }}
				</template>
				<template #agreementTemplateName="{ row }">
					<span v-if="row.agreementTemplateName" style="color: #409eff">{{ row.agreementTemplateName }}</span>
					<span v-else>-</span>
				</template>
				<template #businessNode="{ row }">
					{{ findNameById(pageConfig.businessTypeData, row.businessNode) || '-' }}
				</template>
			</co-table>
		</div>
		<formData ref="formDataRef" @refresh="onSearch" />
	</div>
</template>

<script setup>
import { getDocumentConfigList, shelfing } from '/@/api/platform-manage/platform-accessory';
import { getDictionary } from '/@/api/common/dictionary';
import { downLoadFile2 } from '/@/api/common/upload';
import formData from './components/form-data.vue';
import datas from './index.data.js';
import DIC_ID from '/@/api/dict/dict-business';
import { useMessage, useMessageBox } from '/@/hooks/message';

const formDataRef = ref(null);

const searchForm = reactive({
	effectiveStartTime: '',
	effectiveEndTime: '',
	timeList: [],
	businessType: '',
	businessNode: '',
	documentName: '',
	signingStatus: '',
	listingStatus: '',
});
const pageConfig = reactive({
	tableHeader: datas.tableHeader,
	searchConfig: datas.searchConfig,
	tableConfig: {
		dic: datas.dic,
		operation: {
			fixed: 'right',
			width: 220,
		},
		request: {
			apiName: getDocumentConfigList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
			params: { configuringParty: 1, documentType: 2 },
		},
	},
	// 业务类型
	businessTypeData: [],
	// 业务节点
	businessNodeData: [],
	// 是否签署
	signingStatusList: [
		{
			dicValue: 0,
			dicName: '否',
		},
		{
			dicValue: 1,
			dicName: '是',
		},
	],
	// 协议状态
	listingStatusList: [
		{
			dicValue: '0',
			dicName: '未上架',
		},
		{
			dicValue: '1',
			dicName: '上架',
		},
	],
});

// 获取字典
async function getInit() {
	// 业务类型
	const businessType = await getDictionary(DIC_ID.agreementType);
	pageConfig.businessTypeData = businessType;
}
getInit();

let searchHandle = null;

// dsTable加载完成回调
function dsTableLoaded({ getDataList }) {
	searchHandle = getDataList;
}

// 监听表格搜索操作
function onOperation({ field, row }) {
	switch (field) {
		// 详情
		case 'add':
			formDataRef.value.addDialog();
			break;
		case 'edit':
			formDataRef.value.editDialog(row.id);
			break;
		case 'hit':
			changeShelfing(row, 'hit');
			break;
		case 'out':
			changeShelfing(row, 'out');
			break;
		case 'dow':
			downLoadFile2(row.agreementTemplate).catch((_) => {
				useMessage().error('下载失败');
			});
			break;
	}
}

function onSearch() {
	if (searchForm.timeList && searchForm.timeList.length > 0) {
		searchForm.effectiveStartTime = searchForm.timeList[0];
		searchForm.effectiveEndTime = searchForm.timeList[1];
	} else {
		searchForm.effectiveStartTime = '';
		searchForm.effectiveEndTime = '';
	}
	const params = JSON.parse(JSON.stringify(searchForm));
	delete params.timeList;
	searchHandle({ params });
}

// 重置
function onReset() {
	Object.assign(searchForm, {
		effectiveStartTime: '',
		effectiveEndTime: '',
		timeList: [],
		businessType: '',
		businessNode: '',
		documentName: '',
		signingStatus: '',
		listingStatus: '',
	});
	onSearch();
}

function changeShelfing(row, type) {
	const str = type == 'hit' ? '上架' : '下架';
	useMessageBox()
		.confirm('确认' + str + ', 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {
			const data = {
				id: row.id,
				listingStatus: row.listingStatus == 0 ? 1 : 0,
			};
			shelfing(data).then((res) => {
				if (res.code == 200) {
					useMessage().success(str + '成功');
					onSearch();
				}
			});
		});
}

function changeBusinessType(value) {
	searchForm.businessNode = '';
	pageConfig.businessTypeData.forEach((item) => {
		if (item.dicValue == value) {
			pageConfig.businessNodeData = item.children;
		}
	});
}

function findNameById(tree, id) {
	for (let i = 0; i < tree.length; i++) {
		const node = tree[i];
		if (node.dicValue == id) {
			return node.dicName;
		}
		if (node.children && node.children.length > 0) {
			const result = findNameById(node.children, id);
			if (result) {
				return result;
			}
		}
	}

	return null;
}
</script>
<style lang="scss" scoped></style>
