<template>
	<el-dialog :title="pageConfig.title" v-model="pageConfig.visible" width="600px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="cancel">
		<el-form ref="formRef" :model="form" :rules="pageConfig.rules" label-width="120px">
			<el-form-item label="业务类型：" prop="businessType">
				<el-select v-model="form.businessType" clearable placeholder="请选择" style="width: 100%" @change="changeBusinessType">
					<el-option v-for="item in pageConfig.businessTypeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="业务节点：" prop="businessNode">
				<el-select v-model="form.businessNode" clearable placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.businessNodeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="协议名称：" prop="documentName">
				<el-input v-model.trim="form.documentName" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="是否签署：" prop="signingStatus">
				<el-select v-model="form.signingStatus" placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.signingStatusList" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="协议状态：" prop="listingStatus">
				<el-select v-model="form.listingStatus" placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.listingStatusList" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="生效时间：" prop="effectiveTime">
				<el-date-picker v-model="form.effectiveTime" type="date" :disabled-date="pageConfig.pickerOptions" value-format="YYYY-MM-DD" style="width: 100%" placeholder="选择日期" />
			</el-form-item>
			<el-form-item label="协议字段：" prop="stuffingString">
				<el-input v-model.trim="form.stuffingString" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="协议附件：" prop="agreementTemplate">
				<upload-file v-model:value="form.agreementTemplate" :file-type="['docx']" :limit="1" @change="uploadChange" />
			</el-form-item>
		</el-form>
		<template v-slot:footer>
			<div class="dialog-footer">
				<el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import { getDictionary } from '/@/api/common/dictionary';
import UploadFile from '/@/components/pro-upload/upload-file.vue';
import { add, edit, getInfo } from '/@/api/platform-manage/platform-accessory';
import DIC_ID from '/@/api/dict/dict-business';
import { useMessage } from '/@/hooks/message';

let form = reactive({
	configuringParty: 1,
	documentType: 2,
	businessType: '',
	businessNode: '',
	documentName: '',
	signingStatus: '',
	listingStatus: '',
	effectiveTime: '',
	stuffingString: '',
	agreementTemplate: null,
});

const formRef = ref(null);

const pageConfig = reactive({
	title: '协议配置',
	visible: false,
	rules: {
		businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
		businessNode: [{ required: true, message: '请选择业务节点', trigger: 'change' }],
		documentName: [{ required: true, message: '请输入协议名称', trigger: 'blur' }],
		signingStatus: [{ required: true, message: '请选择是否签署', trigger: 'change' }],
		listingStatus: [{ required: true, message: '请选择协议状态', trigger: 'change' }],
		effectiveTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
		stuffingString: [{ required: true, message: '请输入协议字段', trigger: 'blur' }],
		agreementTemplate: [{ required: true, message: '请上传协议附件', trigger: 'change' }],
	},
	// 业务类型
	businessTypeData: [],
	// 业务节点
	businessNodeData: [],
	// 是否签署
	signingStatusList: [
		{
			dicValue: 0,
			dicName: '否',
		},
		{
			dicValue: 1,
			dicName: '是',
		},
	],
	// 协议状态
	listingStatusList: [
		{
			dicValue: 0,
			dicName: '未上架',
		},
		{
			dicValue: 1,
			dicName: '上架',
		},
	],
	pickerOptions(v) {
		return v.getTime() < new Date().getTime() - 86400000; //  - 86400000是否包括当天
	},
});

// 获取字典
async function getInit() {
	// 业务类型
	const businessType = await getDictionary(DIC_ID.agreementType);
	pageConfig.businessTypeData = businessType;
}
getInit();

function addDialog() {
	form = reactive({
		configuringParty: 1,
		documentType: 2,
		businessType: '',
		businessNode: '',
		documentName: '',
		signingStatus: '',
		listingStatus: '',
		effectiveTime: '',
		stuffingString: '',
		agreementTemplate: null,
	});

	// Object.assign(form, {
	// 	id: '',
	// 	configuringParty: 1,
	// 	documentType: 2,
	// 	businessType: '',
	// 	businessNode: '',
	// 	documentName: '',
	// 	signingStatus: '',
	// 	listingStatus: '',
	// 	effectiveTime: '',
	// 	stuffingString: '',
	// });
	console.log(form, 'form');
	pageConfig.visible = true;
}

function editDialog(id) {
	pageConfig.visible = true;
	getInfo(id).then((res) => {
		if (res.code == 200) {
			Object.assign(form, res.data);
			form.businessType = form.businessType.toString();
			changeBusinessType(form.businessType, true);
		}
	});
}

defineExpose({
	addDialog,
	editDialog,
});

function changeBusinessType(value, echo = false) {
	if (!echo) form.businessNode = '';
	pageConfig.businessTypeData.forEach((item) => {
		if (item.dicValue == value) {
			pageConfig.businessNodeData = item.children;
		}
	});
}

function uploadChange(val) {
	if (val && val.length > 0) {
		form.agreementTemplateName = val[0].name;
		if (form.agreementDocumentName) form.agreementDocumentName = form.agreementTemplateName;
		form.agreementTemplate = val[0].id;
	}
}
const emits = defineEmits(['refresh']);

function cancel() {
	formRef.value.resetFields();
	Object.assign(form, {
		configuringParty: 1,
		documentType: 2,
	});
	pageConfig.visible = false;
}

function submitForm() {
	formRef.value.validate((valid) => {
		if (valid) {
			let api = null;
			if (!form.id) {
				api = add;
			} else {
				api = edit;
			}
			api(form)
				.then((res) => {
					if (res.code === 200) {
						useMessage().success(form.id ? '修改成功' : '添加成功');
						pageConfig.visible = false;
						emits('refresh');
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '请求失败');
				});
		} else {
			console.log('error submit!!');
			return false;
		}
	});
}
</script>
<style scoped>
.btns {
	display: flex;
	justify-content: center;
}
</style>
