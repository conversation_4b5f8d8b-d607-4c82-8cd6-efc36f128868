<template>
	<div class="layout-padding">
		<pro-back-pre :title="detailConfig.title" />
		<div class="page">
			<pro-title-up-down class="financing-detail">
				<template #header>
					<pro-content-title>
						<template #title>企业基本信息</template>
					</pro-content-title>
				</template>
				<el-form :inline="true" :model="formInline">
					<el-form-item label="商户名称：">
						<el-input v-model="formInline.businessName" readonly placeholder="商户名称" />
					</el-form-item>
					<el-form-item label="统一社会信用代码：">
						<el-input v-model="formInline.unifiedSocialCreditCode" readonly placeholder="统一社会信用代码" />
					</el-form-item>
				</el-form>
			</pro-title-up-down>
			<pro-title-up-down class="financing-detail">
				<template #header>
					<pro-content-title>
						<template #title>附件配置信息</template>
					</pro-content-title>
				</template>
				<co-table ref="depositRef" :config="detailConfig.tableConfig" :header="detailConfig.tableHeader" @loaded="dsTableLoaded" @operation="onOperation" align="left">
					<template #businessType="{ row }">
						{{ findNameById(detailConfig.businessTypeData, row.businessType) || '-' }}
					</template>
					<template #attachmentType="{ row }">
						{{ getLabels(row.attachmentType, detailConfig.fileType) }}
					</template>
					<template #businessNode="{ row }">
						{{ findNameById(detailConfig.businessTypeData, row.businessNode) || '-' }}
					</template>
				</co-table>
			</pro-title-up-down>
		</div>
		<formData ref="formDataRef" @refresh="onSearch" />
	</div>
</template>

<script setup>
import datas from './detail.data.js';
import formData from './components/form-data.vue';
import { getDocumentConfigList } from '/@/api/platform-manage/platform-accessory';
import { getDictionary } from '/@/api/common/dictionary';
import { reactive } from 'vue';
import DIC_ID from '/@/api/dict/dict-business';
const route = useRoute();

const formDataRef = ref(null);

const formInline = reactive({
	businessName: route.query.businessName,
	unifiedSocialCreditCode: route.query.unifiedSocialCreditCode,
});

const detailConfig = reactive({
	title: '业务附件配置-详情',
	tableConfig: {
		dic: datas.dic,
		operation: {
			fixed: 'right',
			width: 220,
		},
		request: {
			apiName: getDocumentConfigList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
			params: {
				configuringParty: 2,
				documentType: 1,
				merchantEcode: route.query.unifiedSocialCreditCode,
			},
		},
	},
	isMandatory: [
		{
			dicValue: '0',
			dicName: '否',
		},
		{
			dicValue: '1',
			dicName: '是',
		},
	],
	listingStatusArr: [
		{
			dicValue: '0',
			dicName: '未上架',
		},
		{
			dicValue: '1',
			dicName: '上架',
		},
	],
	fileType: [
		{ id: '1', label: 'jpg' },
		{ id: '2', label: 'png' },
		{ id: '3', label: 'word' },
		{ id: '4', label: 'pdf' },
		{ id: '5', label: 'zip' },
		{ id: '6', label: 'rar' },
	],
	// 业务类型
	businessTypeData: [],
	// 业务节点
	businessNodeData: [],
	tableHeader: datas.headers,
	searchConfig: datas.searchConfig,
});

let searchHandle = null;

// dsTable加载完成回调
function dsTableLoaded({ getDataList }) {
	searchHandle = getDataList;
}

// 监听表格搜索操作
function onOperation({ field, row }) {
	switch (field) {
		// 详情
		case 'add':
			formDataRef.value.addDialog(formInline.unifiedSocialCreditCode);
			break;
		case 'edit':
			formDataRef.value.editDialog(row.id);
			break;
	}
}

function onSearch() {
	searchHandle();
}

async function getInit() {
	// 业务类型
	const businessType = await getDictionary(DIC_ID.reportType);
	detailConfig.businessTypeData = businessType;
	// 附件类型
	const attachmentType = await getDictionary(DIC_ID.attachmentType);
	detailConfig.fileType = attachmentType.dicList;
}

function getLabels(ids, data) {
	if (ids) {
		const idArray = ids.split(',').map(Number);
		const labels = [];

		for (let i = 0; i < idArray.length; i++) {
			const id = idArray[i];
			const item = data.find((obj) => obj.dicValue == id);

			if (item) {
				labels.push(item.dicName);
			}
		}

		return labels.join('、');
	} else {
		return '-';
	}
}

function findNameById(tree, id) {
	for (let i = 0; i < tree.length; i++) {
		const node = tree[i];
		if (node.dicValue == id) {
			return node.dicName;
		}
		if (node.children && node.children.length > 0) {
			const result = this.findNameById(node.children, id);
			if (result) {
				return result;
			}
		}
	}

	return null;
}

getInit();
</script>
<style lang="scss" scoped>
.page {
	border-radius: 8px;
	background: #fff;
	box-sizing: border-box;
	margin-top: 15px;
}
</style>
