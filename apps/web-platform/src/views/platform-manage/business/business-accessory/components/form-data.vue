<template>
	<el-dialog :title="pageConfig.title" v-model="pageConfig.visible" width="600px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="cancel">
		<el-form ref="formRef" :model="form" :rules="pageConfig.rules" label-width="100px">
			<el-form-item label="附件名称：" prop="documentName">
				<el-input v-model.trim="form.documentName" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="是否必传：" prop="mandatory">
				<el-select v-model="form.mandatory" placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.isMandatory" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="状态：" prop="listingStatus">
				<el-select v-model="form.listingStatus" placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.listingStatusArr" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="附件类型：" prop="attachmentType">
				<el-checkbox-group v-model="form.attachmentType">
					<el-checkbox v-for="item in pageConfig.attachmentType" :key="item.dicValue" :label="item.dicValue">{{ item.dicName }}</el-checkbox>
				</el-checkbox-group>
			</el-form-item>
			<el-form-item label="业务类型：" prop="businessType">
				<el-select v-model="form.businessType" placeholder="请选择" style="width: 100%" @change="changeBusinessType">
					<el-option v-for="item in pageConfig.businessTypeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
			<el-form-item label="业务节点：" prop="businessNode">
				<el-select v-model="form.businessNode" placeholder="请选择" style="width: 100%">
					<el-option v-for="item in pageConfig.businessNodeData" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
				</el-select>
			</el-form-item>
		</el-form>
		<template v-slot:footer>
			<div class="dialog-footer">
				<el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm('form')">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import { add, edit, getInfo } from '../../../../../api/platform-manage/platform-accessory';
import { getDictionary } from '/@/api/common/dictionary';
import { reactive, ref, defineEmits } from 'vue';
import DIC_ID from '/@/api/dict/dict-business';
import { useMessage } from '/@/hooks/message';

const formRef = ref(null);

const emits = defineEmits(['update:visible', 'refresh']);

defineExpose({ addDialog, editDialog });

const form = reactive({
	documentType: 1,
	configuringParty: 2,
	merchantEcode: '',
	attachmentType: [],
});
const pageConfig = reactive({
	title: '附件配置',
	visible: false,
	dicCode: null,
	rules: {
		documentName: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
		mandatory: [{ required: true, message: '请选择是否必传', trigger: 'change' }],
		listingStatus: [{ required: true, message: '请选择状态', trigger: 'change' }],
		attachmentType: [{ required: true, message: '请选择附件类型', trigger: 'change' }],
		businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
		businessNode: [{ required: true, message: '请选择业务节点', trigger: 'blur' }],
	},
	isMandatory: [
		{
			dicValue: 0,
			dicName: '否',
		},
		{
			dicValue: 1,
			dicName: '是',
		},
	],
	listingStatusArr: [
		{
			dicValue: 0,
			dicName: '未上架',
		},
		{
			dicValue: 1,
			dicName: '上架',
		},
	],
	attachmentType: [],
	// 业务类型
	businessTypeData: [],
	// 业务节点
	businessNodeData: [],
});

getInit();

async function getInit() {
	// 业务类型
	const businessType = await getDictionary(DIC_ID.reportType);
	pageConfig.businessTypeData = businessType;
	// 附件类型
	const attachmentType = await getDictionary(DIC_ID.attachmentType);
	pageConfig.attachmentType = attachmentType.dicList;
}

function addDialog(merchantEcode) {
	pageConfig.visible = true;
	form.merchantEcode = merchantEcode;
}

function editDialog(id) {
	pageConfig.visible = true;
	getInfo(id).then((res) => {
		if (res.code == 200) {
			Object.assign(form, res.data);
			form.attachmentType = form.attachmentType ? form.attachmentType.split(',') : [];
			form.businessType = form.businessType.toString();
			changeBusinessType(form.businessType, true);
		}
	});
}

function changeBusinessType(value, echo = false) {
	if (!echo) form.businessNode = '';
	pageConfig.businessTypeData.forEach((item) => {
		if (item.dicValue == value) {
			pageConfig.businessNodeData = item.children;
		}
	});
}

function submitForm() {
	formRef.value.validate((valid) => {
		if (valid) {
			let api = null;
			if (!this.form.id) {
				api = add;
			} else {
				api = edit;
			}
			const data = JSON.parse(JSON.stringify(form));
			data.attachmentType = data.attachmentType.toString();
			api(data)
				.then((res) => {
					if (res.code === 200) {
						useMessage().success(form.id ? '修改成功' : '添加成功');
						pageConfig.visible = false;
						emits('refresh');
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '请求失败');
				});
		} else {
			console.log('error submit!!');
			return false;
		}
	});
}

function cancel() {
	formRef.value.resetFields();
	Object.assign(form, {
		documentType: 1,
		configuringParty: 2,
		merchantEcode: '',
		attachmentType: [],
	});
	pageConfig.visible = false;
}
</script>
