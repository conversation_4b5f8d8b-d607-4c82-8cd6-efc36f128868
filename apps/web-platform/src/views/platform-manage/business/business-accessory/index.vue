<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-search :config="pageConfig.searchConfig" @search="searchHandle" />
			<co-table :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" @loaded="dsTableLoaded" @operation="onOperation" align="left" />
		</div>
	</div>
</template>

<script setup name="businessAccessory">
import datas from './index.data.js';
import { getBusinessList } from '/@/api/platform-manage/business-accessory';
import { reactive } from 'vue';
const router = useRouter();
const pageConfig = reactive({
	tableConfig: {
		operation: {
			fixed: 'right',
			width: 220,
		},
		request: {
			apiName: getBusinessList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
			params: {
				disabledState: 0,
				enterType: 1,
			},
		},
	},
	tableHeader: datas.headers,
	searchConfig: datas.searchConfig,
});

let onSearch = null;

// dsTable加载完成回调
function dsTableLoaded({ getDataList }) {
	onSearch = getDataList;
}

function searchHandle(data) {
	onSearch({ params: data });
}

// 监听表格搜索操作
function onOperation({ field, row }) {
	switch (field) {
		case 'detail':
			router.push({ path: '/platform-manage/business/business-accessory/detail', query: { businessName: row.businessName, unifiedSocialCreditCode: row.unifiedSocialCreditCode } });
			break;
	}
}
</script>

<style lang="scss" scoped></style>
