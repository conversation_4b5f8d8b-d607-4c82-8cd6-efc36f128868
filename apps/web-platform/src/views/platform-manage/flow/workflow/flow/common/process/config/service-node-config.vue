<!-- 服务 -->
<template>
	<div>
		<el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
			<el-form-item label="服务类型" prop="taskType">
				<el-radio-group v-model="ruleForm.taskType">
					<el-radio label="feign" value="feign" />
				</el-radio-group>
			</el-form-item>
			<el-form-item label="服务名称" prop="feignServiceName">
				<el-input v-model="ruleForm.feignServiceName" />
			</el-form-item>
			<el-form-item label="接口路由" prop="feignServicePath">
				<el-input v-model="ruleForm.feignServicePath" />
			</el-form-item>
			<el-form-item label="是否可触发" prop="triggerable">
				<el-radio-group v-model="ruleForm.triggerable">
					<el-radio :label="true">是</el-radio>
					<el-radio :label="false">否</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="是否异步" prop="async">
				<el-radio-group v-model="ruleForm.async">
					<el-radio :label="true">是</el-radio>
					<el-radio :label="false">否</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup>
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { computed, ref, watch } from 'vue';

// 使用 Pinia store
const flowStore = useDesign();

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

// 数据
const rules = ref({
	// feign: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
	// feignServiceName: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
	// feignServicePath: [{ required: true, message: '请输入接口路由', trigger: 'blur' }],
	// triggerable: [
	//   {
	//     required: true,
	//     message: '请选择是否可触发',
	//     trigger: 'change'
	//   }
	// ],
	// async: [
	//   {
	//     required: true,
	//     message: '请选择是否异步',
	//     trigger: 'change'
	//   }
	// ]
});

// 计算属性
const selectedNode = computed(() => flowStore.selectedNode);
const ruleForm = computed(() => {
	console.log('服务？？', selectedNode.value);
	return selectedNode.value.props;
});

// 监听器
watch(
	ruleForm,
	(val) => {
		// this.selectedNode.async = val.async
		// this.selectedNode.feignServiceName = val.feignServiceName
		// this.selectedNode.feignServicePath = val.feignServicePath
		// this.selectedNode.taskType = val.taskType
		// this.selectedNode.triggerable = val.triggerable
		// this.selectedNode.propsData = JSON.stringify({
		//   async: val.async,
		//   feignServiceName: val.feignServiceName,
		//   feignServicePath: val.feignServicePath,
		//   taskType: val.taskType,
		//   triggerable: val.triggerable,
		//   id: val.id,
		//   nodeName: val.nodeName
		// })
		// this.selectedNode.nodeInfo = {
		//   async: val.async,
		//   feignServiceName: val.feignServiceName,
		//   feignServicePath: val.feignServicePath,
		//   taskType: val.taskType,
		//   triggerable: val.triggerable
		// }
	},
	{ deep: true }
);

// 生命周期钩子
const created = () => {
	// 初始化逻辑
};

// 方法
const methods = {};

// 调用 created 钩子
created();
</script>
<style lang="scss" scoped></style>
