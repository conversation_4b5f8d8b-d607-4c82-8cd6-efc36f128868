<template>
	<div>
	<el-popover placement="bottom-start" title="添加流程节点" width="350" trigger="click">
		<div class="node-select">
			<div @click="addApprovalNode">
				<UserFilled style="color: rgb(255, 148, 62);width:34px" />
				<span>审批人</span>
			</div>
			<div @click="addCcNode">  
				<Promotion style="color: rgb(50, 150, 250);width:34px" />
				<span>抄送人</span>
			</div>
			<div @click="addConditionsNode">
				<Share style="color: rgb(21, 188, 131);width:34px" />
				<span>条件分支</span>
			</div>
			<div @click="addConcurrentsNode">
				<Operation style="color: #718dff;width:34px" />
				<span>并行分支</span>
			</div>
			<div @click="addStart">
				<Operation style="color: #718dff;width:34px" />
				<span>开始</span>
			</div>
			<div @click="addEnd">
				<Operation style="color: #718dff;width:34px" />
				<span>结束</span>
			</div>
			<div @click="addService">
				<Operation style="color: #718dff;width:34px" />
				<span>服务</span>
			</div>
		</div>
		<template #reference>
		<el-button icon="el-icon-plus" style="height: 24px;" type="primary" size="small" circle />
	</template>

	</el-popover>
</div>
</template>

<script setup>
// 定义方法
const addApprovalNode = () => {
  emit('insertNode', 1)
}

const addCcNode = () => {
  emit('insertNode', 2)
}

const addConditionsNode = () => {
  emit('insertNode', 4)
}

const addConcurrentsNode = () => {
  emit('insertNode', 5)
}

const addStart = () => {
  emit('insertNode', 8)
}

const addEnd = () => {
  emit('insertNode', 9)
}

const addService = () => {
  emit('insertNode', 6)
}

// 定义事件发射器
const emit = defineEmits(['insertNode'])
</script>

<style lang="scss" scoped>
.node-select {
	div {
		display: inline-block;
		margin: 5px 5px;
		cursor: pointer;
		padding: 10px 15px;
		border: 1px solid #f8f9f9;
		background-color: #f8f9f9;
		border-radius: 10px;
		width: 130px;
		position: relative;
		span {
			position: absolute;
			left: 65px;
			top: 18px;
		}
		&:hover {
			background-color: #fff;
			box-shadow: 0 0 8px 2px #d6d6d6;
		}
		i {
			font-size: 25px;
			padding: 5px;
			border: 1px solid #dedfdf;
			border-radius: 14px;
		}
	}
}
</style>
