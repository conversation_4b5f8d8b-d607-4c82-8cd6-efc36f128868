<template>
	<div>
		<el-dialog class="synchronous" title="同步流程" v-model="synchronous" width="750px">
			<el-table ref="multipleTable" class="synchronous-table" :data="tableData" size="small" style="width: 100%" tooltip-effect="dark" height="58vh" @selection-change="handleSelectionChange">
				<el-table-column type="selection" width="55" />
				<el-table-column prop="orgName" align="left" label="组织机构" />
				<el-table-column prop="orgCode" align="left" label="代码" />
			</el-table>
			<!-- <div class="pagination">
				<el-pagination :current-page="pageParams.current" :page-sizes="pageSize" :page-size="pageParams.size" background layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
			</div> -->
			<div slot="footer" class="dialog-footer">
				<el-button size="small" @click="cancel">取 消</el-button>
				<el-button type="primary" size="small" @click="submitForm('form')">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrganizationList, updateProcessDefinition } from '/@/api/flow/flow/index.ts'

const synchronous = ref(false)
const tableData = ref([])
const pageSize = ref([10, 20, 30])
const total = ref(0)
const pageParams = reactive({
  current: 1,
  size: 5,
  total: 1
})
const multipleSelection = ref([])
const defineKey = ref('')

const cancel = () => {
  synchronous.value = false
  multipleSelection.value = []
  defineKey.value = ''
}

const sync = (data) => {
  defineKey.value = data.key || data.flowId
  getOrganizationList().then((res) => {
    tableData.value = res.data
    synchronous.value = true
    if (data) {
      nextTick(() => {
        toggleSelection(data)
      })
    }
  })
}

const submitForm = () => {
  if (multipleSelection.value.length === 0) {
    ElMessageBox.confirm('是否同步到所有企业', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      updateProcess()
    }).catch(() => {})
  } else {
    updateProcess()
  }
}

const updateProcess = () => {
  const orgCodes = multipleSelection.value.map(item => item.orgCode).join(',')
  const formData = new FormData()
  formData.append('defineKey', defineKey.value)
  formData.append('orgCodes', orgCodes)
  updateProcessDefinition(formData).then((res) => {
    if (res.code === 200) {
      ElMessage.success('添加成功')
      synchronous.value = false
      multipleSelection.value = []
      defineKey.value = ''
      emit('refresh')
    }
  }).catch(() => {})
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

const toggleSelection = (rows) => {
  const multipleTable = ref(null)
  multipleTable.value.clearSelection()
  // if (rows) {
  //   rows.forEach(row => {
  //     multipleTable.value.toggleRowSelection(row)
  //   })
  // } else {
  //   multipleTable.value.clearSelection()
  // }
}

const handleCurrentChange = (current) => {
  pageParams.current = current
  getList()
}

const handleSizeChange = (size) => {
  pageParams.size = size
  getList()
}

const getList = () => {
  // 获取列表数据的逻辑
}

const emit = defineEmits(['refresh'])
// 打开弹窗
const openDialog =  () => {
	synchronous.value = true;
};

</script>
<style scoped></style>
