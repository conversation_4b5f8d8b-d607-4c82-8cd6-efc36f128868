<template>
	<node
		title="开始"
		placeholder="自定义开始节点"
		:header-bgc="headerBgc"
		header-icon="el-icon-s-check"
		@selected="$emit('selected')"
		@delNode="$emit('delNode')"
		@insertNode="(type) => $emit('insertNode', type)"
	/>
</template>

<script setup>
import Node from './node-index.vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { computed, watch } from 'vue';
// 使用 Pinia store
const flowStore = useDesign();

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

// 计算属性
const diagramMode = computed(() => flowStore.diagramMode);
const startNode = computed(() => flowStore.startNode);
const content = computed(() => '自定义节点开始');
const headerBgc = computed(() => '#67C23A');

// 监听器
watch(
	() => props.config,
	(val) => {
		// 处理 config 变化
	},
	{ deep: true }
);

// 生命周期钩子
const created = () => {
	// this.$emit()
};

// 方法
const methods = {};

// 调用 created 钩子
created();
</script>

<style scoped></style>

<!-- 开始
<template>
	<div :class="{ node: true, root: isRoot || !show, 'node-error-state': showError }">
		<div v-if="show" :class="{ 'node-body': true, error: showError }">
			<div>
				<div class="node-body-header" :style="{ 'background-color': '#67C23A' }">
					<span>开始</span>
					<i v-if="!isRoot && $store.state.diagramMode !== 'viewer'" class="el-icon-close" style="float: right" @click="$emit('delNode')" />
				</div>
				<div class="node-body-content" @click="$emit('selected')">
					<span v-if="(content || '').trim() === ''" class="placeholder">自定义节点开始</span>
				</div>
			</div>
		</div>
		<div class="node-footer">
			<div class="btn">
				<insert-button v-show="$store.state.diagramMode !== 'viewer'" @insertNode="type => $emit('insertNode', type)" />
			</div>
		</div>
	</div>
</template>

<script>
import InsertButton from '@/views/system/workflow/flow/components/InsertButton.vue'

export default {
	name: 'Node',
	components: { InsertButton },
	props: {
		// 是否为根节点
		isRoot: {
			type: Boolean,
			default: false
		},
		// 是否显示节点体
		show: {
			type: Boolean,
			default: true
		},
		// 节点内容区域文字
		content: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: '标题'
		},
		placeholder: {
			type: String,
			default: '请设置'
		},
		// 节点体左侧图标
		leftIcon: {
			type: String,
			default: undefined
		},
		// 头部图标
		headerIcon: {
			type: String,
			default: ''
		},
		// 头部背景色
		headerBgc: {
			type: String,
			default: '#576a95'
		},
		// 是否显示错误状态
		showError: {
			type: Boolean,
			default: false
		},
		errorInfo: {
			type: String,
			default: '无信息'
		}
	},
	data() {
		return {}
	},
	created() {},
	methods: {}
}
</script>

<style lang="scss" scoped>
.root {
	&:before {
		display: none !important;
	}
}
.node-error-state {
	.node-body {
		box-shadow: 0px 0px 5px 0px #f56c6c !important;
	}
}
.node {
	padding: 0 50px;
	width: 220px;
	position: relative;
	&:before {
		content: '';
		position: absolute;
		top: -12px;
		left: 50%;
		-webkit-transform: translateX(-50%);
		transform: translateX(-50%);
		width: 0;
		border-style: solid;
		border-width: 8px 6px 4px;
		border-color: #cacaca transparent transparent;
		background: #f5f5f7;
	}
	.node-body {
		cursor: pointer;
		max-height: 120px;
		position: relative;
		border-radius: 5px;
		background-color: white;
		box-shadow: 0px 0px 5px 0px #d8d8d8;
		&:hover {
			box-shadow: 0px 0px 3px 0px #1890ff;
			.node-body-header {
				.el-icon-close {
					display: inline;
					font-size: medium;
				}
			}
		}
		.node-body-header {
			border-top-left-radius: 5px;
			border-top-right-radius: 5px;
			padding: 5px 15px;
			color: white;
			font-size: xx-small;
			.el-icon-close {
				display: none;
			}
			.name {
				height: 14px;
				width: 150px;
				display: inline-block;
			}
		}
		.node-body-content {
			padding: 18px;
			color: #656363;
			font-size: 14px;
			i {
				position: absolute;
				top: 55%;
				right: 5px;
				font-size: medium;
			}
			.placeholder {
				color: #8c8c8c;
			}
		}
		.node-error {
			position: absolute;
			right: -40px;
			top: 20px;
			font-size: 25px;
			color: #f56c6c;
		}
	}

	.node-footer {
		position: relative;
		.btn {
			width: 100%;
			display: flex;
			padding: 20px 0 32px;
			justify-content: center;
		}
		::v-deep .el-button {
			height: 32px;
		}
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: -1;
			margin: auto;
			width: 2px;
			height: 100%;
			background-color: #cacaca;
		}
	}
}
</style> -->
