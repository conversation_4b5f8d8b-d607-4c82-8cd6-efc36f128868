<template>
	<el-dialog :title="title" v-model="visible" width="800" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="cancel">
		<div class="picker" v-loading="vloading">
			<div class="candidate">
				<div v-if="type !== 'role'">
					<el-input v-model.trim="search" style="width: 95%" size="small" clearable placeholder="搜索" prefix-icon="el-icon-search" @input="searchUser" />
				</div>
				<div v-else class="role-header">
					<div>系统角色</div>
				</div>
				<div class="org-items" :style="type === 'role' || type === 'post' ? 'height: 350px' : ''">
					<el-empty v-show="orgs.length === 0" :image-size="100" description="似乎没有数据" />
					<div v-if="type === 'user'">
						<div v-for="(org, index) in orgs" :key="org.orgCode" :class="orgItemClass(org)">
							<div style="display: flex; align-items: center; margin-top: 4px" class="d2-ml-10">
								<div style="display: flex; align-items: center" @click="selectChangePeople(org)">
									<!-- <el-checkbox v-model="org.selected" :disabled="disableDept(org)" /> -->
									<proEllipsis hover-tip :row="1" :content="org.orgName" style="cursor: pointer">
										<span class="name">{{ org.orgName }}</span>
									</proEllipsis>
								</div>
								<span v-if="org.children && org.children.length > 0" style="display: inline-block; width: 40px; color: #2c8afd" @click.stop="nextNode(org)">
									<i class="iconfont icon-map-site" />
									下级
								</span>
							</div>
						</div>
					</div>
					<!-- 角色 -->
					<div v-else-if="type === 'role'">
						<div v-for="(org, index) in roleList" :key="index" :class="orgItemClass(org)" class="d2-ml-10">
							<div style="display: flex; align-items: center; margin-top: 4px">
								<div style="display: flex; align-items: center" @click="selectChange(org)">
									<el-checkbox v-model="org.selected" :disabled="disableDept(org)" />
									<proEllipsis hover-tip :row="1" :content="org.orgName">
										<span class="name">{{ org.orgName }}</span>
									</proEllipsis>
								</div>
							</div>
						</div>
					</div>
					<!-- 岗位 -->
					<div v-else-if="type === 'post'">
						<div v-for="(org, index) in postList" :key="index" :class="orgItemClass(org)" class="d2-ml-10">
							<div style="display: flex; align-items: center">
								<div style="display: flex; align-items: center" @click="selectChange(org)">
									<el-checkbox v-model="org.selected" @change="selectChange(org)" :disabled="disableDept(org)" />
									<proEllipsis hover-tip :row="1" :content="org.orgName">
										<span class="name">{{ org.orgName }}</span>
									</proEllipsis>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 用户、成员的节点 -->
			<div v-if="type === 'user' && deptStack.length !== 0" class="org-check">
				<div v-if="deptStack.length > 0" class="d2-mt-10 d2-ml-10">
					选择下级部门
					<!-- 第二级的部门人员展示 -->
					<div v-for="nextItem in deptStack" :key="nextItem.userId" style="display: flex; align-items: center; margin-left: 12px; margin-top: 4px">
						<div style="display: flex; align-items: center; cursor: pointer" @click="selectNextChangePeople(nextItem)">
							<!-- <el-checkbox v-model="nextItem.selected" :disabled="disableDept(nextItem)" class="d2-mr-10" /> -->
							<proEllipsis hover-tip :row="1" :content="nextItem.orgName">
								<span class="name">{{ nextItem.orgName }}</span>
							</proEllipsis>
						</div>
						<span v-if="nextItem.children && nextItem.children.length > 0" style="display: inline-block; width: 40px; color: #2c8afd; cursor: pointer" @click.stop="thirdNode(nextItem)">
							<i class="iconfont icon-map-site" />
							下级
						</span>
					</div>

					<!-- 第三级的部门人员展示 -->
					<div v-for="nextItem in thirdList" :key="nextItem.userId" style="display: flex; align-items: center; margin-left: 24px">
						<div style="cursor: pointer" @click="selectChangePeople(nextItem)">
							<el-checkbox v-model="nextItem.selected" @change="selectChange(org)" :disabled="disableDept(nextItem)" class="d2-mr-10" />
							{{ nextItem.orgName }}
						</div>
					</div>
				</div>
				<el-empty v-else :image-size="100" description="似乎没有数据" />
			</div>
			<div v-if="type === 'user'" class="org-check">
				<div class="d2-mt-10 d2-ml-10">
					<span>选择成员</span>
				</div>
				<div v-if="type === 'user'" class="d2-ml-20 d2-mt-10 d2-mb-20">
					<div v-if="nextChildren.length > 0">
						<div v-for="(org, index) in nextChildren" :key="index" :class="orgItemClass(org)">
							<div style="display: flex; align-items: center">
								<div style="display: flex; align-items: center" @click.stop="selectChange(org)">
									<el-checkbox v-model="org.selected" @change="selectChange(org)" :disabled="disableDept(org)" class="d2-mr-10" />
									<proEllipsis hover-tip :row="1" :content="org.name" style="cursor: pointer">
										<span class="name">{{ org.name }}</span>
									</proEllipsis>
								</div>
							</div>
						</div>
					</div>
					<el-empty v-else :image-size="100" description="似乎没有数据" />
				</div>
			</div>
			<div class="selected">
				<div class="count">
					<span>已选 {{ select.length }} 项</span>
					<span class="d2-mr-10" @click="clearSelected">清空</span>
				</div>
				<div class="org-items" style="height: 350px">
					<el-empty v-show="select.length === 0" :image-size="100" description="请点击左侧列表选择数据" />
					<div v-for="(org, index) in select" :key="index" :class="orgItemClass(org)" style="display: flex; align-items: center; margin-top: 4px" class="d2-ml-10 d2-mr-10">
						<div v-if="org.type === 'dept'">
							<i class="el-icon-folder-opened" />
							<proEllipsis hover-tip :row="1" :content="org.orgName">
								<span class="name">{{ org.orgName }}</span>
							</proEllipsis>
						</div>
						<div v-else-if="org.type === 'user'" style="display: flex; align-items: center">
							<proEllipsis hover-tip :row="1" :content="org.orgName">
								<span class="name">{{ org.orgName }}</span>
							</proEllipsis>
						</div>
						<div v-else-if="org.type === 'role'" style="display: flex; align-items: center">
							<proEllipsis hover-tip :row="1" :content="org.roleName">
								<span class="name">{{ org.roleName }}</span>
							</proEllipsis>
						</div>
						<div v-else-if="org.type === 'post'" style="display: flex; align-items: center">
							<proEllipsis hover-tip :row="1" :content="org.postName">
								<span class="name">{{ org.postName }}</span>
							</proEllipsis>
						</div>
						<div v-else>
							<proEllipsis hover-tip :row="1" :content="org.orgName">
								<span class="name">{{ org.orgName }}</span>
							</proEllipsis>
						</div>
						<i class="el-icon-close" @click="noSelected(index, org)" />
					</div>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="org-btn d2-mt-10">
				<el-button size="small" @click="close">取 消</el-button>
				<el-button type="primary" size="small" @click="selectOk">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { getOrgTree, findUserListByOrgCode, getRoleList, getPostList } from '/@/api/flow/flow/index.ts';
import { handleTree, getValue } from '/@/views/platform-manage/flow/workflow/flow/hooks/tree.js';
import proEllipsis from './pro-ellipsis.vue';

// 定义props
const props = defineProps({
	title: {
		default: '请选择',
		type: String,
	},
	multiple: {
		default: false,
		type: Boolean,
	},
	selected: {
		default: () => [],
		type: Array,
	},
});

// 定义emit
const emit = defineEmits(['ok', 'close']);

// 响应式状态
const visible = ref(false);
const type = ref('user');
const vloading = ref(false);
const checkAll = ref(false);
const nowDeptId = ref(null);
const isIndeterminate = ref(false);
const searchUsers = ref([]);
const nodes = ref([]);
const select = ref(props.selected.slice());
const search = ref('');
const deptStack = ref([]);
const thirdList = ref([]);
const roleList = ref([]);
const postList = ref([]);
const nextChildren = ref([]);

// 计算属性
const deptStackStr = computed(() => {
	return deptStack.value.map((v) => v.orgName).join(' > ');
});

const orgs = computed(() => {
	return !search.value || search.value.trim() === '' ? nodes.value : searchUsers.value;
});

const showUsers = computed(() => {
	return search.value && search.value.trim() !== '';
});

// 监听selected变化
watch(() => props.selected, (val) => {
  select.value = [...val];
  // 强制更新视图（在Vue 3中通常不需要）
}, { deep: true });

// 方法定义
function show(typeVal = 'user') {
	visible.value = true;
	type.value = typeVal;
	init();
	if (typeVal === 'role') {
		getRoleLists();
	} else if (typeVal === 'post') {
		getPostLists();
	} else {
		getOrgList();
		const node = {
			orgCode: 10000, // 这里假设info是 info.orgCode上下文中的一个变量
		};
		selectChangePeople(node);
	}
}

function orgItemClass(org) {
	return {
		'org-item': true,
		'org-dept-item': org.type === 'dept',
		'org-user-item': org.type === 'user',
		'org-post-item': org.type === 'post',
		'org-role-item': org.type === 'role',
	};
}

function disableDept(node) {
	return type.value === 'user' && node.type === 'dept';
}

async function getOrgList() {
	vloading.value = true;
	try {
		const rsp = await getOrgTree();
		vloading.value = false;
		let treeList = [];
		treeList = handleTree(rsp.data, 'orgId', 'parentId');
		// 排序
		treeList.sort((a, b) => a.sort - b.sort);
		sortTree(treeList);

		nodes.value = treeList[0].children;
		selectToLeft();
	} catch (err) {
		vloading.value = false;
		console.error(err.response.data); // 使用console.error代替$messsage.error
	}
}

function sortTree(tree) {
	tree &&
		tree.forEach((node) => {
			if (node.children && node.children.length > 0) {
				node.children.sort((a, b) => a.sort - b.sort);
				sortTree(node.children);
			}
		});
}

function getShortName(orgName) {
	if (orgName) {
		return orgName.length > 2 ? orgName.substring(1, 3) : orgName;
	}
	return '**';
}

async function searchUser() {
	const userName = search.value.trim();
	searchUsers.value = [];
	vloading.value = true;
	try {
		const rsp = await getOrgTree({ orgName: userName });
		vloading.value = false;
		searchUsers.value = rsp.data;
		selectToLeft();
	} catch (err) {
		vloading.value = false;
		console.error('接口异常'); // 使用console.error代替$message.error
	}
}

function selectToLeft() {
	const nodesToProcess = search.value.trim() === '' ? nodes.value : searchUsers.value;
	nodesToProcess &&
		nodesToProcess.forEach((node) => {
			for (let i = 0; i < select.value.length; i++) {
				if (select.value[i].userId === node.userId) {
					node.selected = true;
					break;
				} else {
					node.selected = false;
				}
			}
		});
	nodes.value = [...nodesToProcess];
}

async function selectChangePeople(node) {
	deptStack.value = [];
	nextChildren.value = [];
	const params = {
		orgCode: node.orgCode,
		size: 9999,
	};
	const { code, data } = await findUserListByOrgCode(params);
	if (code !== 200) return;

	data.records &&
		data.records.forEach((item) => {
			item.orgName = item.name;
			// 处理左侧选中
			if (select.value.length > 0) {
				for (let i = 0; i < select.value.length; i++) {
					if (select.value[i].userId === item.userId) {
						item.selected = true;
						break;
					} else {
						item.selected = false;
					}
				}
			}
		});

	nextChildren.value = data.records;
}

async function selectNextChangePeople(node) {
	nextChildren.value = [];
	const params = {
		orgCode: node.orgCode,
		size: 9999,
	};
	const { code, data } = await findUserListByOrgCode(params);
	if (code !== 200) return;

	data.records &&
		data.records.forEach((item) => {
			item.orgName = item.name;
			// 处理左侧选中
			if (select.value.length > 0) {
				for (let i = 0; i < select.value.length; i++) {
					if (select.value[i].userId === item.userId) {
						item.selected = true;
						break;
					} else {
						item.selected = false;
					}
				}
			}
		});

	nextChildren.value = data.records;
}

function selectChange(node) {
	if (node.selected) {
		checkAll.value = false;
		for (let i = 0; i < select.value.length; i++) {
			if (select.value[i].userId === node.userId) {
				select.value.splice(i, 1);
				break;
			}
		}
		node.selected = false;
	} else if (!disableDept(node)) {
		node.selected = true;
		if (!props.multiple) {
			nodes.value &&
				nodes.value.forEach((nd) => {
					if (node.userId !== nd.userId) {
						nd.selected = false;
					}
				});
		}
		if (node.type === 'dept') {
			if (!props.multiple) {
				select.value = [node];
			} else {
				select.value.unshift(node);
			}
		} else {
			if (!props.multiple) {
				select.value = [node];
			} else {
				select.value.push(node);
			}
		}
	}
}

function noSelected(index) {
	if (type.value === 'role') {
		let nodes = roleList.value;
		for (let f = 0; f < 2; f++) {
			for (let i = 0; i < nodes.length; i++) {
				if (nodes[i].userId === select.value[index].userId) {
					nodes[i].selected = false;
					checkAll.value = false;
					break;
				}
			}
			nodes = searchUsers.value;
		}
	} else {
		let nodes = nextChildren.value;
		for (let f = 0; f < 2; f++) {
			for (let i = 0; i < nodes.length; i++) {
				if (nodes[i].userId === select.value[index].userId) {
					nodes[i].selected = false;
					checkAll.value = false;
					break;
				}
			}
			nodes = searchUsers.value;
		}
	}
	select.value.splice(index, 1);
}

function handleCheckAllChange() {
	nodes.value &&
		nodes.value.forEach((node) => {
			if (checkAll.value) {
				if (!node.selected && !disableDept(node)) {
					node.selected = true;
					select.value.push(node);
				}
			} else {
				node.selected = false;
				for (let i = 0; i < select.value.length; i++) {
					if (select.value[i].userId === node.userId) {
						select.value.splice(i, 1);
						break;
					}
				}
			}
		});
}

function nextNode(node) {
	nextChildren.value = [];
	deptStack.value = [];
	thirdList.value = [];
	nowDeptId.value = node.userId;
	deptStack.value = node.children.length > 0 ? [...node.children] : [];
}

function thirdNode(node) {
	nextChildren.value = [];
	thirdList.value = [];
	thirdList.value = node.children.length > 0 ? [...node.children] : [];
}

function beforeNode() {
	if (deptStack.value.length === 0) {
		return;
	}
	if (deptStack.value.length < 2) {
		nowDeptId.value = null;
	} else {
		nowDeptId.value = deptStack.value[deptStack.value.length - 2].userId;
	}
	deptStack.value.splice(deptStack.value.length - 1, 1);
	getOrgList();
}

function recover() {
	select.value = [];
	nodes.value && nodes.value.forEach((nd) => (nd.selected = false));
	nextChildren.value && nextChildren.value.forEach((nd) => (nd.selected = false));
	roleList.value = [];
}

function selectOk() {
	emit(
		'ok',
		select.value.map((v) => ({ ...v }))
	);
	visible.value = false;
	recover();
}

function clearSelected() {
	recover();
}

function close() {
	emit('close');
	recover();
	visible.value = false;
}

function init() {
	checkAll.value = false;
	nowDeptId.value = null;
	deptStack.value = [];
	nodes.value = [];
	select.value = [...props.selected];

	selectToLeft();
}

async function getRoleLists() {
	const res = await getRoleList({ roleType: 1 });
	res.data &&
		res.data.forEach((item) => {
			item.orgName = item.roleName;
			item.userId = item.roleCode;
			// 处理左侧选中
			if (select.value.length > 0 && type.value === 'role') {
				for (let i = 0; i < select.value.length; i++) {
					if (select.value[i].userId === item.userId) {
						item.selected = true;
						break;
					} else {
						item.selected = false;
					}
				}
			}
		});
	roleList.value = res.data || [];
	nodes.value = res.data || [];
}

async function getPostLists() {
	const res = await getPostList({});
	res.data &&
		res.data.forEach((item) => {
			item.orgName = item.postName;
			item.userId = item.postCode;
			// 处理左侧选中
			if (select.value.length > 0 && type.value === 'post') {
				for (let i = 0; i < select.value.length; i++) {
					if (select.value[i].userId === item.userId) {
						item.selected = true;
						break;
					} else {
						item.selected = false;
					}
				}
			}
		});
	postList.value = res.data || [];
	nodes.value = res.data || [];
}
const cancel = () => {
	visible.value = false;
};
defineExpose({ show });
</script>

<style lang="scss" scoped>
.candidate,
.selected {
	width: 45%;
	height: 400px;
	border: 1px solid #e8e8e8;
}
.name {
	cursor: pointer;
	margin-left: 5px;
}
.picker {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 402px;
	text-align: left;
	.candidate {
		left: 0;
		top: 0;

		.role-header {
			padding: 10px !important;
			margin-bottom: 5px;
			border-bottom: 1px solid #e8e8e8;
		}

		.top-dept {
			margin-left: 20px;
			cursor: pointer;
			color: #38adff;
		}
		.next-dept {
			float: right;
			color: #1890ff;
			cursor: pointer;
		}

		.next-dept-disable {
			float: right;
			color: #8c8c8c;
			cursor: not-allowed;
		}

		& > div:first-child {
			padding: 5px 10px;
		}
	}

	.selected {
		right: 0;
		top: 0;
	}

	.org-items {
		overflow-y: auto;
		height: 310px;

		.el-icon-close {
			position: absolute;
			right: 5px;
			cursor: pointer;
			font-size: larger;
		}
		.org-dept-item {
			padding: 10px 5px;

			& > div {
				display: inline-block;

				& > span:last-child {
					position: absolute;
					right: 5px;
				}
			}
		}

		.org-role-item {
			display: flex;
			align-items: center;
			padding: 10px 5px;
		}

		::v-deep .org-user-item {
			display: flex;
			align-items: center;
			padding: 5px;

			& > div {
				display: inline-block;
			}

			.avatar {
				width: 35px;
				text-align: center;
				line-height: 35px;
				background: #1890ff;
				color: white;
				border-radius: 50%;
			}
		}

		::v-deep .org-item {
			margin: 0 5px;
			border-radius: 5px;
			position: relative;

			.el-checkbox {
				margin-right: 10px;
			}

			.name {
				cursor: pointer;
				margin-left: 5px;
			}

			&:hover {
				cursor: pointer;
				background: #f1f1f1;
			}
		}
	}
}

.org-check {
	width: 400px;
	overflow-y: auto;
	height: 400px;
	border-top: 1px solid #e8e8e8;
	border-bottom: 1px solid #e8e8e8;
	border-right: 1px solid #e8e8e8;
}

.selected {
	border-left: none;

	.count {
		width: 94%;
		padding: 10px;
		display: inline-block;
		border-bottom: 1px solid #e8e8e8;
		margin-bottom: 5px;
		& > span:nth-child(2) {
			float: right;
			color: #c75450;
			cursor: pointer;
		}
	}
}

::v-deep .el-dialog__body {
	padding: 10px 20px;
}

.disabled {
	cursor: not-allowed !important;
	color: #8c8c8c !important;
}

::-webkit-scrollbar {
	float: right;
	width: 4px;
	height: 4px;
	background-color: white;
}

::-webkit-scrollbar-thumb {
	border-radius: 16px;
	background-color: #efefef;
}

.org-btn {
	text-align: right;
}
.d2-mt-10 {
	margin-top: 10px;
}
.d2-mt-20 {
	margin-top: 20px;
}
.d2-mr-10 {
	margin-right: 10px;
}
.d2-mr-20 {
	margin-right: 20px;
}
.d2-mb-10 {
	margin-bottom: 10px;
}
.d2-mb-20 {
	margin-bottom: 20px;
}
.d2-ml-10 {
	margin-left: 10px;
}
.d2-ml-20 {
	margin-left: 20px;
}
</style>
