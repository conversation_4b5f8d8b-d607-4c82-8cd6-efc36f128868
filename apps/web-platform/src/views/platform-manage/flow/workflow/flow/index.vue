<template>
	<div>
		<div class="scale">
			<el-button icon="el-icon-plus" size="small" :disabled="scale >= 150" circle @click="scale += 10" />
			<span>{{ scale }}%</span>
			<el-button icon="el-icon-minus" size="small" :disabled="scale <= 40" circle @click="scale -= 10" />
		</div>
		<div class="design" :style="'transform: scale(' + scale / 100 + ');'">
			<!-- <div @click="nodeSelected">点我</div>	 -->
			<process-tree ref="process-tree" @selectedNode="nodeSelected" />
		</div>
		<el-drawer v-model="showConfig" :size="selectedNode.type === 4 ? '600px' : '500px'" :modal-append-to-body="false">
			<template #header>
				<span style="font-size: medium">{{ selectedNode.name }}</span>
			</template>
			<div class="node-config-content">
				<node-config />
			</div>
		</el-drawer>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useDesign } from './flow.js';
const ProcessTree = defineAsyncComponent(() => import('./common/process/process-tree.vue'));
const NodeConfig = defineAsyncComponent(() => import('./common/process/config/node-config.vue'));

// 定义响应式状态
const scale = ref(100);
const selected = ref({});
const showInput = ref(false);
const showConfig = ref(false);
const drawer = ref(false);

// 使用 Pinia
const flowStore = useDesign();

// 计算属性
const design = computed(() => flowStore.design);
const selectedNode = computed(() => flowStore.$state.selectedNode);

// 生命周期钩子
onMounted(() => {
	// flowStore.setDiagramMode('viewer')
});

// 方法
const validate = () => {
	return $refs['process-tree'].validateProcess();
};

const nodeSelected = (node, drawShow) => {
	// showConfig.value = true
	if ([5, 8, 9].includes(node.type)) {
		showConfig.value = false;
	} else {
		showConfig.value = drawShow;
	}
};
</script>

<style lang="scss" scoped>
.design {
	margin-top: 60px;
	display: flex;
	transform-origin: 50% 0px 0px;
}

.scale {
	z-index: 999;
	margin-top: 10px;
	text-align: right;

	span {
		margin: 0 10px;
		font-size: 15px;
		color: #7a7a7a;
		width: 50px;
	}
}

// .node-config-content {
// 	padding: 0 20px 20px;
// }

::v-deep .el-drawer__body {
	overflow-y: auto;
}
</style>
