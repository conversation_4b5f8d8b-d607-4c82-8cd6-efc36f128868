<template>
  <div :class="{ line: row === 1, lines: row > 1 }" :title="hoverTip ? content : null" :style="{ '--row': row, width: `${width}px` }">
    <slot v-if="!isTitle" name="pre" />
    <span v-if="!isTitle">
      {{ content }}
    </span>
    <div v-else contenteditable="true">
      {{ content }}
    </div>
  </div>
</template>

// 超出指定行数自动隐藏文字
<script setup>
defineProps({
  row: {
    type: Number,
    default: 1
  },
  hoverTip: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  },
  isTitle: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '180'
  }
})

// 定义组件名称
defineComponent({ name: 'Ellipsis' })
</script>

<style lang="scss" scoped>
.line {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.lines {
	display: -webkit-box;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
