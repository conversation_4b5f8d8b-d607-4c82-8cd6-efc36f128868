import { defineStore } from 'pinia'
import {ref ,reactive} from 'vue'
export const useDesignStore = defineStore({
  id: 'design',
  state: () => ({
    nodeMap: new Map(),
    isEdit: null,
    selectedNode: {
      type: 0,
      id: '',
      nodeName: '',
      parentId: '',
      props: ''
    },
    selectFormItem: null,
    design: reactive({
      process: {
        id: 'root',
        parentId: null,
        type: 0,
        nodeName: '发起人',
        desc: '任何人',
        props: {
          assignedUser: [],
          role: [],
          post: [],
          expression: ''
        }
        // children: {}
        // formItems: []
      }
    }),
    runningList: [],
    noTakeList: [],
    endList: [],
    diagramMode: 'design',
    approvalNode: {}, // 审批人节点
    ccNode: {}, // 抄送人节点
    conditionNode: {}, // 条件分支节点
    serviceNode: {
      // 服务节点
      taskType: 'feign',
      feignServiceName: '',
      feignServicePath: '',
      triggerable: false,
      async: true
    },
    initiator: {
      id: 'root',
      parentId: null,
      type: 0,
      nodeName: '发起人',
      desc: '任何人',
      props: {
        assignedUser: [],
        role: [],
        post: [],
        expression: ''
      },
      placeholder: ''
    }, // 发起人节点
    domNode: ref({}), // 使用 ref 使 domNode 响应式
    variableForm: [] // 变量设计
  }),
  actions: {
    // 保留原有的逻辑
    setSelectedNode(val) {
      this.selectedNode = val
    },
    loadForm(val) {
      this.design = val
    },
    setIsEdit(val) {
      this.isEdit = val
    },
    setDiagramMode(val) {
      this.diagramMode = val
    },
    approvalNode(val) {
      this.approvalNode = val
    },
    ccNode(val) {
      this.ccNode = val
    },
    conditionNode(val) {
      this.conditionNode = val
    },
    serviceNode(val) {
      this.serviceNode = val
    },
    initiatorNode(val) {
      this.initiator = val
    },
    setDomNode(val) {
      this.domNode.value = val
    },
    setVariableForm(val) {
      this.variableForm = val
    }
  }
})

// 如果需要在组件中使用这个 store
export function useDesign() {
  return useDesignStore()
}