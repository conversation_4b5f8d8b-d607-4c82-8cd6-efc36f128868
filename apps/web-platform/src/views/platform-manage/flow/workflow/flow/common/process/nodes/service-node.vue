<!-- 服务 -->
<template>
	<node
		:title="config.nodeName"
		:show-error="showError"
		:error-info="errorInfo"
		placeholder="请设置服务"
		:header-bgc="headerBgc"
		header-icon="el-icon-s-check"
		@selected="$emit('selected')"
		@delNode="$emit('delNode')"
		@insertNode="(type) => $emit('insertNode', type)"
	/>
</template>

<script setup>
import Node from './node-index.vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { computed, ref } from 'vue';

// 使用 Pinia store
const flowStore = useDesign();

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

// 数据
const showError = ref(false);
const errorInfo = ref('');

// 计算属性
const diagramMode = computed(() => flowStore.diagramMode);
const headerBgc = computed(() => {
	if (diagramMode.value === 'viewer') {
		return props.config.props?.headerBgc || '#ff943e';
	} else {
		return '#ff943e';
	}
});

// 方法
const methods = {};
</script>

<style scoped></style>
