// useTree.js

import { reactive, ref } from 'vue';

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * * @param {string} [id='id'] id字段
 * @param {string} [parentId='parentId'] 父节点字段
 * @param {string} [children='children'] 孩子节点字段
 */
function handleTree(data, id = 'id', parentId = 'parentId', children = 'children') {
    const config = reactive({
        id,
        parentId,
        children
    });

    const childrenListMap = reactive({});
    const nodeIds = reactive({});
    const tree = ref([]);

    // 创建子节点映射表
    for (const d of data) {
        const parentId = d[config.parentId];
        if (!childrenListMap[parentId]) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    // 找出根节点
    for (const d of data) {
        const parentId = d[config.parentId];
        if (!nodeIds[parentId]) {
            tree.value.push(d);
        }
    }

    // 递归设置子节点
    function adaptToChildrenList(node) {
        const nodeId = node[config.id];
        if (childrenListMap[nodeId] !== null) {
            node[config.children] = childrenListMap[nodeId];
        }
        if (node[config.children]) {
            for (const child of node[config.children]) {
                adaptToChildrenList(child);
            }
        }
    }

    tree.value.forEach(adaptToChildrenList);

    return tree.value;
}

/**
 * 处理回显数据
 * @param {*} data 数据源
 * @returns
 */
function getValue(data) {
    const result = {};
    for (const key in data) {
        const obj = data[key];
        if (obj && Object.prototype.hasOwnProperty.call(obj, 'value') && Object.prototype.hasOwnProperty.call(obj, 'text')) {
            result[key] = obj.value;
        } else {
            result[key] = obj;
        }
    }
    return result;
}

export { handleTree, getValue };