<template>
	<div>
		<!-- <p class="desc">选择能发起该审批的人员/部门，不选则默认开放给所有人</p> -->
		<el-button size="mini" icon="el-icon-plus" style="width: 40px; height: 40px" round @click="selectOrg" />
		<org-items v-model="select" />
		<org-picker ref="orgPickerRef" title="发起人" multiple type="user" :selected="select" @ok="selected" />
	</div>
</template>

<script setup>
import { ref, computed } from 'vue';
// 动态导入组件
import OrgPicker from '/@/views/platform-manage/flow/workflow/flow/components/org-picker.vue';
const OrgItems = defineAsyncComponent(() => import('../org-items.vue'));
const orgPickerRef = ref();
const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

const select = computed({
	get() {
		return props.config.assignedUser || [];
	},
	set(value) {
		props.config.assignedUser = value;
	},
});

function selectOrg() {
	if (orgPickerRef.value) {
		orgPickerRef.value.show();
	}
}

function selected(selectedItems) {
	console.log('发起人', selectedItems);
	select.value = selectedItems;
}

function removeOrgItem(index) {
	select.value.splice(index, 1);
}
</script>

<style lang="scss" scoped>
.desc {
	font-size: small;
	color: #8c8c8c;
}
.org-item {
	margin: 5px;
}
::v-deep .el-button--mini.is-round {
	padding: 0;
}
</style>
