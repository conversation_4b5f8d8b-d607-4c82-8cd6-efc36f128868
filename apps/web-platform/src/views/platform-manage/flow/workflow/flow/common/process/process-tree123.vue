<template>
  <div class="_root" ref="_root" style="margin: 0 auto;">
    <!-- <div v-if="dom?.childNode?.type === 3"></div>
    <div v-for="treeItem in getDomTree(dom)" :key="treeItem.key">{{treeItem}}1111111</div>
    <div style="text-align: center">
      <div class="process-end">流程结束</div>
    </div> -->
    <div class="tree-primary-node">
      <template v-for="(node, index) in domComponent" :key="index"">
        <!-- <component  :is=" node.component" :config="node.config" @selected="selectNode(node.eventsds.node)"
        @insertNode="insertNode(1,node.eventsds.node)" /> -->
</template>

</div>

</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted ,h } from 'vue'
import Approval from './nodes/approval-node.vue'
import Cc from './nodes/cc-node.vue'
import Concurrent from './nodes/concurrent-node.vue'
import Condition from './nodes/condition-node.vue'
import Empty from './nodes/empty-node.vue'
import Root from './nodes/root-node.vue'
import RootParNode from './tree-node/tree-primary-node.vue'

import Node from './nodes/node-index.vue'
import Service from './nodes/service-node.vue'
import Start from './nodes/start-node.vue'
import End from './nodes/end-node.vue'
import DefaultProps from './default-node-props'
import { useDesign } from '../../flow.js'

const flowStore = useDesign()

const valid = ref(true)
const dom = reactive(flowStore.design.process)
const nodeMap = computed(() => flowStore.nodeMap)
const emit = defineEmits(['selectedNode']);
const domComponent = ref([]);

onMounted(() => {
  console.log(flowStore, 'process tree mounted')
})

function getDomTree(node) {

  toMapping(node);
    if (isPrimaryNode(node)) {
        // 普通业务节点
        const childDoms = getDomTree(node.childNode);
        decodeAppendDom(node, childDoms);
        return  decodeAppendDom(node, childDoms);
    } else if (isBranchNode(node)) {
        let index = 0;

        // 遍历分支节点，包含并行及条件节点
        const branchItems = node.conditionNodes.map(branchNode => {
            // 处理每个分支内子节点
            toMapping(branchNode);
            const childDoms = getDomTree(branchNode.childNode);
            decodeAppendDom(branchNode, childDoms, { level: index + 1, size: node.conditionNodes.length });

            // 插入4条横线，遮挡掉条件节点左右半边线条
            insertCoverLine(index, childDoms, node.conditionNodes);

            // 遍历子分支尾部分支
            index++;

            return h('div', { class: { 'branch-node-item': true } }, childDoms);
        });

        // 插入添加分支/条件的按钮
        branchItems.unshift(
            h('div', { class: { 'add-branch-btn': true } }, [
                h(
                    'el-button',
                    {
                        class: { 'add-branch-btn-el': true },
                        props: { size: 'small', round: true },
                        onClick: () => addBranchNode(node),
                        domProps: { innerHTML: `添加${isConditionNode(node) === 4 ? '条件' : '分支'}` }
                    },
                    []
                )
            ])
        );

        const bchDom = [h('div', { class: { 'branch-node': true } }, branchItems)];

        // 继续遍历分支后的节点
        const afterChildDoms = getDomTree(node.childNode);
        return [h('div', {}, [bchDom, afterChildDoms])];
    } else if (isEmptyNode(node)) {
        // 空节点，存在于分支尾部
        const childDoms = getDomTree(node.childNode);
        decodeAppendDom(node, childDoms);
        return [h('div', { class: { 'empty-node': true } }, childDoms)];
    } else {
        // 遍历到了末端，无子节点
        return [];
    }
}

const currentComponent = computed(() => {
  return getDomTree(dom)
})
 getDomTree(dom)
function decodeAppendDom(node, dom, props = {}) {
      props.config = node
			let domType = null
			switch (node.type) {
				case 0:
					domType = Root
					break
				case 1:
					domType = Approval
					break
				case 2:
					domType = Cc
					break
				case 4:
					domType = Condition
					break
				case 5:
					domType = Concurrent
					break
				case 6:
					domType = Service
					break
				case 8:
					domType = Start
					break
				case 9:
					domType = End
					break
				case 3: // 条件分支和并行分支的空数据结构 - 为了显示添加按钮
					domType = Empty
					break
			}
      domComponent.value.push({domType:1,config:props.config,component:domType,eventsds:{node}})

      // return domComonent
			// dom.unshift(
			// 	h(
			// 		domType,
			// 		{
			// 			config: props.config,
			// 			ref: node.id,
			// 			key: node.id,
			// 			// 定义事件，插入节点，删除节点，选中节点，复制/移动
			// 			onInsertNode: type => insertNode(type, node),
			// 			onDelNode: () => delNode(node),
			// 			onSelected: () => selectNode(node),
			// 			// copy: () => this.copyBranch(node),
			// 			onLeftMove: () => branchMove(node, -1),
			// 			onRightMove: () => branchMove(node, 1)
			// 		},
			// 		[]
			// 	)
			// )
	
		
			// 保存整个树形结构树
			flowStore.setDomNode(node)
			// this.$store.commit('flow/domNode', node)
}

function toMapping(node) {
  if (node && node.id) {
    nodeMap.value.set(node.id, node)
  }
}

function insertCoverLine(index, doms, conditionNodes) {
  if (index === 0) {
				// 最左侧分支
				doms.unshift(h('div', { class: { 'line-top-left': true } }, []))
				doms.unshift(h('div', { class: { 'line-bot-left': true } }, []))
			} else if (index === conditionNodes.length - 1) {
				// 最右侧分支
				doms.unshift(h('div', { class: { 'line-top-right': true } }, []))
				doms.unshift(h('div', { class: { 'line-bot-right': true } }, []))
			}
}

function copyBranch(node) {
  const parentNode = nodeMap.value.get(node.parentId)
			const branchNode = $deepCopy(node)
			branchNode.name = branchNode.name + '-copy'
			forEachNode(parentNode, branchNode, (parent, node) => {
				const id = getRandomId()
				console.log(node, '新id =>' + id, '老nodeId:' + node.id)
				node.id = id
				node.parentId = parent.id
			})
			parentNode.conditionNodes.splice(parentNode.conditionNodes.indexOf(node), 0, branchNode)
			// $forceUpdate()
}

function branchMove(node, offset) {
  // 逻辑与原版相同...
}

function isPrimaryNode(node) {
  return node && [0, 1, 2, 8, 9, 6].includes(node.type)
}

function isBranchNode(node) {
  return node && [4, 5].includes(node.type)
}

function isEmptyNode(node) {
  return node && node.type === 3
}

function isConditionNode(node) {
  return node.type === 4
}

function getRandomId() {
  return `node_${new Date().getTime().toString().substring(5)}${Math.round(Math.random() * 9000 + 1000)}`
}

function selectNode(node, drawShow = true) {
  console.log('选中的节点', flowStore)
  flowStore.setSelectedNode(node)
  emit('selectedNode', node, drawShow)
}

function insertNode(type, parentNode) {
  console.log('添加节点', type, parentNode)
  domComponent.value.push({domType:1,config:dom,component:Approval,eventsds:{parentNode}})
  console.log(domComponent, '执行了-------------------------process tree mounted')

  return

  const afterNode = parentNode.childNode || null
  parentNode.childNode = {
    id: getRandomId(),
    parentId: parentNode.id,
    props: {},
    type: type,
    childNode: null,
    editable: true
  }
  switch (type) {
    case 1:
      insertApprovalNode(parentNode, afterNode)
      break
    case 2:
      insertCcNode(parentNode, afterNode)
      break
    case 4:
      insertConditionsNode(parentNode)
      break
    case 5:
      insertConcurrentsNode(parentNode)
      break
    case 6:
      insertServiceNode(parentNode)
      break
    case 8:
      insertStartNode(parentNode)
      break
    case 9:
      insertEndNode(parentNode)
      break
    default:
      break
  }
  if (isBranchNode({ type })) {
    if (afterNode && afterNode.id) {
      afterNode.parentId = parentNode.childNode.childNode.id
    }
    parentNode.childNode.childNode = afterNode
  } else {
    if (afterNode && afterNode.id) {
      afterNode.parentId = parentNode.childNode.id
    }
    parentNode.childNode.childNode = afterNode
  }
}

function insertApprovalNode(parentNode) {
  domComponent.value.push({domType:1,config:DefaultProps.APPROVAL_PROPS,component:Approval,eventsds:{parentNode}})

  parentNode.childNode.nodeName = '审批人'
  parentNode.childNode.props = JSON.parse(JSON.stringify(DefaultProps.APPROVAL_PROPS))
  const node = {
    childNode: {},
    ...parentNode.childNode
  }
  flowStore.setSelectedNode(node)
  selectNode(parentNode, false)
}

function insertCcNode(parentNode) {
  parentNode.childNode.nodeName = '抄送人'
  parentNode.childNode.props = JSON.parse(JSON.stringify(DefaultProps.CC_PROPS))
  flowStore.setSelectedNode(parentNode)
  selectNode(parentNode, false)
}

function insertStartNode(parentNode) {
  parentNode.childNode.nodeName = '自定义节点开始'
  const props = {
    id: parentNode.childNode.id,
    nodeName: parentNode.childNode.nodeName,
    parentId: parentNode.childNode.parentId,
    type: parentNode.childNode.type
  }
  parentNode.childNode.props = props
  parentNode.childNode.propsData = JSON.stringify(props)
  parentNode.childNode.childNode = {}
  const node = {
    childNode: {},
    ...parentNode.childNode
  }
  flowStore.setSelectedNode(node)
  selectNode(parentNode, false)
}

function insertEndNode(parentNode) {
  parentNode.childNode.nodeName = '自定义节点结束'
  const props = {
    id: parentNode.childNode.id,
    nodeName: parentNode.childNode.nodeName,
    parentId: parentNode.childNode.parentId,
    type: parentNode.childNode.type
  }
  parentNode.childNode.props = props
  parentNode.childNode.propsData = JSON.stringify(props)
  parentNode.childNode.childNode = null
  const node = {
    childNode: null,
    ...parentNode.childNode
  }
  flowStore.setSelectedNode(node)
  selectNode(node, false)
}

function insertServiceNode(parentNode) {
  parentNode.childNode.nodeName = '服务'
  parentNode.childNode.props = JSON.parse(JSON.stringify(DefaultProps.SERVICE_PROPS))
  parentNode.childNode.childNode = null
  flowStore.setSelectedNode(parentNode)
  selectNode(parentNode, false)
}

function insertConditionsNode(parentNode) {
  parentNode.childNode.nodeName = '条件分支'
  parentNode.childNode.childNode = {
    id: getRandomId(),
    parentId: parentNode.childNode.id,
    type: 3,
    childNode: null
  }
  parentNode.childNode.conditionNodes = [
    {
      id: getRandomId(),
      parentId: parentNode.childNode.id,
      type: 4,
      props: {
        nodeName: '条件1',
        id: getRandomId(),
        parentId: parentNode.childNode.id,
        ...JSON.parse(JSON.stringify(DefaultProps.CONDITION_PROPS))
      },
      nodeName: '条件1',
      childNode: null
    },
    {
      id: getRandomId(),
      parentId: parentNode.childNode.id,
      type: 4,
      props: {
        nodeName: '条件2',
        id: getRandomId(),
        parentId: parentNode.childNode.id,
        ...JSON.parse(JSON.stringify(DefaultProps.CONDITION_PROPS))
      },
      nodeName: '条件2',
      childNode: null
    }
  ]
  flowStore.setSelectedNode(parentNode)
  selectNode(parentNode, false)
  // $forceUpdate()
}

function insertConcurrentsNode(parentNode) {
  parentNode.childNode.nodeName = '并行分支'
  parentNode.childNode.childNode = {
    id: getRandomId(),
    parentId: parentNode.childNode.id,
    type: 3,
    childNode: null
  }
  parentNode.childNode.conditionNodes = [
    {
      id: getRandomId(),
      nodeName: '分支1',
      parentId: parentNode.childNode.id,
      type: 5,
      props: {},
      childNode: null
    },
    {
      id: getRandomId(),
      nodeName: '分支2',
      parentId: parentNode.childNode.id,
      type: 5,
      props: {},
      childNode: null
    }
  ]
  flowStore.setSelectedNode(parentNode)
  selectNode(parentNode, false)
  // $forceUpdate()
}

function getBranchEndNode(conditionNode) {
  if (!conditionNode?.childNode || !conditionNode?.childNode.id) {
    return conditionNode || {}
  }
  return getBranchEndNode(conditionNode.childNode)
}

function addBranchNode(node) {
  if (node.conditionNodes.length < 8) {
    node.conditionNodes.push({
      id: getRandomId(),
      parentId: node.id,
      nodeName: isConditionNode(node) ? '条件' : '分支' + (node.conditionNodes.length + 1),
      props: isConditionNode(node)
        ? {
            id: getRandomId(),
            parentId: node.id,
            nodeName: `条件${node.conditionNodes.length + 1}`,
            ...JSON.parse(JSON.stringify(DefaultProps.CONDITION_PROPS))
          }
        : {},
      type: isConditionNode(node) ? 4 : 5,
      childNode: null
    })
    // $forceUpdate()
  } else {
    $message.warning('最多只能添加 8 项')
  }
}

function delNode(node) {
  const parentNode = nodeMap.value.get(node.parentId)
  if (parentNode) {
    if (node.type === 4 || node.type === 5) {
      const { conditionNodes } = 'conditionNodes' in parentNode.props ? parentNode.props : parentNode
      conditionNodes.splice(conditionNodes.indexOf(node), 1)
      if (conditionNodes.length < 2) {
        const ppNode = nodeMap.value.get(parentNode.parentId)
        if (parentNode.childNode && parentNode.childNode.id) {
          ppNode.childNode = parentNode.childNode.type === 3 ? {} : parentNode.childNode || null
          ppNode.childNode.parentId = ppNode.id
          const endNode = getBranchEndNode(conditionNodes[0])
          endNode.childNode = parentNode?.childNode?.childNode || null
          if (endNode?.childNode && endNode?.childNode.id) {
            endNode.childNode.parentId = endNode.id || null
          }
        } else {
          ppNode.childNode = parentNode?.childNode?.childNode || null
          if (ppNode.childNode && ppNode.childNode.id) {
            ppNode.childNode.parentId = ppNode.id || null
          }
        }
      }
    } else {
      if (node.childNode && node.childNode.id) {
        node.childNode.parentId = parentNode.id
      }
      parentNode.childNode = node.childNode
    }
    // $forceUpdate()
  } else {
    $message.warning('出现错误，找不到上级节点')
  }
}

function validateProcess() {
  valid.value = true
  const err = []
  validate(err, dom.value)
  return err
}

function validateNode(err, node) {
  if ($refs[node.id]?.validate) {
    valid.value = $refs[node.id].validate(err)
  }
}

// const nodeDomUpdate = (node) => {
//   $refs[node.id].$forceUpdate()
// }

const forEachNode = (parent, node, callback) => {
  console.log(node,'lkllllllll')
  if (isBranchNode(node)) {
    callback(parent, node)
    forEachNode(node, node.childNode, callback)
    node.conditionNodes.map(branchNode => {
      callback(node, branchNode)
      forEachNode(branchNode, branchNode.childNode, callback)
    })
  } else if (isPrimaryNode(node) || isEmptyNode(node) || isBranchSubNode(node)) {
    callback(parent, node)
    forEachNode(node, node.childNode, callback)
  }
}

function validate(err, node) {
  if (isPrimaryNode(node)) {
    validateNode(err, node)
    validate(err, node.childNode)
  } else if (isBranchNode(node)) {
    node.conditionNodes.map(branchNode => {
      validateNode(err, branchNode)
      validate(err, branchNode.childNode)
    })
    validate(err, node.childNode)
  } else if (isEmptyNode(node)) {
    validate(err, node.childNode)
  }
}

function $deepCopy(obj) {
  return JSON.parse(JSON.stringify(obj))
}
function render() {
		if (dom?.childNode?.type === 3) {
			dom.childNode = null
		}
		nodeMap.clear()
		const processTrees = getDomTree(dom)
		// 插入末端节点
		processTrees.push( 
			h('div', { style: { 'text-align': 'center' } }, 
			[
				h('div', { class: { 'process-end': true }, innerHTML: '流程结束'})
			]
		))
		console.log('processTreesprocessTreesprocessTreesprocessTreesprocessTreesprocessTreesprocessTrees', processTrees)
		return h('div', { class: { _root: true }, ref: '_root' }, processTrees);
	}
</script>

<style scoped>
.process-end {
  font-weight: bold;
  color: red;
}
</style>