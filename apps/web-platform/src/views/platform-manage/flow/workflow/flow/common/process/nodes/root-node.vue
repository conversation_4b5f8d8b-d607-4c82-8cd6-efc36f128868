<template>
	<node
		title="发起人"
		:is-root="true"
		:content="content"
		placeholder="所有人"
		:header-bgc="headerBgc"
		header-icon="el-icon-user-solid"
		@selected="$emit('selected')"
		@insertNode="(type) => $emit('insertNode', type)"
	/>
</template>

<script setup>
const Node = defineAsyncComponent(() => import('./node-index.vue'));
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});
// 使用 Pinia
const flowStore = useDesign();
const diagramMode = computed(() => flowStore.diagramMode);

const content = computed(() => {
	if (props.config.props.assignedUser.length > 0) {
		const texts = [];
		props.config.props.assignedUser.forEach((org) => texts.push(org.orgName));
		return String(texts).replaceAll(',', '、');
	} else if (props.config.props.role.length > 0) {
		const texts = [];
		props.config.props.role.forEach((org) => texts.push(org.orgName));
		return String(texts).replaceAll(',', '、');
	} else if (props.config.props.post.length > 0) {
		const texts = [];
		props.config.props.post.forEach((org) => texts.push(org.orgName));
		return String(texts).replaceAll(',', '、');
	} else {
		return '所有人';
	}
});

const headerBgc = computed(() => {
	if (diagramMode === 'viewer') {
		return props.config.props.headerBgc;
	} else {
		return '#576a95';
	}
});

watch(
	() => props.config,
	(val) => {
		// const texts = []
		// val.props.assignedUser.forEach(org => texts.push(org.orgName))
		// val.placeholder = String(texts).replaceAll(',', '、')
		// if (val.props.assignedUser.length > 0) {
		//   val.nodeUserList = []
		//   val.props.assignedUser.map(item => {
		//     val.nodeUserList.push({
		//       id: item.orgId,
		//       name: item.orgName,
		//       type: 'user'
		//     })
		//   })
		// }
		// 使用 Vuex 的 commit 方法
		// this.$store.commit('flow/initiatorNode', val)
		// 如果有 Vuex 相关的逻辑，可以在这里调用
	},
	{ deep: true }
);
</script>

<style scoped></style>
