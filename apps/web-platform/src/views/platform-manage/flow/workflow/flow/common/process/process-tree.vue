<script>
// 导入所有节点组件
import Approval from './nodes/approval-node.vue'; // 发起人
import Cc from './nodes/cc-node.vue'; // 抄送人
import Concurrent from './nodes/concurrent-node.vue'; // 并行分支
import Condition from './nodes/condition-node.vue'; // 条件分支
import Empty from './nodes/empty-node.vue';
import Root from './nodes/root-node.vue';
import Node from './nodes/node-index.vue';
import Service from './nodes/service-node.vue'; // 服务
import Start from './nodes/start-node.vue'; // 开始
import End from './nodes/end-node.vue'; // 结束
import { ElButton } from 'element-plus'; // 假设你使用的是 Element Plus

import DefaultProps from './default-node-props';
import { useDesign } from '../../flow.js';
import { h } from 'vue';

// 使用 Pinia
const flowStore = useDesign();
// import { mapState } from 'vuex'

export default {
	name: 'ProcessTree',
	components: { Node, Root, Approval, Cc, Concurrent, Condition, Empty, Service, Start, End },

	data() {
		return {
			valid: true,
		};
	},
	computed: {
		// ...mapState('flow', ['nodeMap', 'design']),
		nodeMap() {
			return flowStore.nodeMap;
		},
		dom() {
			// console.log('processs  dome', this.design.process)
			return flowStore.design.process;
		},
	},
	mounted() {},
	methods: {
		getDomTree(node) {
			this.toMapping(node);
			if (this.isPrimaryNode(node)) {
				// 普通业务节点
				const childDoms = this.getDomTree(node.childNode);
				this.decodeAppendDom(node, childDoms);
				return [h('div', { class: { 'primary-node': true } }, childDoms)];
			} else if (this.isBranchNode(node)) {
				let index = 0;

				// 遍历分支节点，包含并行及条件节点
				const branchItems = node.conditionNodes.map((branchNode) => {
					// 处理每个分支内子节点
					this.toMapping(branchNode);
					const childDoms = this.getDomTree(branchNode.childNode);
					this.decodeAppendDom(branchNode, childDoms, { level: index + 1, size: node.conditionNodes.length });

					// 插入4条横线，遮挡掉条件节点左右半边线条
					this.insertCoverLine(index, childDoms, node.conditionNodes);

					// 遍历子分支尾部分支
					index++;

					return h('div', { class: 'branch-node-item' }, childDoms);
				});

				// 插入添加分支/条件的按钮
				branchItems.unshift(
					h('div', { class: { 'add-branch-btn': true } }, [
						h(
							ElButton,
							{
								class: 'add-branch-btn-el',
								size: 'small',
								round: true,
								onClick: () => this.addBranchNode(node),
								innerHTML: `添加${this.isConditionNode(node) === 4 ? '条件' : '分支'}`,
							},
							[]
						),
					])
				);

				const bchDom = [h('div', { class: { 'branch-node': true } }, branchItems)];

				// 继续遍历分支后的节点
				const afterChildDoms = this.getDomTree(node.childNode);
				return [h('div', {}, [bchDom, afterChildDoms])];
			} else if (this.isEmptyNode(node)) {
				// 空节点，存在于分支尾部
				const childDoms = this.getDomTree(node.childNode);
				this.decodeAppendDom(node, childDoms);
				return [h('div', { class: { 'empty-node': true } }, childDoms)];
			} else {
				// 遍历到了末端，无子节点
				return [];
			}
		},
		// 解码渲染的时候插入dom到同级
		decodeAppendDom(node, dom, props = {}) {
			props.config = node;
			let domType = null;
			switch (node.type) {
				case 0:
					domType = Root;
					break;
				case 1:
					domType = Approval;
					break;
				case 2:
					domType = Cc;
					break;
				case 4:
					domType = Condition;
					break;
				case 5:
					domType = Concurrent;
					break;
				case 6:
					domType = Service;
					break;
				case 8:
					domType = Start;
					break;
				case 9:
					domType = End;
					break;
				case 3: // 条件分支和并行分支的空数据结构 - 为了显示添加按钮
					domType = Empty;
					break;
			}
			dom.unshift(
				h(
					domType,
					{
						config: props.config,
						ref: node.id,
						key: node.id,
						// 定义事件，插入节点，删除节点，选中节点，复制/移动
						onInsertNode: this.debounce((type) => this.insertNode(type, node), 1), // 使用防抖函数并传递参数
						onDelNode: () => this.delNode(node),
						onSelected: this.debounce(() => this.selectNode(node)),
						// copy: () => this.copyBranch(node),
						onLeftMove: this.debounce((node) => this.branchMove(node, -1)),
						onRightMove: this.debounce((node) => this.branchMove(node, 1)),
					},
					[]
				)
			);

			// 保存整个树形结构树

			flowStore.setDomNode(node);
			// this.$store.commit('flow/domNode', node)
		},
		debounce(func, wait) {
			let timeout;
			return function (...args) {
				clearTimeout(timeout);
				timeout = setTimeout(() => func.apply(this, args), wait);
			};
		},
		// id映射到map，用来向上遍历
		toMapping(node) {
			if (node && node.id) {
				// console.log("node=> " + node.id + " name:" + node.name + " type:" + node.type)
				this.nodeMap.set(node.id, node);
			}
		},
		insertCoverLine(index, doms, conditionNodes) {
			if (index === 0) {
				// 最左侧分支
				doms.unshift(h('div', { class: { 'line-top-left': true } }, []));
				doms.unshift(h('div', { class: { 'line-bot-left': true } }, []));
			} else if (index === conditionNodes.length - 1) {
				// 最右侧分支
				doms.unshift(h('div', { class: { 'line-top-right': true } }, []));
				doms.unshift(h('div', { class: { 'line-bot-right': true } }, []));
			}
		},
		// 复制分支节点
		copyBranch(node) {
			const parentNode = this.nodeMap.get(node.parentId);
			const branchNode = this.$deepCopy(node);
			branchNode.name = branchNode.name + '-copy';
			this.forEachNode(parentNode, branchNode, (parent, node) => {
				const id = this.getRandomId();
				node.id = id;
				node.parentId = parent.id;
			});
			parentNode.conditionNodes.splice(parentNode.conditionNodes.indexOf(node), 0, branchNode);
			this.$forceUpdate();
		},
		// 移动分支节点
		branchMove(node, offset) {
			const parentNode = this.nodeMap.get(node.parentId);
			const index = parentNode.conditionNodes.indexOf(node);
			const branch = parentNode.conditionNodes[index + offset];
			parentNode.conditionNodes[index + offset] = parentNode.conditionNodes[index];
			parentNode.conditionNodes[index] = branch;
			this.$forceUpdate();
		},
		// 判断是否为主要业务节点
		isPrimaryNode(node) {
			return node && (node.type === 0 || node.type === 1 || node.type === 2 || node.type === 8 || node.type === 9 || node.type === 6);
		},
		// 是否为分支节点
		isBranchNode(node) {
			return node && (node.type === 4 || node.type === 5);
		},
		isEmptyNode(node) {
			return node && node.type === 3;
		},
		// 是分支节点
		isConditionNode(node) {
			return node.type === 4;
		},
		// 是分为支节点子节点
		isBranchSubNode(node) {
			return node && (node.type === 4 || node.type === 5);
		},
		isConcurrentNode(node) {
			return node.type === 5;
		},
		getRandomId() {
			return `node_${new Date().getTime().toString().substring(5)}${Math.round(Math.random() * 9000 + 1000)}`;
		},
		// 选中一个节点
		selectNode(node, drawShow = true) {
			// if (node.type !== 0) {
			// 	const rootNode = {
			// 		id: 'root',
			// 		parentId: null,
			// 		type: 'ROOT',
			// 		name: '发起人',
			// 		desc: '任何人',
			// 		childNode: node || null
			// 	}
			// 	console.log('rootnode', rootNode)
			// }
			// this.$store.commit('flow/selectedNode', node)
			flowStore.setSelectedNode(node);
			this.$emit('selectedNode', node, drawShow);
		},
		// 处理节点插入逻辑
		insertNode(type, parentNode) {
			this.$refs['_root'].click();
			// 缓存一下后面的节点
			const afterNode = parentNode.childNode || null;
			// 插入新节点
			parentNode.childNode = {
				id: this.getRandomId(),
				parentId: parentNode.id,
				props: {},
				type: type,
				childNode: null,
				editable: true,
			};
			switch (type) {
				case 1:
					this.insertApprovalNode(parentNode, afterNode);
					break;
				case 2:
					this.insertCcNode(parentNode, afterNode);
					break;
				case 4:
					this.insertConditionsNode(parentNode);
					break;
				case 5:
					this.insertConcurrentsNode(parentNode);
					break;
				case 6:
					this.insertServiceNode(parentNode);
					break;
				case 8:
					this.insertStartNode(parentNode);
					break;
				case 9:
					this.insertEndNode(parentNode);
					break;
				default:
					break;
			}
			// 拼接后续节点
			if (this.isBranchNode({ type })) {
				if (afterNode && afterNode.id) {
					afterNode.parentId = parentNode.childNode.childNode.id;
				}
				parentNode.childNode.childNode.childNode = afterNode;
				// this.$set(parentNode.childNode.childNode, 'childNode', afterNode)
			} else {
				if (afterNode && afterNode.id) {
					afterNode.parentId = parentNode.childNode.id;
				}
				parentNode.childNode.childNode = afterNode;

				// this.$set(parentNode.childNode, 'childNode', afterNode)
			}
			// this.$forceUpdate()
		},
		// 审批人处理数据
		insertApprovalNode(parentNode) {
			parentNode.childNode.nodeName = '审批人';
			parentNode.childNode.props = this.$deepCopy(DefaultProps.APPROVAL_PROPS);
			// this.$set(parentNode.childNode, 'nodeName', '审批人')
			// this.$set(parentNode.childNode, 'props', this.$deepCopy(DefaultProps.APPROVAL_PROPS))
			const node = {
				childNode: {},
				...parentNode.childNode,
			};
			// this.$store.commit('flow/selectedNode', node)
			flowStore.setSelectedNode(node);

			this.selectNode(parentNode, false);
		},
		// 抄送人处理数据
		insertCcNode(parentNode) {
			parentNode.childNode.nodeName = '抄送人';
			parentNode.childNode.props = this.$deepCopy(DefaultProps.CC_PROPS);
			// this.$store.commit('flow/selectedNode', parentNode)
			flowStore.setSelectedNode(parentNode);

			this.selectNode(parentNode, false);
		},
		// 自定义节点 - 开始
		insertStartNode(parentNode) {
			parentNode.childNode.nodeName = '自定义节点开始';
			const props = {
				id: parentNode.childNode.id,
				nodeName: parentNode.childNode.nodeName,
				parentId: parentNode.childNode.parentId,
				type: parentNode.childNode.type,
			};
			parentNode.childNode.props = props;
			parentNode.childNode.propsData = JSON.stringify(props);
			const node = {
				childNode: {},
				...parentNode.childNode,
			};
			// this.$store.commit('flow/selectedNode', node)
			flowStore.setSelectedNode(node);
			this.selectNode(parentNode, false);
		},
		// 自定义节点 - 结束
		insertEndNode(parentNode) {
			parentNode.childNode.nodeName = '自定义节点结束';
			const props = {
				id: parentNode.childNode.id,
				nodeName: parentNode.childNode.nodeName,
				parentId: parentNode.childNode.parentId,
				type: parentNode.childNode.type,
			};
			parentNode.childNode.props = props;
			parentNode.childNode.propsData = JSON.stringify(props);
			parentNode.childNode.childNode = null;
			const node = {
				childNode: null,
				...parentNode.childNode,
			};
			// this.$store.commit('flow/selectedNode', node)
			flowStore.setSelectedNode(node);
			this.selectNode(node, false);
		},
		// 设置服务节点
		insertServiceNode(parentNode) {
			parentNode.childNode.nodeName = '服务';
			parentNode.childNode.props = this.$deepCopy(DefaultProps.SERVICE_PROPS);
			parentNode.childNode.childNode = null;
			// this.$store.commit('flow/selectedNode', parentNode)
			flowStore.setSelectedNode(parentNode);
			this.selectNode(parentNode, false);
		},
		// 设置条件分支节点
		insertConditionsNode(parentNode) {
			parentNode.childNode.nodeName = '条件分支';
			parentNode.childNode.childNode = {
				id: this.getRandomId(),
				parentId: parentNode.childNode.id,
				type: 3,
				childNode: null,
			};
			parentNode.childNode.conditionNodes = [
				{
					id: this.getRandomId(),
					parentId: parentNode.childNode.id,
					type: 4,
					props: {
						nodeName: '条件1',
						id: this.getRandomId(),
						parentId: parentNode.childNode.id,
						...this.$deepCopy(DefaultProps.CONDITION_PROPS),
					},
					nodeName: '条件1',
					childNode: null,
				},
				{
					id: this.getRandomId(),
					parentId: parentNode.childNode.id,
					type: 4,
					props: {
						nodeName: '条件2',
						id: this.getRandomId(),
						parentId: parentNode.childNode.id,
						...this.$deepCopy(DefaultProps.CONDITION_PROPS),
					},
					nodeName: '条件2',
					childNode: null,
				},
			];
			// this.$store.commit('flow/selectedNode', parentNode)
			flowStore.setSelectedNode(parentNode);

			this.selectNode(parentNode, false);
			this.$forceUpdate();
		},
		// 设置并行分支节点
		insertConcurrentsNode(parentNode) {
			parentNode.childNode.nodeName = '并行分支';
			parentNode.childNode.childNode = {
				id: this.getRandomId(),
				parentId: parentNode.childNode.id,
				type: 3,
				childNode: null,
			};
			parentNode.childNode.props = {};
			parentNode.childNode.conditionNodes = [
				{
					id: this.getRandomId(),
					nodeName: '分支1',
					parentId: parentNode.childNode.id,
					type: 5,
					props: {},
					childNode: null,
				},
				{
					id: this.getRandomId(),
					nodeName: '分支2',
					parentId: parentNode.childNode.id,
					type: 5,
					props: {},
					childNode: null,
				},
			];

			// this.$store.commit('flow/selectedNode', parentNode)
			flowStore.setSelectedNode(parentNode);

			this.selectNode(parentNode, false);
			this.$forceUpdate();
		},
		getBranchEndNode(conditionNode) {
			if (!conditionNode?.childNode || !conditionNode?.childNode.id) {
				return conditionNode || {};
			}
			return this.getBranchEndNode(conditionNode.childNode);
		},
		addBranchNode(node) {
			if (node.conditionNodes.length < 8) {
				node.conditionNodes.push({
					id: this.getRandomId(),
					parentId: node.id,
					nodeName: this.isConditionNode(node) ? '条件' : '分支' + (node.conditionNodes.length + 1),
					props: this.isConditionNode(node)
						? {
								id: this.getRandomId(),
								parentId: node.id,
								nodeName: `条件${node.conditionNodes.length + 1}`,
								...this.$deepCopy(DefaultProps.CONDITION_PROPS),
						  }
						: {},
					type: this.isConditionNode(node) ? 4 : 5,
					childNode: null,
				});
				this.$forceUpdate();
			} else {
				this.$message.warning('最多只能添加 8 项');
			}
		},
		// 删除当前节点
		delNode(node) {
			// 获取该节点的父节点
			const parentNode = this.nodeMap.get(node.parentId);
			if (parentNode) {
				// 判断该节点是不是分支节点
				if (node.type === 4 || node.type === 5) {
					const { conditionNodes } = 'conditionNodes' in parentNode.props ? parentNode.props : parentNode;
					// 移除该分支
					conditionNodes.splice(conditionNodes.indexOf(node), 1);
					// 处理只剩1个分支的情况
					if (conditionNodes.length < 2) {
						// 获取条件组的父节点
						const ppNode = this.nodeMap.get(parentNode.parentId);
						// 判断唯一分支是否存在业务节点
						if (parentNode.childNode && parentNode.childNode.id) {
							// 将剩下的唯一分支头部合并到主干
							ppNode.childNode = parentNode.childNode.type == 3 ? {} : parentNode.childNode || null;
							ppNode.childNode.parentId = ppNode.id;
							// 搜索唯一分支末端最后一个节点
							const endNode = this.getBranchEndNode(conditionNodes[0]);
							// 后续节点进行拼接, 这里要取parentNode.childNode 的节点
							endNode.childNode = parentNode?.childNode?.childNode || null;
							if (endNode?.childNode && endNode?.childNode.id) {
								endNode.childNode.parentId = endNode.id || null;
							}
						} else {
							// 后续节点进行拼接, 这里要取parentNode.childNode 的节点
							ppNode.childNode = parentNode?.childNode?.childNode || null;
							if (ppNode.childNode && ppNode.childNode.id) {
								ppNode.childNode.parentId = ppNode.id || null;
							}
						}
					}
					// if (parentNode.childNode.type == 3) {
					// 	parentNode.childNode = null
					// }
				} else {
					// 不是的话就直接删除
					if (node.childNode && node.childNode.id) {
						node.childNode.parentId = parentNode.id;
					}
					parentNode.childNode = node.childNode;
				}
				this.$forceUpdate();
			} else {
				this.$message.warning('出现错误，找不到上级节点');
			}
		},
		validateProcess() {
			this.valid = true;
			const err = [];
			this.validate(err, this.dom);
			return err;
		},
		validateNode(err, node) {
			if (this.$refs[node.id].validate) {
				this.valid = this.$refs[node.id].validate(err);
			}
		},
		// 更新指定节点的dom
		nodeDomUpdate(node) {
			this.$refs[node.id].$forceUpdate();
		},
		// 给定一个起始节点，遍历内部所有节点
		forEachNode(parent, node, callback) {
			if (this.isBranchNode(node)) {
				callback(parent, node);
				this.forEachNode(node, node.childNode, callback);
				node.conditionNodes.map((branchNode) => {
					callback(node, branchNode);
					this.forEachNode(branchNode, branchNode.childNode, callback);
				});
			} else if (this.isPrimaryNode(node) || this.isEmptyNode(node) || this.isBranchSubNode(node)) {
				callback(parent, node);
				this.forEachNode(node, node.childNode, callback);
			}
		},
		// 校验所有节点设置
		validate(err, node) {
			if (this.isPrimaryNode(node)) {
				this.validateNode(err, node);
				this.validate(err, node.childNode);
			} else if (this.isBranchNode(node)) {
				// 校验每个分支
				node.conditionNodes.map((branchNode) => {
					// 校验条件节点
					this.validateNode(err, branchNode);
					// 校验条件节点后面的节点
					this.validate(err, branchNode.childNode);
				});
				this.validate(err, node.childNode);
			} else if (this.isEmptyNode(node)) {
				this.validate(err, node.childNode);
			}
		},
		$deepCopy(obj) {
			return JSON.parse(JSON.stringify(obj));
		},
	},
	render() {
		if (this.dom?.childNode?.type === 3) {
			this.dom.childNode = null;
		}
		this.nodeMap.clear();
		const processTrees = this.getDomTree(this.dom);
		// 插入末端节点
		processTrees.push(h('div', { style: { 'text-align': 'center' } }, [h('div', { class: { 'process-end': true }, innerHTML: '流程结束' })]));
		return h('div', { class: { _root: true }, ref: '_root' }, processTrees);
	},
};
</script>

<style lang="scss" scoped>
._root {
	margin: 0 auto;
}

.process-end {
	width: 60px;
	margin: 0 auto;
	margin-bottom: 20px;
	border-radius: 15px;
	padding: 5px 10px;
	font-size: small;
	color: #747474;
	background-color: #f2f2f2;
	box-shadow: 0 0 10px 0 #bcbcbc;
}

.primary-node {
	display: flex;
	align-items: center;
	flex-direction: column;
}

.branch-node {
	display: flex;
	justify-content: center;
	/*border-top: 2px solid #cccccc;
  border-bottom: 2px solid #cccccc;*/
}

.branch-node-item {
	position: relative;
	display: flex;
	background: #f5f6f6;
	flex-direction: column;
	align-items: center;
	border-top: 2px solid #cccccc;
	border-bottom: 2px solid #cccccc;

	&:before {
		content: '';
		position: absolute;
		top: 0;
		left: calc(50% - 1px);
		margin: auto;
		width: 2px;
		height: 100%;
		background-color: #cacaca;
	}

	.line-top-left,
	.line-top-right,
	.line-bot-left,
	.line-bot-right {
		position: absolute;
		width: 50%;
		height: 4px;
		background-color: #f5f6f6;
	}

	.line-top-left {
		top: -2px;
		left: -1px;
	}

	.line-top-right {
		top: -2px;
		right: -1px;
	}

	.line-bot-left {
		bottom: -2px;
		left: -1px;
	}

	.line-bot-right {
		bottom: -2px;
		right: -1px;
	}
}

.add-branch-btn {
	position: absolute;
	width: 80px;

	.add-branch-btn-el {
		z-index: 999;
		position: absolute;
		top: -15px;
	}
}

.empty-node {
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
}
</style>
