<template>
	<div class="layout-padding" v-loading="vloading">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<co-search ref="searchRef" inline :model="searchData" label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table border ref="dsTableRef" :config="tableConfig" :header="tableHeader" single-mode="icon-hook" @loaded="onTableLoad" @operation="onOperation" align="left"> </co-table>
			<synchronizationProcess ref="synchronousRef" @refresh="onRefresh" />
		</div>
	</div>
</template>
<script setup>
import { ref, reactive, defineAsyncComponent } from 'vue';
import { processPage, processDelete } from '/@/api/flow/flow/index.ts';
import { useMessage, useMessageBox } from '/@/hooks/message';
import synchronizationProcess from '../built-in-processes/component/synchronization-process.vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
// 使用 Pinia
const flowStore = useDesign();

import { tableHeader } from './data.js';
import { useRouter } from 'vue-router';
const router = useRouter();
const synchronousRef = ref();
const searchConfig = reactive({
	items: [
		{
			prop: 'name',
			type: 'input',
			attrs: {
				placeholder: '请输入名称',
				label: '名称',
				clearable: true,
			},
		},
	],
});
const searchData = reactive({ name: '' });
const dicData = ref();
const vloading = ref(false);
// 表格配置
const tableConfig = ref({
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: processPage, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {}, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
	},
});

let onSearch = null;
// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}
const onSearchHandle = (data) => {
	onSearch({ params: data });
};

function onRefresh() {
	onSearch();
}

// 监听表格搜索操作
function onOperation({ row, field }) {
	switch (field) {
		case 'add':
			resetFlowData();
			router.push({ path: '/platform-manage/flow/workflow/flow/create-flow' });
			break;
		case 'edit':
			router.push({
				path: '/platform-manage/flow/workflow/flow/create-flow',
				query: {
					type: 'edit',
					flowId: row.flowId,
				},
			});
			break;
		case 'synchronous':
			synchronousRef.value.sync(row);
			break;
		case 'delete':
			useMessageBox()
				.confirm('是否确认删除此数据?')
				.then((res) => {
					vloading.value = true;
					processDelete({ flowId: row.flowId })
						.then((res) => {
							onRefresh();
							useMessage().success(res.msg || '删除成功');
							vloading.value = false;
						})
						.catch((err) => {
							vloading.value = false;
							useMessage().error(err.msg);
						});
				});
			break;
	}
}
// 删除流程
const del = (row) => {
	const confirmAction = async () => {
		try {
			await processDelete({ flowId: row.flowId });
		} catch (error) {
			console.error(error);
		}
	};
	this.$confirm('此操作将永久删除, 是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(confirmAction)
		.catch(() => {});
};
const resetFlowData = () => {
	// 重置创建流程
	const form = {
		process: {
			id: 'root',
			parentId: null,
			type: 0,
			nodeName: '发起人',
			desc: '任何人',
			props: {
				assignedUser: [],
				role: [],
				post: [],
				expression: '',
			},
			childNode: {},
		},
	};
	flowStore.loadForm(form);
	flowStore.setVariableForm([]);
};
</script>
<style lang="scss" scoped>
.pagination-page {
	margin: 15px;
	width: 100%;
	display: flex;
	justify-content: flex-end;
}
</style>
