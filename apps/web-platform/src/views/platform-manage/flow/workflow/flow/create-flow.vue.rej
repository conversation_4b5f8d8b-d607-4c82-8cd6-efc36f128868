diff a/src/views/platform-manage/flow/workflow/flow/create-flow.vue b/src/views/platform-manage/flow/workflow/flow/create-flow.vue	(rejected hunks)
@@ -207,9 +207,10 @@
 const getDetails = () => {
 	processGet({ flowId: flowId.value }).then((res) => {
 		const { name, remark } = res.data;
+		const processParse = JSON.parse(res.data.process);
+
 		ruleForm.name = name;
 		ruleForm.remark = remark;
-		process.value = res.data.process;
 		const varIable = {
 			domains: [
 				{
