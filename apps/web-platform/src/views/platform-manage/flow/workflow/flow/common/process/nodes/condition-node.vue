<!-- 条件分支 -->
<template>
	<div :class="{ node: true, 'node-error-state': showError }">
		<div :class="{ 'node-body': true, error: showError }">
			<div v-if="level > 1 && diagramMode !== 'viewer'" class="node-body-left">
				<i class="el-icon-arrow-left" @click.stop="$emit('leftMove')" />
			</div>
			<div class="node-body-main" @click.prevent="$emit('selected')">
				<div class="node-body-main-header">
					<proEllipsis class="title" hover-tip :content="config.name ? config.name : '条件' + level" :is-title="true" />
					<span class="level">优先级{{ level }}</span>
					<span v-if="diagramMode !== 'viewer'" class="option">
						<!-- <el-tooltip effect="dark" content="复制条件" placement="top">
							<i class="el-icon-copy-document" @click.stop="$emit('copy')" />
						</el-tooltip> -->
						<el-icon class="el-icon-close" style="display: inline-block" @click="$emit('delNode')">
							<Close />
						</el-icon>
					</span>
				</div>
				<div class="node-body-main-content">
					<span v-if="(content || '').trim() === ''" class="placeholder">{{ level == size && size != 0 ? '请设置条件' : placeholder }}</span>
					<proEllipsis v-else hover-tip :row="4" :content="content" />
				</div>
			</div>
			<div v-if="level < size && diagramMode !== 'viewer'" class="node-body-right">
				<i class="el-icon-arrow-right" @click.stop="$emit('rightMove')" />
			</div>
			<div v-if="showError" class="node-error">
				<el-tooltip effect="dark" :content="errorInfo" placement="top-start">
					<i class="el-icon-warning-outline" />
				</el-tooltip>
			</div>
		</div>
		<div class="node-footer">
			<div class="node_btn">
				<insert-button v-if="diagramMode !== 'viewer'" @insertNode="(type) => $emit('insertNode', type)" />
			</div>
		</div>
	</div>
</template>

<script setup>
import InsertButton from '/@/views/platform-manage/flow/workflow/flow/components/insert-button.vue';
import proEllipsis from '/@/views/platform-manage/flow/workflow/flow/components/pro-ellipsis.vue';
import { ref, computed } from 'vue';
import { ValueType } from '/@/views/platform-manage/flow/workflow/flow/common/form/components-config-export';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';

const groupNames = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
	level: {
		type: Number,
		default: 1,
	},
	size: {
		type: Number,
		default: 0,
	},
});

const ValueTypeRef = ref(ValueType);
const groupNamesRef = ref(groupNames);
const placeholder = ref('请设置条件');
const errorInfo = ref('');
const showError = ref(false);
// 使用 Pinia
const flowStore = useDesign();
const diagramMode = computed(() => flowStore.diagramMode);
const content = computed(() => {
	// const groups = props.config.props.conditionNodes
	// const conditions = []
	// groups.forEach(group => {
	//   const subConditions = []
	//   group.conditionList.forEach(subCondition => {
	//     let subConditionStr = ''
	//     switch (subCondition.valueType) {
	//       case ValueType.dept:
	//       case ValueType.user:
	//         subConditionStr = `${subCondition.title}属于[${String(subCondition.value.map(u => u.name)).replaceAll(',', '. ')}]之一`
	//         break
	//       case ValueType.number:
	//       case ValueType.string:
	//         subConditionStr = getOrdinaryConditionContent(subCondition)
	//         break
	//     }
	//     subConditions.push(subConditionStr)
	//   })
	//   // 根据子条件关系构建描述
	//   const subConditionsStr = String(subConditions).replaceAll(',', subConditions.length > 1 ? (group.groupType === 'AND' ? ') 且 (' : ') 或 (') : group.groupType === 'AND' ? ' 且 ' : ' 或 ')
	//   conditions.push(subConditions.length > 1 ? `(${subConditionsStr})` : subConditionsStr)
	// })
	// // 构建最终描述
	// return String(conditions).replaceAll(',', props.config.props.groupsType === 'AND' ? ' 且 ' : ' 或 ')
	return '';
});

const getDefault = (val, df) => {
	return val && val !== '' ? val : df;
};

const getOrdinaryConditionContent = (subCondition) => {
	switch (subCondition.compare) {
		case 'in':
			return `${subCondition.title}为[${String(subCondition.value).replaceAll(',', '、')}]中之一`;
		case 'notin':
			return `${subCondition.value[0]} < ${subCondition.title} < ${subCondition.value[1]}`;
		case 'AB':
			return `${subCondition.value[0]} ≤ ${subCondition.title} < ${subCondition.value[1]}`;
		case 'BA':
			return `${subCondition.value[0]} < ${subCondition.title} ≤ ${subCondition.value[1]}`;
		case 'ABA':
			return `${subCondition.value[0]} ≤ ${subCondition.title} ≤ ${subCondition.value[1]}`;
		case '<=':
			return `${subCondition.title} ≤ ${getDefault(subCondition.value[0], ' ?')}`;
		case '>=':
			return `${subCondition.title} ≥ ${getDefault(subCondition.value[0], ' ?')}`;
		default:
			return `${subCondition.title}${subCondition.compare}${getDefault(subCondition.value[0], ' ?')}`;
	}
};

const validate = (err) => {
	console.log('condition childNode', props.config.childNode);
	if (!(props.level === props.size && props.size !== 0) && !props.config.childNode?.id) {
		showError.value = true;
		errorInfo.value = '条件分支后不能为空';
		err.push(`条件分支后不能为空`);
		return !showError.value;
	}

	const propsConfig = props.config.props;
	if (propsConfig.groups.length <= 0) {
		showError.value = true;
		errorInfo.value = '请设置分支条件';
		err.push(`${props.config.name} 未设置条件`);
	} else {
		if (!(props.level === props.size && props.size !== 0)) {
			for (let i = 0; i < propsConfig.groups.length; i++) {
				if (propsConfig.groups[i].cids.length === 0) {
					showError.value = true;
					errorInfo.value = `请设置条件组${groupNamesRef.value[i]}内的条件`;
					err.push(`条件 ${props.config.name} 条件组${groupNamesRef.value[i]}内未设置条件`);
					break;
				} else {
					const conditions = propsConfig.groups[i].conditionList;
					for (let ci = 0; ci < conditions.length; ci++) {
						const subc = conditions[ci];
						if (subc.value.length === 0) {
							showError.value = true;
						} else {
							showError.value = false;
						}
						if (showError.value) {
							errorInfo.value = `请完善条件组${groupNamesRef.value[i]}内的${subc.title}条件`;
							err.push(`条件 ${props.config.name} 条件组${groupNamesRef.value[i]}内${subc.title}条件未完善`);
							return false;
						}
					}
				}
			}
		}
	}
	return !showError.value;
};
</script>

<style lang="scss" scoped>
.node-error-state {
	.node-body {
		box-shadow: 0px 0px 5px 0px #f56c6c !important;
	}
}

.node {
	padding: 30px 10px 0;
	width: 220px;

	.node-body {
		cursor: pointer;
		min-height: 80px;
		max-height: 120px;
		position: relative;
		border-radius: 5px;
		background-color: white;
		box-shadow: 0px 0px 5px 0px #d8d8d8;

		&:hover {
			.node-body-left,
			.node-body-right {
				i {
					display: block !important;
				}
			}

			.node-body-main {
				.level {
					display: none !important;
				}

				.option {
					display: inline-block !important;
				}
			}

			box-shadow: 0px 0px 3px 0px #1890ff;
		}

		.node-body-left,
		.node-body-right {
			display: flex;
			align-items: center;
			position: absolute;
			height: 100%;

			i {
				display: none;
			}

			&:hover {
				background-color: #ececec;
			}
		}

		.node-body-left {
			left: 0;
		}

		.node-body-right {
			right: 0;
			top: 0;
		}

		.node-body-main {
			//position: absolute;
			width: 188px;
			margin-left: 17px;
			display: inline-block;

			.node-body-main-header {
				padding: 10px 0px 5px;
				font-size: xx-small;
				position: relative;

				.title {
					color: #15bca3;
					display: inline-block;
					height: 14px;
					width: 125px;
				}

				.level {
					position: absolute;
					width: 34%;
					right: 25px;
					color: #888888;
				}

				.option {
					position: absolute;
					right: 0;
					display: none;
					font-size: medium;

					i {
						color: #888888;
						padding: 0 3px;
					}
				}
			}

			.node-body-main-content {
				padding: 6px;
				color: #656363;
				font-size: 14px;

				i {
					position: absolute;
					top: 55%;
					right: 10px;
					font-size: medium;
				}

				.placeholder {
					color: #8c8c8c;
				}
			}
		}

		.node-error {
			position: absolute;
			right: -40px;
			top: 20px;
			font-size: 25px;
			color: #f56c6c;
		}
	}

	.node-footer {
		position: relative;

		.node_btn {
			width: 100%;
			display: flex;
			height: 70px;
			padding: 20px 0 32px;
			justify-content: center;
		}

		::v-deep .el-button {
			height: 32px;
		}

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: -1;
			margin: auto;
			width: 2px;
			height: 100%;
			background-color: #cacaca;
		}
	}
}
</style>
