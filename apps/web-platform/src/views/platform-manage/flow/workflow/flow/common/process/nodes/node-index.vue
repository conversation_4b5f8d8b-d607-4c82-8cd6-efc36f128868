<template>
	<div :class="{ node: true, root: isRoot || !show, 'node-error-state': showError }">
		<div v-if="show" :class="{ 'node-body': true, error: showError }">
			<div>
				<div class="node-body-header" :style="{ 'background-color': headerBgc }">
					<i v-if="(headerIcon || '') !== ''" :class="headerIcon" style="margin-right: 5px" />
					<span v-if="flag" @click="edit">{{ title }}</span>
					<input v-else ref="refInput" v-model="titleInput" type="text" style="width: 120px; color: #333" :maxlength="20" @blur="titleChange" />
					<el-icon class="el-icon-close" v-if="!isRoot && diagramMode !== 'viewer'" @click="$emit('delNode')" style="float: right">
						<Close />
					</el-icon>
				</div>
				<div class="node-body-content" @click="$emit('selected')">
					<i v-if="leftIcon" :class="leftIcon" />
					<span v-if="(content || '').trim() === ''" class="placeholder">{{ placeholder }}</span>
					{{ content }}
					<i v-if="diagramMode !== 'viewer'" class="el-icon-arrow-right" />
				</div>
				<div v-if="showError" class="node-error">
					<el-tooltip effect="dark" :content="errorInfo" placement="top-start">
						<i class="el-icon-warning-outline" />
					</el-tooltip>
				</div>
			</div>
		</div>
		<div class="node-footer">
			<div class="node_btn">
				<insert-button v-show="diagramMode !== 'viewer'" @insertNode="(type) => $emit('insertNode', type)" />
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';

const InsertButton = defineAsyncComponent(() => import('/@/views/platform-manage/flow/workflow/flow/components/insert-button.vue'));
// 使用 emit 发射事件
const emit = defineEmits(['titleChange']);
const props = defineProps({
	// 是否为根节点
	isRoot: {
		type: Boolean,
		default: false,
	},
	// 是否显示节点体
	show: {
		type: Boolean,
		default: true,
	},
	// 节点内容区域文字
	content: {
		type: String,
		default: '',
	},
	title: {
		type: String,
		default: '标题',
	},
	placeholder: {
		type: String,
		default: '请设置',
	},
	// 节点体左侧图标
	leftIcon: {
		type: String,
		default: undefined,
	},
	// 头部图标
	headerIcon: {
		type: String,
		default: '',
	},
	// 头部背景色
	headerBgc: {
		type: String,
		default: '#576a95',
	},
	// 是否显示错误状态
	showError: {
		type: Boolean,
		default: false,
	},
	errorInfo: {
		type: String,
		default: '无信息',
	},
	config: {
		type: Object,
		default: () => ({}),
	},
});
const flag = ref(true);
const titleInput = ref('');
const refInput = ref();
// 使用 Pinia
const flowStore = useDesign();
const diagramMode = computed(() => flowStore.diagramMode);
const edit = () => {
	if (Object.keys(props.config).length > 0 && props.config.type === 1) {
		flag.value = false;
	}
	titleInput.value = props.title;
	nextTick(() => {
		// refInput.value.focus()
	});
};

const titleChange = () => {
	flag.value = true;

	emit('titleChange', titleInput.value);
};
</script>

<style lang="scss" scoped>
.root {
	&:before {
		display: none !important;
	}
}

.node-error-state {
	.node-body {
		box-shadow: 0px 0px 5px 0px #f56c6c !important;
	}
}

.node {
	width: 210px;
	position: relative;

	&:before {
		content: '';
		position: absolute;
		top: -12px;
		left: 50%;
		-webkit-transform: translateX(-50%);
		transform: translateX(-50%);
		width: 0;
		border-style: solid;
		border-width: 8px 6px 4px;
		border-color: #cacaca transparent transparent;
		background: #f5f5f7;
	}

	.node-body {
		cursor: pointer;
		max-height: 120px;
		position: relative;
		border-radius: 5px;
		background-color: white;
		box-shadow: 0px 0px 5px 0px #d8d8d8;

		&:hover {
			box-shadow: 0px 0px 3px 0px #1890ff;

			.node-body-header {
				.el-icon-close {
					display: inline;
					font-size: medium;
				}
			}
		}

		.node-body-header {
			border-top-left-radius: 5px;
			border-top-right-radius: 5px;
			padding: 5px 15px;
			color: white;
			font-size: xx-small;

			.el-icon-close {
				display: none;
			}

			.name {
				height: 14px;
				width: 150px;
				display: inline-block;
			}
		}

		.node-body-content {
			padding: 18px;
			color: #656363;
			font-size: 14px;

			i {
				position: absolute;
				top: 55%;
				right: 5px;
				font-size: medium;
			}

			.placeholder {
				color: #8c8c8c;
			}
		}

		.node-error {
			position: absolute;
			right: -40px;
			top: 20px;
			font-size: 25px;
			color: #f56c6c;
		}
	}

	.node-footer {
		position: relative;

		.node_btn {
			width: 100%;
			display: flex;
			padding: 20px 0 32px;
			justify-content: center;
		}

		::v-deep .el-button {
			height: 32px;
		}

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: -1;
			margin: auto;
			width: 2px;
			height: 100%;
			background-color: #cacaca;
		}
	}
}
</style>
