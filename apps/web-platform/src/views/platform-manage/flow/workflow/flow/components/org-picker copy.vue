

<script setup>
import { computed, toRefs,watch } from 'vue';
const props = defineProps({
      title: {
        type: String,
        default: '请选择'
      },
      // type: {
      //   type: String,
      //   default: 'user'
      // },
      multiple: {
        type: Boolean,
        default: false
      },
      selected: {
        type: Array,
        default: () => []
      }
    });
    const visible = ref(false);
    const type = ref('user');
    const loading = ref(false);
    const checkAll = ref(false);
    const nowDeptId = ref(null);
    const isIndeterminate = ref(false);
    const searchUsers = ref([]);
    const nodes = ref([]);
    const select = ref([]);
    const search = ref('');
    const deptStack = ref([]);
    const thirdList = ref([]);
    const roleList = ref([]);
    const postList = ref([]);
    const nextChildren = ref([]); // 成员列表
		const deptStackStr = computed(() => {
      return String(deptStack.map(v => v.orgName)).replaceAll(',', ' > ');
    });

    const orgs = computed(() => {
      return !search || search.trim() === '' ? nodes : searchUsers;
    });

    const showUsers = computed(() => {
      return search || search.trim() !== '';
    });
		watch(selected, (val) => {
      select.value = [...val];
      // Vue 3 中不需要手动调用 $forceUpdate()
    }, { deep: true });

</script>

