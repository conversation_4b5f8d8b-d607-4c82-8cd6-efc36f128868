<template>
	<div>
		<el-tabs v-if="name" v-model="active" type="border-card">
			<el-tab-pane :label="name" name="properties">
				<component :is="domType" :config="selectNode.props" />
			</el-tab-pane>
			<el-tab-pane v-if="name === '设置审批人'" label="设置审批按钮行为" name="permissions">
				<ApproveBtnConfig :config="selectNode.props" />
			</el-tab-pane>
		</el-tabs>
		<component :is="domType" v-else :config="selectNode.props" />
	</div>
</template>

<script setup>
import { markRaw, watch } from 'vue';

const Approval = markRaw(defineAsyncComponent(() => import('./approval-node-config.vue'))); // 审批人
import ApproveBtnConfig from './approve-btn-config.vue' // 审批按钮行为
import Condition from './condition-node-config.vue'; // 添加条件
import Cc from './cc-node-config.vue' // 抄送人
const Root = markRaw(defineAsyncComponent(() => import('./root-node-config.vue')));
import Service from './service-node-config.vue'; // 服务
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';

const props = defineProps({
	process: {
		type: String,
		default: '',
	},
});
// 使用 Pinia
const flowStore = useDesign();
const active = ref('properties');
const domType = ref(null);
const selectNode = computed(() => flowStore.$state.selectedNode);
const name = computed(() => {
	switch (selectNode.value.type) {
		case 0:
			return '设置发起人';
		case 1:
			return '设置审批人';
		case 'TASK':
			return '设置办理人';
		case 2:
			return '设置抄送人';
		default:
			return null;
	}
});

// 监听 selectNode 的变化
watch(
	selectNode,
	(newVal) => {
		const { type } = newVal;

		switch (type) {
			case 0:
				domType.value = Root;
				break;
			case 1:
				domType.value = Approval;
				break;
			case 2:
				domType.value = Cc;
				break;
			case 4:
				domType.value = Condition;
				break;
			case 6:
				domType.value = Service;
				break;
		}
	},
	{ immediate: true }
);
</script>
<style lang="scss" scoped></style>
