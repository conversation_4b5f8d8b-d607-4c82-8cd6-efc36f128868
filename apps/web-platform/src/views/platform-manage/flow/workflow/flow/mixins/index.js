export default {
	data() {
		return {
			total: 0,
			pageParams: {
				current: 1,
				size: 10
			},
			pageSize: [10, 20, 30, 50]
		}
	},
	methods: {
		// 搜索
		search() {
			this.pageParams.current = 1
			this.getList()
		},
		// 页码改变
		handleCurrentChange(val) {
			this.pageParams.current = val
			this.getList()
		},
		// 每页多少条
		handleSizeChange(val) {
			this.pageParams.size = val
			this.pageParams.current = 1
			this.getList()
		},
		// 重置搜索
		resetForm(formName) {
			this.$refs[formName].resetFields()
			this.resetList()
		},
		// 重置列表
		resetList() {
			this.pageParams.current = 1
			this.getList()
		}
	}
}
