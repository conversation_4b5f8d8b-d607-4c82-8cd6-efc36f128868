<template>
	<div>
		<div class="scale">
			<el-button icon="el-icon-plus" size="small" :disabled="scale >= 150" circle @click="scale += 10" />
			<span>{{ scale }}%</span>
			<el-button icon="el-icon-minus" size="small" :disabled="scale <= 40" circle @click="scale -= 10" />
		</div>
		<div class="design" :style="'transform: scale(' + scale / 100 + ');'">
			<process-tree ref="process-tree" @selectedNode="nodeSelected" />
		</div>
		<el-drawer :visible.sync="showConfig" :modal-append-to-body="false" :size="selectedNode.type === 4 ? '600px' : '500px'" direction="rtl" :modal="false" destroy-on-close>
			<template #header>
				<span style="font-size: medium">{{ selectedNode.name }}</span>
			</template>
			<div class="node-config-content">
				<!-- <node-config /> -->
			</div>
		</el-drawer>
	</div>
</template>

<script>
// import ProcessTree from '@/views/system/workflow/flow/common/process/process-tree.vue'
// import NodeConfig from './common/process/config/node-config.vue'
// import { mapState } from 'vuex'

export default {
	name: 'ProcessDesign',
	components: {
		ProcessTree,
		NodeConfig,
	},
	props: {
		process: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			scale: 100,
			selected: {},
			showInput: false,
			showConfig: false,
		};
	},
	computed: {
		// ...mapState('flow', ['design', 'selectedNode'])
	},
	created() {
		// this.$store.commit('flow/setDiagramMode', 'viewer')
		// const form = {
		// 	id: 'root',
		// 	parentId: null,
		// 	type: 'ROOT',
		// 	name: '发起人',
		// 	desc: '任何人',
		// 	props: {
		// 		assignedUser: [],
		// 		formPerms: []
		// 	},
		// }
		// this.$store.commit('flow/loadForm', form)
	},
	methods: {
		validate() {
			return this.$refs['process-tree'].validateProcess();
		},
		nodeSelected(node, drawShow = true) {
			if (node.type === 5 || node.type === 8 || node.type === 9) {
				this.showConfig = false;
			} else {
				this.showConfig = drawShow && node.editable;
			}
		},
	},
};
</script>

<style lang="scss" scoped>
.design {
	margin-top: 60px;
	display: flex;
	transform-origin: 50% 0px 0px;
}

.scale {
	z-index: 999;
	margin-top: 10px;
	text-align: right;

	span {
		margin: 0 10px;
		font-size: 15px;
		color: #7a7a7a;
		width: 50px;
	}
}

.node-config-content {
	padding: 0 20px 20px;
}

::v-deep .el-drawer__body {
	overflow-y: auto;
}
</style>
