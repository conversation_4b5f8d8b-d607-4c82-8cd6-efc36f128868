<template>
	<div>
		<el-form ref="approveBtnFormRef" :model="approveBtnForm" class="d2-mt-20">
			<el-form-item label="拒绝">
				<el-select v-model="approveBtnForm.status" placeholder="请选择" style="width: 90%" :clearable="true">
					<el-option v-for="item in refuseOptions" :key="item.id" :label="item.optName" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="通过">
				<el-select v-model="approveBtnForm.status1" placeholder="请选择" style="width: 90%" :clearable="true">
					<el-option v-for="item in passOptions" :key="item.id" :label="item.optName" :value="item.id" />
				</el-select>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { processButon } from '/@/api/flow/flow/index.ts'
const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  }
})

const approveBtnForm = ref({
  status: '',
  status1: ''
})
const refuseOptions = ref([])
const passOptions = ref([])


watch(approveBtnForm, (val) => {
  // 拒绝 - 选中
  if (val.status !== '') {
    const statusList = []
    refuseOptions.value.forEach(item => {
      if (item.id === val.status) {
        statusList.push(item)
      }
    })
    props.config.processButtonList.push(...statusList)
  }

  // 通过 - 选中
  if (val.status1 !== '') {
    const status1List = []
    passOptions.value.forEach(item => {
      if (item.id === val.status1) {
        status1List.push(item)
      }
    })
    props.config.processButtonList.push(...status1List)
  }

  // 如果没有选中 - 清空
  if (val.status === '' && val.status1 === '') {
    props.config.processButtonList = []
  }

  props.config.processButtonList = deWeightThree(props.config.processButtonList)

  // 对选中的数据做处理
  const dealList = []
  props.config.processButtonList.forEach(item => {
    if (val.status !== '' && item.id === val.status) {
      dealList.push(item)
    }
    if (val.status1 !== '' && item.id === val.status1) {
      dealList.push(item)
    }
  })

  props.config.processButtonList = dealList
}, { deep: true })

onMounted(() => {
  getProcessBtn()
  props.config.processButtonList.forEach(item => {
    if (item.btnKey === 'PASS') {
      approveBtnForm.value.status1 = item.id
    }

    if (item.btnKey === 'REFUSE') {
      approveBtnForm.value.status = item.id
    }
  })
})

async function getProcessBtn() {
  const { data } = await processButon()
  const options = data || []

  refuseOptions.value = []
  passOptions.value = []
  options.forEach(item => {
    if (item.btnKey === 'REFUSE') {
      refuseOptions.value.push(item)
    } else if (item.btnKey === 'PASS') {
      passOptions.value.push(item)
    }
  })
	console.log(passOptions,'passOptionspassOptions')
}

function deWeightThree(list) {
  const map = new Map()
  list.forEach(item => {
    if (!map.has(item.id)) {
      map.set(item.id, item)
    }
  })
  return [...map.values()]
}
</script>
<style lang="scss" scoped></style>
