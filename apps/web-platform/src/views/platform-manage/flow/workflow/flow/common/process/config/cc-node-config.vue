<template>
	<div>
		<el-button size="mini" icon="el-icon-plus" round class="el-button" @click="selectOrg" />
		<org-items v-model="select" @click="selectOrg" />
		<org-picker ref="orgPickerRef" multiple :selected="select" @ok="selected" />
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import OrgPicker from '../../../components/org-picker.vue'
import OrgItems from '../org-items.vue'
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';


const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  }
})

// 使用 composition API
const flowStore = useDesign();
const orgPickerRef = ref(null)

const select = computed({
	get() {
		return props.config.assignedUser || [];
	},
	set(value) {
		props.config.assignedUser = value;
	},
});

watch(select, (newVal) => {
  props.config.assignedUser = newVal
})

const selectOrg = () => {
  if (orgPickerRef.value) {
    orgPickerRef.value.show()
  }
}

const selected = (selectedItems) => {
	select.value = selectedItems;
}

const removeOrgItem = (index) => {
  select.value.splice(index, 1)
}
</script>

<style lang="scss" scoped>
.option {
	color: #606266;
	margin-top: 20px;
	font-size: small;
}

.desc {
	font-size: small;
	color: #8c8c8c;
}
.org-item {
	margin: 5px;
}
.el-button {
	width: 40px;
	height: 40px;
}
::v-deep .el-button--mini.is-round {
	padding: 0;
}
</style>
