<template>
  <div style="margin-top: 10px">
    <el-tag
      v-for="(org, index) in _value"
      :key="index + '_org'"
      class="org-item"
      :type="org.type === 'dept' ? '' : 'info'"
      closable
      size="mini"
      @close="removeOrgItem(index)"
    >
      {{ org.orgName }}
    </el-tag>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { ElTag } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['input']);

const _value = computed({
  get: () => props.modelValue,
  set: (val) => emit('input', val)
});
console.log(_value,'_value_value_value')
const removeOrgItem = (index) => {
  _value.value.splice(index, 1);
};
</script>

<style scoped>
.org-item {
  margin: 5px;
}
</style>