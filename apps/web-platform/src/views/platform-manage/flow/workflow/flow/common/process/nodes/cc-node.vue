<template>
	<node
		:title="config.nodeName"
		:show-error="showError"
		:content="content"
		:error-info="errorInfo"
		placeholder="请设置抄送人"
		header-bgc="#3296fa"
		header-icon="el-icon-s-promotion"
		@selected="$emit('selected')"
		@delNode="$emit('delNode')"
		@insertNode="type => $emit('insertNode', type)"
	/>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import Node from './node-index.vue'

const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  }
})

const showError = ref(false)
const errorInfo = ref('')

const content = computed(() => {
  if (props.config.props.shouldAdd) {
    return '由发起人指定'
  } else if (props.config.props.assignedUser.length > 0) {
    const texts = []
    props.config.props.assignedUser.forEach(org => texts.push(org.orgName))
    return texts.join('、')
  } else {
    return null
  }
})

// 方法：校验数据配置的合法性
const validate = (err) => {
  showError.value = false
  if (props.config.props.shouldAdd) {
    showError.value = false
  } else if (props.config.props.assignedUser.length === 0) {
    showError.value = true
    errorInfo.value = '请选择需要抄送的人员'
  }
  if (showError.value) {
    err.push(`抄送节点 ${props.config.name} 未设置抄送人`)
  }
  return !showError.value
}

// 示例：如何调用 validate 方法
// watch(props.config, () => {
//   const errors = []
//   validate(errors)
//   console.log(errors)
// }, { deep: true })
</script>


<style scoped></style>
