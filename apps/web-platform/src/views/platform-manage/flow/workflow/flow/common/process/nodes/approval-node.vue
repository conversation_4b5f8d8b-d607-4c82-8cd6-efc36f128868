<template>
	<node
		:title="config.nodeName"
		:show-error="showError"
		:content="content"
		:error-info="errorInfo"
		placeholder="请设置审批人"
		:header-bgc="headerBgc"
		:config="config"
		header-icon="el-icon-s-check"
		@titleChange="titleChange"
		@selected="$emit('selected')"
		@delNode="$emit('delNode')"
		@insertNode="(type) => $emit('insertNode', type)"
	/>
</template>

<script setup>
import { ref, computed } from 'vue';
import Node from './node-index.vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});
// 使用 Pinia
const flowStore = useDesign();
const diagramMode = computed(() => flowStore.diagramMode);
// 响应式状态
const showError = ref(false);
const errorInfo = ref('');

// 计算属性
const headerBgc = computed(() => {
	if (diagramMode === 'viewer') {
		return '#ff943e';
	} else {
		return '#ff943e';
	}
});

const content = computed(() => {
	const config = props.config.props;
	console.log('审批节点 config', props.config);
	switch (config.assignedType) {
		case 1:
			if (config.assignedUser.length > 0) {
				const texts = config.assignedUser.map((org) => org.orgName);
				return texts.join('、');
			} else {
				return '请指定审批人';
			}
		case 5:
			return '发起人自己';
		case 4:
			return '发起人自选一人';
		case 2:
			return '部门主管';
		case 11:
			if (!config.formUser || config.formUser === '') {
				return '指定审批组（未选择）';
			} else {
				return '指定审批组';
			}
		case 3:
			if (config.role.length > 0) {
				const texts = config.role.map((org) => org.orgName);
				return texts.join('、');
			} else {
				return '指定角色（未设置）';
			}
		case 10:
			if (config.post.length > 0) {
				const texts = config.post.map((org) => org.orgName);
				return texts.join('、');
			} else {
				return '指定岗位（未设置）';
			}
		case 9:
			return '指定表达式';
		case 12:
			return '上一级主管人审批';
		default:
			return '未知设置项';
	}
});

// 方法
const validate = (err) => {
	try {
		showError.value = !validate_[props.config.props.assignedType](err);

		if (props.config.props.nobody.handler === 'TO_USER' && props.config.props.nobody.assignedUser.length === 0) {
			errorInfo.value = '审批人为空时，转交给指定人员：【请指定一个具体的人】';
			err.push('审批人为空时，转交给指定人员：【请指定一个具体的人】');
			showError.value = true;
		}

		return showError.value;
	} catch (e) {
		return true;
	}
};

const validate_ASSIGN_USER = (err) => {
	if (props.config.props.assignedUser.length > 0) {
		return true;
	} else {
		errorInfo.value = '请指定审批人员';
		err.push(`${props.config.name} 未指定审批人员`);
		return false;
	}
};

const validate_SELF_SELECT = () => true;

const validate_LEADER_TOP = () => true;

const validate_LEADER = () => true;

const validate_ROLE = (err) => {
	if (props.config.props.role.length <= 0) {
		errorInfo.value = '请指定负责审批的系统角色';
		err.push(`${props.config.name} 未指定审批角色`);
		return false;
	}
	return true;
};

const validate_POST = (err) => {
	if (props.config.props.post.length <= 0) {
		errorInfo.value = '请指定负责审批的系统岗位';
		err.push(`${props.config.name} 未指定审批岗位`);
		return false;
	}
	return true;
};

const validate_SELF = () => true;

const validate_FORM_USER = (err) => {
	if (props.config.props.formUser === '') {
		errorInfo.value = '请指定表单中的人员组件';
		err.push(`${props.config.name} 审批人为表单中人员，但未指定`);
		return false;
	}
	return true;
};

const validate_REFUSE = () => true;

// title更改
const titleChange = (data) => {
	props.config.nodeName = data;
};
</script>

<style scoped></style>
