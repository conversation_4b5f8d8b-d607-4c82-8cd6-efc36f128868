<!-- 审批人选择 -->
<template>
	<div>
		<el-form label-position="top" label-width="90px">
			<el-form-item prop="text" class="user-type">
				<el-radio-group v-model="nodeProps.assignedType">
					<el-radio v-for="t in approvalTypes" :key="t.type" :label="t.type">{{ t.name }}</el-radio>
				</el-radio-group>

				<div class="item-wrap" v-if="nodeProps.assignedType === 1">
					<el-divider style="width: 100%" />
					<div>选择成员</div>
					<el-button size="mini" icon="el-icon-plus" round class="el-button" @click="selectUser" />
					<org-items v-model="nodeProps.assignedUser" />
				</div>
				<!-- <div v-else-if="nodeProps.assignedType === 2">
					<el-divider />
					<div>多人审批时采用的审批方式</div>
					<el-radio-group v-model="nodeProps.multipleMode">
						<el-radio :label="1" style="width: 100%">会签（需要所有审批人同意）</el-radio>
						<el-radio :label="2" style="width: 100%">或签（一名审批人同意即可）</el-radio>
						<el-radio :label="3" style="width: 100%">依次审批（按顺序依次审批）</el-radio>
					</el-radio-group>
				</div> -->
				<div v-else-if="nodeProps.assignedType === 3">
          <el-divider />
          <div class="left-column">
						<div>选择指定角色</div>
						<el-button size="mini" icon="el-icon-plus" round class="el-button" @click="selectRole" />
						<org-items v-model="nodeProps.role" />
					</div>
          <div class="right-column">
						<el-radio-group v-model="nodeProps.extData">
							<el-radio label="2">全部角色人员</el-radio>
							<el-radio label="1">发起人同部门角色人员</el-radio>
							<el-radio label="3">发起人同企业角色人员</el-radio>
							<el-radio label="4">指定企业/部门</el-radio>
              <el-radio label="5">发起人上级公司角色人员</el-radio>
              <el-radio label="6">上一级审批人上级公司角色人员</el-radio>
						</el-radio-group>
					</div>
					<div v-if="nodeProps.extData === '4'">
						<el-divider />
						组织机构代码(支持表达式):
						<el-input v-model="nodeProps.targetOrgCode" :maxlength="100" />
					</div>
        </div>
				<div class="item-wrap" v-else-if="nodeProps.assignedType === 10">
					<el-divider />
					<div>选择指定岗位</div>
					<el-button size="mini" icon="el-icon-plus" round class="el-button" @click="selectPost" />
					<org-items v-model="nodeProps.post" />
				</div>
				<div class="item-wrap" v-else-if="nodeProps.assignedType === 11">
					<el-divider />
					<div>选择审批组</div>
					<el-select v-model="nodeProps.formUser" placeholder="请选择" filterable :clearable="true">
						<el-option v-for="item in approveGroup" :key="item.id" :label="item.name" :value="{ value: item.name, label: item.id }" />
					</el-select>
				</div>
				<div class="item-wrap" v-else-if="nodeProps.assignedType === 9">
					<el-divider />
					<el-input v-model="nodeProps.expression" :maxlength="100" />
				</div>
				<div v-else>
					<span v-if="false" class="item-desc">发起人自己作为审批人进行审批</span>
				</div>

				<el-divider />
				<div>多人审批时采用的审批方式</div>
				<el-radio-group v-model="nodeProps.multipleMode">
					<el-radio :label="1" style="width: 100%">会签（需要所有审批人同意）</el-radio>
					<el-radio :label="2" style="width: 100%">或签（一名审批人同意即可）</el-radio>
					<el-radio :label="3" style="width: 100%">依次审批（按顺序依次审批）</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-divider />
			<el-form-item prop="text" class="line-mode">
        <div>缺少审批人时</div>
        <el-radio-group v-model="nodeProps.nobody.handler">
          <el-radio label="WAITING">等待审批</el-radio>
          <el-radio label="TO_PASS">自动通过</el-radio>
          <el-radio label="TO_REFUSE">自动结束</el-radio>
        </el-radio-group>

        <div v-if="nodeProps.nobody.handler === 'TO_USER'" style="margin-top: 10px">
          <el-button size="mini" icon="el-icon-plus" round @click="selectNoSetUser" />
          <org-items v-model="nodeProps.nobody.assignedUser" />
        </div>
      </el-form-item>
      <el-divider />
      <el-form-item  prop="text" class="line-mode">
        <div>存在审批人时</div>
        <el-radio-group v-model="nodeProps.somebody.handler">
          <el-radio label="WAITING">等待审批</el-radio>
          <el-radio label="TO_PASS">自动通过</el-radio>
          <el-radio label="TO_REFUSE">自动结束</el-radio>
        </el-radio-group>
      </el-form-item>
		</el-form>
		<org-picker ref="orgPickerRef" :title="pickerTitle" multiple :type="orgPickerType" :selected="orgPickerSelected" @ok="selected" />
	</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { actGroupList, listAssigneeTypes } from '/@/api/flow/flow/index.ts';
import OrgItems from '/@/views/platform-manage/flow/workflow/flow/common/process/org-items.vue';
import OrgPicker from '/@/views/platform-manage/flow/workflow/flow/components/org-picker.vue';
// 使用 composition API
const flowStore = useDesign();
const selectNode = computed(() => flowStore.selectedNode);
const orgPickerRef = ref();
const showOrgSelect = ref(false);
const orgPickerSelected = ref([]);
const orgPickerType = ref('user');
const approvalTypes = ref([]);
const approveGroup = ref([]); // 审批组

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

const nodeProps = computed(() => selectNode.value.props);
const select = computed(() => props.config.assignedUser || []);
const pickerTitle = computed(() => {
	switch (orgPickerType.value) {
		case 'user':
			return '请选择人员';
		case 'role':
			return '请选择指定角色';
		case 'post':
			return '请选择指定岗位';
		default:
			return null;
	}
});

watch(
	nodeProps,
	(val) => {
		console.log(val, '---------------------------------------------');
		// 逻辑保持不变
		// 这里可以添加和原有逻辑相同的处理
	},
	{ deep: true }
);

onMounted(() => {
	getActGroupList();
	getApproveTypeList();
});

// 方法
const getApproveTypeList = async () => {
	const { code, data } = await listAssigneeTypes();
	if (code === 200) {
		data?.forEach((item) => {
			item.type = item.code;
		});
		approvalTypes.value = data || [];
	}
};

const selectUser = () => {
	orgPickerSelected.value = select.value;
	orgPickerType.value = 'user';
	if (orgPickerRef.value) {
		orgPickerRef.value.show();
	}
};

const selectNoSetUser = () => {
	orgPickerSelected.value = props.config.nobody.assignedUser;
	orgPickerType.value = 'user';
	orgPickerRef.value.show('user');
};

const selectRole = () => {
	orgPickerSelected.value = props.config.role;
	orgPickerType.value = 'role';
	orgPickerRef.value.show('role');
};

const selectPost = () => {
	orgPickerSelected.value = props.config.post;
	orgPickerType.value = 'post';
	orgPickerRef.value.show('post');
};

const selected = (select) => {
	if (orgPickerType.value === 'role') {
		selectNode.value.props.role = select;
	} else if (orgPickerType.value === 'post') {
		selectNode.value.props.post = select;
	} else if (orgPickerType.value === 'user') {
		selectNode.value.props.assignedUser = select;
	} else {
		orgPickerSelected.value.length = 0;
		select.forEach((val) => orgPickerSelected.value.push(val));
	}
};

const removeOrgItem = (index) => {
	select.value.splice(index, 1);
};

const getActGroupList = () => {
	actGroupList().then((res) => {
		if (res.code === 200) {
			approveGroup.value = res.data;
		}
	});
};
</script>
<style lang="scss" scoped>
.user-type {
  .left-column, .right-column {
  display: inline-block;
  width: 50%;
  vertical-align: top;
}

.right-column {
  vertical-align: inherit;
}
.right-column :deep(.el-radio) {
  width: 100% !important;
}
}
:deep(.el-radio) {
	width: 23%;
	margin-top: 10px;
	margin-bottom: 20px;
}

:deep(.line-mode) {
	.el-radio {
		width: 150px;
		margin: 5px;
	}
}

:deep(.el-form-item__label) {
	line-height: 25px;
}

:deep(.approve-mode) {
	.el-radio {
		float: left;
		width: 100%;
		display: block;
		margin-top: 15px;
	}
}

:deep(.approve-end) {
	position: relative;

	.el-radio-group {
		width: 160px;
	}

	.el-radio {
		margin-bottom: 5px;
		width: 100%;
	}

	.approve-end-leave {
		position: absolute;
		bottom: -5px;
		left: 150px;
	}
}

:deep(.el-divider--horizontal) {
	margin: 10px 0;
}

.el-button {
	width: 40px;
	height: 40px;
}

:deep(.el-button--mini.is-round) {
	padding: 0;
}

.item-wrap {
	width: 100%;
}
</style>
