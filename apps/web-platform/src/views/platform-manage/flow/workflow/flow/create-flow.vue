<!-- 创建工作流 -->
<template>
	<div>
		<pro-back-pre :title="text" path="list" />
		<div class="create-flow">
			<div class="create-flow-first" :class="activeIndex === 1 ? 'flow-first' : ''" @click="handleClick(1)">
				<span :class="activeIndex === 1 ? 'flow-first-span' : 'flow-first-default'"> 1 </span>
				<span :class="activeIndex === 1 ? 'flow-first-p' : ''">基础信息</span>
			</div>

			<div class="create-flow-first" :class="activeIndex === 2 ? 'flow-first' : ''" @click="handleClick(2)">
				<span :class="activeIndex === 2 ? 'flow-first-span' : 'flow-first-default'"> 2 </span>
				<span :class="activeIndex === 2 ? 'flow-first-p' : ''">变量设计</span>
			</div>

			<div class="create-flow-second" :class="activeIndex === 3 ? 'flow-first' : ''" @click="handleClick(3)">
				<span :class="activeIndex === 3 ? 'flow-first-span' : 'flow-first-default'"> 3 </span>
				<span :class="activeIndex === 3 ? 'flow-first-p' : ''">流程设计</span>
			</div>
		</div>

		<div class="publish">
			<el-button type="primary" size="small" @click="handlePublish"> 发 布 </el-button>
		</div>

		<!-- 基础信息 -->
		<div v-show="activeIndex === 1">
			<el-card class="box-card">
				<el-form ref="ruleFormRef" label-position="top" :model="ruleForm" :rules="rules" class="demo-ruleForm">
					<el-form-item label="名称" prop="name">
						<el-input v-model.trim="ruleForm.name" />
					</el-form-item>
					<el-form-item label="备注" prop="remark">
						<el-input v-model.trim="ruleForm.remark" />
					</el-form-item>
					<el-form-item prop="remark">
						<template #label>
							是否是自动审批流程
							<el-tooltip placement="top-start" effect="dark">
								<template #content>
									<p>本字段仅用于标记该流程是否是自动审批流程，方便查询过滤。具体流程如何流转，以流程设计为准。</p>
								</template>
								<el-icon color="#FFB617" style="cursor: pointer"><QuestionFilled /></el-icon>
							</el-tooltip>
						</template>
						<el-select v-model="ruleForm.ifAuto" placeholder="请选择" style="width: 100%">
							<el-option v-for="item in ifAutoOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</el-form-item>
				</el-form>
			</el-card>
		</div>

		<!-- 变量设计 -->
		<div v-show="activeIndex === 2">
			<el-card class="variable-card">
				<el-button type="primary" size="small" @click="handleVariable"> 新增变量 </el-button>
				<el-form ref="variableFormRef" :model="variableForm.form" :rules="variableRules" class="demo-ruleForm" label-width="100px">
					<div v-for="(domain, index) in variableForm.form.domains" :key="domain.key" style="display: flex; align-items: center">
						<div style="display: flex; width: 100%; margin-top: 10px">
							<el-form-item
								:label="'变量code'"
								:prop="'domains.' + index + '.code'"
								:rules="{
									required: true,
									message: '变量code不能为空',
									trigger: 'blur',
								}"
							>
								<el-input v-model.trim="domain.code" :maxlength="50" style="width: 160px" clearable />
							</el-form-item>
							<el-form-item
								:label="'变量名称'"
								:prop="'domains.' + index + '.name'"
								:rules="{
									required: true,
									message: '变量名称不能为空',
									trigger: 'blur',
								}"
							>
								<el-input v-model.trim="domain.name" :maxlength="50" style="width: 160px" clearable />
							</el-form-item>
							<el-form-item
								:label="'变量类型'"
								:prop="'domains.' + index + '.type'"
								:rules="{
									required: true,
									message: '变量类型不能为空',
									trigger: 'change',
								}"
							>
								<el-select v-model="domain.type" placeholder="请选择" style="width: 160px">
									<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" clearable />
								</el-select>
							</el-form-item>
							<el-form-item :label="'变量默认值'" :prop="'domains.' + index + '.value'">
								<el-input v-model.trim="domain.value" :maxlength="50" style="width: 160px; margin-bottom: 18px" clearable />
							</el-form-item>
							<el-form-item
								v-if="domain.type === 'DICT'"
								:label="'变量字典值'"
								:prop="'domains.' + index + '.dictCode'"
								:rules="{
									required: true,
									message: '变量字典值不能为空',
									trigger: 'blur',
								}"
							>
								<el-input v-model.trim="domain.dictCode" :maxlength="50" style="width: 160px" clearable />
							</el-form-item>
						</div>

						<el-button v-if="variableForm.form.domains.length > 1" type="danger" size="small" style="height: 30px" @click.prevent="removeDomain(domain)"> 删除 </el-button>
					</div>
				</el-form>
			</el-card>
		</div>

		<!-- 流程设计 -->
		<div v-show="activeIndex === 3">
			<CreateFlow :process="process" />
		</div>
	</div>
</template>
<script setup>
import { reactive, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { processGet, processSave } from '/@/api/flow/flow/index.ts';
const CreateFlow = defineAsyncComponent(() => import('./index.vue'));
// 使用 Pinia
const flowStore = useDesign();
const domNode = computed(() => flowStore.domNode.value);
// const variableForm = computed(() => flowStore.variableForm)
const variableForm = reactive({
	form: {
		domains: [
			{
				code: '',
				name: '',
				type: '',
				value: '',
				dictCode: '',
			},
		],
	},
});
const router = useRouter();
const route = useRoute();
const text = ref('创建审批设置');
const activeIndex = ref(1);
const ruleFormRef = ref(null);
const variableFormRef = ref(null);
const ruleForm = reactive({
	name: '',
	remark: '',
	ifAuto: false,
});
const rules = reactive({
	name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
});
const flowId = ref('');
const process = ref('');
const variableRules = reactive({
	code: [{ required: true, message: '请输入code', trigger: 'blur' }],
	name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
	type: [{ required: true, message: '请选择类型', trigger: 'change' }],
	value: [{ required: true, message: '请输入value', trigger: 'blur' }],
	dictCode: [{ required: true, message: '请输入字典code值', trigger: 'blur' }],
});
const options = reactive([
	{
		value: 'DICT',
		label: '字典',
	},
	{
		value: 'USER',
		label: '用户',
	},
	{
		value: 'DEPT',
		label: '部门',
	},
	{
		value: 'TEXT',
		label: '文本',
	},
	{
		value: 'NUMBER',
		label: '数值',
	},
	{
		value: 'DATE',
		label: '日期',
	},
	{
		value: 'TIME',
		label: '时间',
	},
	{
		value: 'DATETIME',
		label: '日期时间',
	},
	{
		value: 'BOOLEAN',
		label: '布尔',
	},
]);
const ifAutoOptions = [
	{
		value: true,
		label: '是',
	},
	{
		value: false,
		label: '否',
	},
];
onMounted(() => {
	// const { type, flowId } = route.query
	flowId.value = route.query.flowId;
	if (route.query.type === 'edit') {
		getDetails();
		text.value = '修改审批设置';
	} else {
		text.value = '创建审批设置';
	}
});

// 获取详情
const getDetails = () => {
	processGet({ flowId: flowId.value }).then((res) => {
		const { name, remark } = res.data;
		const processParse = JSON.parse(res.data.process);
		const extField = res.data.extField ? JSON.parse(res.data.extField) : {};
		ruleForm.name = name;
		ruleForm.remark = remark;
		ruleForm.ifAuto = extField.ifAuto !== undefined ? extField.ifAuto : false;

		const varIable = {
			domains: [
				{
					code: '',
					name: '',
					type: '',
					value: '',
					dictCode: '',
				},
			],
		};

		// 用来处理旧有数据  后端庞梁轩沟通
		if (processParse.childNode && !processParse.childNode.somebody) {
			nextTick(() => {
				processParse.childNode.props.somebody = { handler: 'WAITING', assignedUser: [] };
			});
		}
		process.value = JSON.stringify(processParse);
		flowStore.setDomNode(process);
		variableForm.form = res.data.settings ? JSON.parse(res.data.settings) : varIable;
		if (processParse.id === 'root') {
			if (processParse.nodeUserList) {
				processParse.nodeUserList.forEach((item) => {
					item.orgId = item.id;
					item.orgName = item.name;
				});
			}
			processParse.props = {
				assignedUser: processParse.nodeUserList || [],
				role: [],
				post: [],
				expression: '',
			};
			processParse.desc = '任何人';
			processParse.parentId = null;
			recursiveObject(processParse);
		}

		const form = {
			process: processParse,
		};
		flowStore.loadForm(form);
	});
};

// 递归对象 - 寻找childNode
const recursiveObject = (obj) => {
	for (const key in obj) {
		if (key === 'childNode') {
			if (obj['childNode'] !== undefined) {
				obj['childNode'].props = (obj['childNode']?.propsData && JSON.parse(obj['childNode'].propsData)) || {};

				if (obj['childNode'].props.type === 1 || obj['childNode'].props.type === 10 || obj['childNode'].props.type === 11) {
					obj['childNode'].props.assignedUser = obj['childNode'].nodeUserList.map((item) => {
						item.orgId = item.id;
						item.orgName = item.name;
						return item;
					});
					obj['childNode'].assignedUser = obj['childNode'].nodeUserList.map((item) => {
						item.orgId = item.id;
						item.orgName = item.name;
						return item;
					});
				} else if (obj['childNode'].type === 5) {
					if (obj['childNode'].conditionNodes.length > 0) {
						obj['childNode'].conditionNodes.forEach((item) => {
							if (item.childNode) {
								item.childNode.props = (item.childNode?.propsData && JSON.parse(item.childNode.propsData)) || {};
							}
						});
					}
				} else if (obj['childNode'].type === 4) {
					if (obj['childNode'].conditionNodes.length > 0) {
						obj['childNode'].conditionNodes.forEach((item) => {
							if (item.childNode) {
								item.childNode.props = (item.childNode?.propsData && JSON.parse(item.childNode.propsData)) || {};
							}
						});
						obj['childNode'].conditionNodes = JSON.parse(obj['childNode'].propsData).conditionNodes || [];
					}
				}
			}
			recursiveObject(obj['childNode']);
		}
	}
};
// 处理传参数据
const dealChildNodeData = (obj) => {
	for (const key in obj) {
		if (key === 'childNode') {
			dealChildNodeData(obj['childNode']);
		}
		if (JSON.stringify(obj['childNode']) == '{}') {
			obj['childNode'] = null;
		}
		if (key === 'type' && (obj['key'] !== null || obj['key'] !== undefined)) {
			if (obj['type'] === 0) {
				const texts = [];
				obj.props.assignedUser.forEach((org) => texts.push(org.orgName));
				obj.placeholder = String(texts).replaceAll(',', '、');
				if (obj.props.assignedUser.length > 0) {
					obj.nodeUserList = [];
					obj.props.assignedUser.map((item) => {
						obj.nodeUserList.push({
							id: item.orgId,
							name: item.orgName,
							type: 'user',
						});
					});
				}
				dealChildNodeData(obj['childNode']);
			}

			// 处理审批节点的数据
			if (obj['type'] === 1) {
				// 指定用户
				if (obj.props.assignedType === 1) {
					obj.props.nodeUserList = [];
					obj.props.assignedUser.map((item) => {
						obj.props.nodeUserList.push({
							id: item.userId,
							name: item.name,
							type: 'user',
						});
					});
				}
				// 部门主管
				if (obj.props.assignedType === 2) {
					obj.props.type = 'deptAdmin';
					obj.props.nodeUserList = [];
					obj.props.nodeUserList.push({
						type: 'deptAdmin',
					});
				}
				// 指定角色
				if (obj.props.assignedType === 3) {
					obj.props.nodeUserList = [];
					obj.props.role.map((item) => {
						obj.props.nodeUserList.push({
							id: item.roleId,
							name: item.roleName,
							type: 'role',
						});
					});
					obj.props.type = 'role';
				}
				// 指定岗位
				if (obj.props.assignedType === 10) {
					obj.props.nodeUserList = [];
					obj.props.post.map((item) => {
						obj.props.nodeUserList.push({
							id: item.postId,
							name: item.postName,
							type: 'post',
						});
					});
					obj.props.type = 'post';
				}
				// 发起人自选
				if (obj.props.assignedType === 4) {
					obj.props.type = 'selected';
					obj.props.nodeUserList = [];
					obj.props.nodeUserList.push({
						type: 'selected',
					});
				}
				// 发起人自己
				if (obj.props.assignedType === 5) {
					obj.props.type = 'own';
					obj.props.nodeUserList = [];
					obj.props.nodeUserList.push({
						type: 'own',
					});
				}
				// 指定表达式
				if (obj.props.assignedType === 9) {
					obj.props.type = 'candidateGroups';
					obj.props.nodeUserList = [];
					obj.props.nodeUserList.push({
						expression: obj.props.expression,
						type: 'candidateGroups',
					});
				}
				// 指定审批组
				if (obj.props.assignedType === 11) {
					obj.props.nodeUserList = [];
					obj.props.nodeUserList.push({
						id: obj.props.formUser.value,
						name: obj.props.formUser.label,
						type: 'approveGroups',
					});
				}
				// 上一级审批人主管
				if (obj.props.assignedType === 12) {
					obj.props.type = 'prevDeptAdmin';
				}

				const texts = [];
				obj.props.nodeUserList && obj.props.nodeUserList.forEach((org) => texts.push(org.name));
				obj.placeholder = String(texts).replaceAll(',', '、');
				obj.nodeUserList = obj.props.nodeUserList;
				obj.nobody = obj.props.nobody;
				obj.somebody = obj.props.somebody;
				obj.processButtonList = obj.props.processButtonList;
				obj.assignedType = obj.props.assignedType;
				obj.multipleMode = obj.props.multipleMode;
				obj.extData = obj.props.extData;
				obj.targetOrgCode = obj.props.targetOrgCode;
				obj.propsData = JSON.stringify(obj.props);
			}

			// 处理服务节点的数据
			if (obj['type'] === 6) {
				obj.async = obj.props.async;
				obj.feignServiceName = obj.props.feignServiceName;
				obj.feignServicePath = obj.props.feignServicePath;
				obj.taskType = obj.props.taskType;
				obj.triggerable = obj.props.triggerable;
				obj.propsData = JSON.stringify({
					async: obj.props.async,
					feignServiceName: obj.props.feignServiceName,
					feignServicePath: obj.props.feignServicePath,
					taskType: obj.props.taskType,
					triggerable: obj.props.triggerable,
					id: obj.props.id,
					nodeName: obj.props.nodeName,
				});
				obj.nodeInfo = {
					async: obj.props.async,
					feignServiceName: obj.props.feignServiceName,
					feignServicePath: obj.props.feignServicePath,
					taskType: obj.props.taskType,
					triggerable: obj.props.triggerable,
				};
			}

			// 处理抄送人节点的数据
			if (obj['type'] === 2) {
				const texts = [];
				obj.props.assignedUser.forEach((org) => texts.push(org.orgName));
				obj.placeholder = String(texts).replaceAll(',', '、');
				if (obj.props.assignedUser.length > 0) {
					obj.props.nodeUserList = [];
					obj.props.assignedUser.map((item) => {
						obj.props.nodeUserList.push({
							id: item.userId,
							name: item.name,
							type: 'user',
						});
					});
				}

				obj.nodeUserList = obj.props.nodeUserList;
				obj.propsData = JSON.stringify(obj.props);
			}

			// 处理并行分支节点的数据
			if (obj['type'] === 5) {
				// 置空空标签的childNode
				if (obj.childNode.type === 3) {
					delete obj.childNode.childNode;
				}
				obj.propsData = JSON.stringify({
					conditionNodes: obj.conditionNodes,
					id: obj.id,
					nodeName: obj.nodeName,
					parentId: obj.parentId,
					type: obj.type,
					props: obj.props,
				});

				if (obj.conditionNodes.length > 0) {
					obj.conditionNodes.map((item) => {
						if ('childNode' in item && item.childNode !== null) {
							handleBranchsData(item.childNode);
						}
					});
				}
			}

			// 处理条件分支节点的数据
			if (obj['type'] === 4) {
				// 置空空标签的childNode
				if (obj.childNode.type === 3) {
					delete obj.childNode.childNode;
				}
				obj.propsData = JSON.stringify({
					conditionNodes: obj.conditionNodes,
					id: obj.id,
					nodeName: obj.nodeName,
					parentId: obj.parentId,
					type: obj.type,
				});

				if (obj.conditionNodes.length > 0) {
					obj.conditionNodes.map((item) => {
						item.groupMode = item.props.groupMode;
						item.conditionList = item.props.conditionNodes[0].conditionList;
						if ('childNode' in item && item.childNode !== null) {
							handleBranchsData(item.childNode);
						}

						if (item.conditionList) {
							item.conditionList.map((nodeItem) => {
								nodeItem &&
									nodeItem.conditionList?.map((nodeIten) => {
										nodeIten &&
											nodeIten.assignedUser?.map((userInfo) => {
												nodeIten.value?.push({
													id: userInfo.userId || userInfo.orgId,
													name: userInfo.orgName,
													type: 'user',
													avatar: userInfo.avatar,
												});
											});
									});
							});
						}
					});
				}
			}
		}
	}
};
// 处理分支内容数据
const handleBranchsData = (obj) => {
	dealChildNodeData(obj);
};

// 点击切换流程
const handleClick = (index) => {
	activeIndex.value = index;
};

// 新增变量
const handleVariable = () => {
	variableForm.form.domains.push({
		code: '',
		name: '',
		type: '',
		value: '',
		dictCode: '',
	});
};

// 删除变量
const removeDomain = (item) => {
	const index = variableForm.form.domains.indexOf(item);
	if (index !== -1) {
		variableForm.form.domains.splice(index, 1);
	}
};

// 发布
const handlePublish = () => {
	const { name, remark, ifAuto } = ruleForm;
	dealChildNodeData(domNode.value);
	if (name === '') return useMessage().error('请完善基础信息名称');

	if (domNode.childNode && state.domNode.id === 'root' && !('type' in domNode.childNode)) {
		domNode.childNode = null;
	}

	const data = {
		flowId: flowId.value,
		name,
		remark,
		extField: JSON.stringify({ ifAuto }),
		process: JSON.stringify(domNode.value),
		settings: JSON.stringify(variableForm.form),
	};
	processSave(data).then((res) => {
		if (res.code !== 200) return;
		router.replace('/platform-manage/flow/workflow/flow/list');
		useMessage().success('发布成功');
		const form = {
			process: {
				id: 'root',
				parentId: null,
				type: 0,
				nodeName: '发起人',
				desc: '任何人',
				props: {
					assignedUser: [],
					role: [],
					post: [],
					expression: '',
				},
				childNode: {},
			},
		};
		flowStore.loadForm(form);
	});
};
watch(variableForm, (val) => {
	flowStore.setVariableForm(val.form.domains);
});
</script>

<style lang="scss" scoped>
.create-flow {
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 18px;
	margin-top: 20px;

	&-first {
		margin-right: 20px;
		padding: 0 10px;
		display: flex;
		align-items: center;
		cursor: pointer;

		span {
			margin-bottom: 12px;
		}
	}

	&-second {
		margin-right: 20px;
		padding: 0 20px;
		display: flex;
		align-items: center;
		cursor: pointer;

		span {
			margin-bottom: 12px;
		}
	}

	.flow-first-default {
		margin-right: 6px;
		display: inline-block;
		width: 20px;
		height: 20px;
		text-align: center;
		font-size: 17px;
		border-radius: 10px;
		border: 1px solid #333;
		line-height: 20px;
	}

	.flow-first {
		line-height: 20px;
		border-bottom: 2px solid #2e5cf6;
	}

	.flow-first-span {
		margin-right: 6px;
		display: inline-block;
		width: 20px;
		height: 20px;
		text-align: center;
		font-size: 17px;
		border-radius: 10px;
		background: #2e5cf6;
		color: #fff;
		border: 1px solid #2e5cf6;
	}

	.flow-first-p {
		color: #2e5cf6;
	}
}

.publish {
	margin-right: 30px;
	text-align: right;
}

.box-card {
	margin: 0 auto;
	margin-top: 20px;
	padding: 20px;
	width: 580px;
}

.variable-card {
	margin: 0 auto;
	margin: 20px 50px;
	padding: 20px;
}
</style>
