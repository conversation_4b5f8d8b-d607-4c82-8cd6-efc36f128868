<!-- 条件分支 -->
<template>
	<div>
		<el-form label-width="120px">
			<el-form-item label="条件组关系">
				<el-switch v-model="nodeProps.conditionNodes[0].mode" active-text="且" active-value="true" inactive-text="或" inactive-value="false" />
			</el-form-item>
		</el-form>

		<el-card v-for="(item, index) in nodeProps.conditionNodes[0].conditionList" :key="index" class="box-card" style="margin-bottom: 20px">
			<template #header>
				<div class="card-header">
					<span>条件组{{ index + 1 }}</span>
					<el-switch v-model="item.mode" active-text="且" inactive-text="或" :active-value="true" :inactive-value="false" />
					<i v-if="nodeProps.conditionNodes[0].conditionList.length > 1" class="el-icon-delete" style="cursor: pointer" @click="deleteGroup(index)" />
				</div>
			</template>
			<div v-for="(item1, index1) in item.conditionList" :key="index1">
				<div style="display: flex; justify-content: space-between; width: 100%">
					<div style="width: 100%">
						<div class="card-header card-margin">
							{{ index1 == 0 ? '当' : item.mode ? '且' : '或' }}
							<i v-if="item.conditionList.length > 1" class="el-icon-delete" style="cursor: pointer" @click="deleteCondition(index, index1)" />
						</div>
						<div>
							<el-select
								v-model="item1.name"
								placeholder="选择变量"
								value-key="name"
								style="width: 100%; margin-bottom: 20px"
								size="small"
								@change="
									(val) => {
										veriableFormChange(val, index, index1);
									}
								"
							>
								<el-option v-for="iten in variableForm" :key="iten.name" :label="iten.name" :value="iten" />
							</el-select>
							<el-select v-model="item1.expression" placeholder="选择关系" style="width: 100%" size="small">
								<el-option v-for="iten in item1.option1" :key="iten.value" :label="iten.label" :value="iten.value" />
							</el-select>

							<!-- 用户 -->
							<el-button
								v-if="item1.type === 'USER' || item1.type === 'DEPT'"
								size="mini"
								icon="el-icon-plus"
								round
								style="width: 40px; height: 40px; margin-top: 20px"
								@click="selectUser(index, index1)"
							/>
							<div v-if="item1.type === 'USER' || item1.type === 'DEPT'" style="margin-bottom: 12px">
								<org-items v-model="item1.assignedUser" />
							</div>

							<div style="margin-top: 12px">
								<!-- 文本 -->
								<el-input v-if="item1.type === 'TEXT'" v-model="item1.value" placeholder="请输入文本" />
								<!-- 数字 -->
								<el-input v-if="item1.type === 'NUMBER'" v-model="item1.value" type="number" placeholder="请输入数字" />
								<!-- 日期选择框 -->
								<el-date-picker v-if="item1.type === 'DATE'" v-model="item1.value" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" />
								<!-- 时间选择框 -->
								<el-time-picker v-if="item1.type === 'TIME'" v-model="item1.value" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="选择时间" />
								<!-- 时间日期选择 -->
								<el-date-picker v-if="item1.type === 'DATETIME'" v-model="item1.value" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间" />
								<!-- 布尔 -->
								<el-radio-group v-if="item1.type === 'BOOLEAN'" v-model="item1.value">
									<el-radio :label="true">true</el-radio>
									<el-radio :label="false">false</el-radio>
								</el-radio-group>
								<!-- 字典 -->
								<el-select v-if="item1.type === 'DICT'" v-model="item1.value" placeholder="选择字典" style="width: 100%; margin-top: 12px" size="small">
									<el-option v-for="iten in dicList" :key="iten.dicTypeId" :label="iten.dicTypeName" :value="iten.dicTypeId" />
								</el-select>
							</div>
						</div>
					</div>
				</div>
			</div>
			<el-button dark type="success" style="margin: 20px 0px" @click="addOneCondition(item, index)">添加条件</el-button>
		</el-card>
		<el-button dark type="primary" @click="addOneConditionGroup">添加条件组</el-button>
		<!-- 发起人，选择弹窗 -->
		<org-picker ref="orgPickerRef" title="请选择可发起人" multiple :selected="orgPickerSelected" @ok="selected" />
	</div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';
import { useDesign } from '/@/views/platform-manage/flow/workflow/flow/flow.js';
import { getCurrenTypeList } from '/@/api/system/dictionaries.ts';
import OrgItems from '/@/views/platform-manage/flow/workflow/flow/common/process/org-items.vue';
import OrgPicker from '/@/views/platform-manage/flow/workflow/flow/components/org-picker.vue';

// 使用 Pinia
const flowStore = useDesign();

const props = defineProps({
	config: {
		type: Object,
		default: () => ({}),
	},
});

const conditionsConfig = ref({
	conditionNodes: [],
});
const conditionConfig = ref({
	mode: true,
	conditionList: [{}],
});
const PriorityLevel = ref('');
const option1 = ref([]);
const orgPickerSelected = ref([]);
const orgPickerType = ref('user');
const assignedUser = ref([]);
const groupIndex = ref(0);
const groupIndex1 = ref(0);
const dicList = ref([]);
const orgPickerRef = ref();

const select = computed(() => props.config.assignedUser);
const nodeProps = computed(() => flowStore.selectedNode?.props);
const variableForm = computed(() => flowStore.variableForm);



onMounted(() => {
	// 创建一个默认组
	createOneCondition(nodeProps.value?.conditionNodes[0]?.conditionList[0]);
});

const deleteGroup = (index) => {
	nodeProps.value.conditionNodes[0].conditionList.splice(index, 1);
};

const veriableFormChange = (value, index, index1) => {
	console.log('变量更改', value);
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].key = value.code;
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].type = value.type;
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].expression = '';
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].name = value.name;
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].option1 = dealVeriableFormType(value);
	if (value.type === 'DICT') {
		nodeProps.value.conditionNodes[0].conditionList[index].conditionList[index1].dicValue = value.dictCode;
		getParentList(value.dictCode);
	}
};

const getParentList = (value) => {
	getCurrenTypeList(value).then((res) => {
		console.log('字典', res.data);
		dicList.value = res.data;
	});
};

const dealVeriableFormType = (value) => {
	console.log('回显', value);
	if (value.type === 'DICT') {
		getParentList(value.dicValue);
	}
	let option1 = [];
	if (value.type === 'DICT' || value.type === 'USER' || value.type === 'DEPT') {
		option1 = [
			{ value: 'in', label: '属于' },
			{ value: 'notin', label: '不属于' },
		];
	} else if (value.type === 'TEXT') {
		option1 = [
			{ value: 'contain', label: '包含' },
			{ value: 'notcontain', label: '不包含' },
			{ value: '==', label: '等于' },
			{ value: '!=', label: '不等于' },
		];
	} else if (value.type === 'NUMBER' || value.type === 'DATE' || value.type === 'TIME' || value.type === 'DATETIME') {
		option1 = [
			{ value: '==', label: '等于' },
			{ value: '!=', label: '不等于' },
			{ value: '>', label: '大于' },
			{ value: '>=', label: '大于等于' },
			{ value: '<', label: '小于' },
			{ value: '<=', label: '小于等于' },
		];
	} else if (value.type === 'BOOLEAN') {
		option1 = [
			{ value: '==', label: '等于' },
			{ value: '!=', label: '不等于' },
		];
	}

	return option1;
};

const deleteCondition = (index, index1) => {
	nodeProps.value.conditionNodes[0].conditionList[index].conditionList.splice(index1, 1);
};

const addOneConditionGroup = () => {
	nodeProps.value.conditionNodes[0].conditionList.push({
		mode: true,
		conditionList: [{}],
	});
};

const addOneCondition = (item) => {
	let conditionList = item?.conditionList;
	if (!conditionList) {
		conditionList = [];
	}

	conditionList.push({});
	item.conditionList = conditionList;
};

const createOneCondition = (item) => {
	let conditionList = item?.conditionList;
	if (!conditionList) {
		conditionList = [];
	}

	if (conditionList.length === 0) {
		conditionList.push({});
	}
	item.conditionList = conditionList;

	conditionList.forEach(($item) => {
		$item.option1 = dealVeriableFormType($item);
	});

	console.log('item', item);
};

const selectUser = (index, index1) => {
	groupIndex.value = index;
	groupIndex1.value = index1;
	orgPickerSelected.value = [];
	orgPickerSelected.value = nodeProps.value.conditionNodes[0].conditionList[groupIndex.value].conditionList[groupIndex1.value].assignedUser || [];
	orgPickerType.value = 'user';
	if (orgPickerRef.value) {
		orgPickerRef.value.show();
	}
};

const selected = (select) => {
	nodeProps.value.conditionNodes[0].conditionList[groupIndex.value].conditionList[groupIndex1.value].assignedUser = [...select];
};
</script>
<style lang="scss" scoped>
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-margin {
	margin: 10px 0;
}

::v-deep .el-button--mini.is-round {
	padding: 0;
}
</style>
