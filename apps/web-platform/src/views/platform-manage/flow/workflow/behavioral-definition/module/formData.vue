<template>
	<el-dialog :title="title" v-model="visible" width="800" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="cancel">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
			<el-row :gutter="10" type="flex" style="flex-wrap: wrap">
				<el-col :span="24" class="mb-5">
					<el-form-item label="行为名称：" prop="optName">
						<el-input v-model.trim="form.optName" placeholder="请填写行为名称" />
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb-5">
					<el-form-item label="关联业务：" prop="busKeyStr">
						<el-select v-model="form.busKeyStr" filterable multiple placeholder="请选择关联业务" clearable style="width: 100%" @change="changeBusKey">
							<el-option v-for="item in busKeyList" :key="item.businessKey" :label="item.businessName" :value="item.businessKey" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb-5">
					<el-form-item label="关联按钮：" prop="btnKey">
						<el-select v-model="form.btnKey" placeholder="请选择关联按钮" clearable style="width: 100%">
							<el-option v-for="item in btnList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb-5">
					<el-form-item label="行为类型：" prop="optTypeKey">
						<el-select v-model="form.optTypeKey" placeholder="请选择行为类型" clearable style="width: 100%" @change="changeOptType">
							<el-option v-for="item in optTypeList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-if="form.optTypeKey == 'URL'" :span="24" class="mb-5">
					<el-form-item label="跳转地址：" prop="url">
						<el-input v-model.trim="form.url" placeholder="请填写跳转地址" />
					</el-form-item>
				</el-col>
				<el-col v-if="form.optTypeKey == 'FEIGN'" :span="24" class="mb-5">
					<el-form-item label="服务名称：" prop="serverName">
						<el-input v-model.trim="form.serverName" placeholder="请填写服务名称" />
					</el-form-item>
				</el-col>
				<el-col v-if="form.optTypeKey == 'FEIGN'" :span="24" class="mb-5">
					<el-form-item label="服务路由：" prop="serverPath">
						<el-input v-model.trim="form.serverPath" placeholder="请填写服务路由" />
					</el-form-item>
				</el-col>
				<el-col v-if="form.optTypeKey == 'FEIGN'" :span="24" class="mb-5">
					<el-form-item label="请求方式：" prop="method">
						<el-select v-model="form.method" placeholder="请选择请求方式" clearable style="width: 100%">
							<el-option v-for="item in methodList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-if="form.optTypeKey == 'FEIGN'" :span="24" class="mb-5">
					<el-form-item label="">
						<div v-for="(item, index) in form.param" :key="item.id" class="sheet-item">
							<el-form-item :prop="`param[${index}][key]`" :rules="rules.field_key" class="sheet-item-input">
								<el-input v-model.trim="item.key" placeholder="请输入参数名" />
							</el-form-item>
							<el-form-item :prop="`param[${index}][value]`" :rules="rules.field_value" class="sheet-item-input">
								<el-input v-model.trim="item.value" placeholder="请输入参数值" />
							</el-form-item>
							<div class="icon-item">
								<el-icon color="#409eff" style="cursor: pointer" @click="addItem(index)"><CirclePlusFilled /></el-icon>
								<el-icon v-if="index > 0" color="#f56c6c" style="cursor: pointer; margin-left: 10px" @click="removeItem(index)"><RemoveFilled /></el-icon>
							</div>
						</div>
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb-5">
					<el-form-item label="自动应用至：" prop="injectPointKey">
						<el-select v-model="form.injectPointKey" placeholder="请选择" clearable style="width: 100%">
							<el-option v-for="item in injectPointList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="行为描述：" prop="optDesc">
						<el-input v-model="form.optDesc" placeholder="请填写行为描述" type="textarea" rows="4" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<el-button type="primary" @click="cancel">取消</el-button>
			<el-button type="primary" @click="submitForm">确定</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { addProcessButton, editProcessButton, getBusProcshipList } from '/@/api/flow/behavioralDefinition/index.ts';
import { rules, btnList, optTypeList, methodList, injectPointList, form } from './formData.js';
import { useMessage } from '/@/hooks/message';

// 数据定义
const visible = ref(false);
const title = ref('添加行为');
const formRef = ref();
const btnId = ref('');
const emit = defineEmits(['btnCallback']);
const busKeyList = ref([]);

// 初始化业务列表
onMounted(() => {
	getBusProcshipList().then((res) => {
		busKeyList.value = res.data;
	});
});

// 编辑行为
const edit = (row) => {
	visible.value = true;
	const obj = JSON.parse(row.optData);
	const result = obj.param ? Object.entries(obj.param).map(([key, value]) => ({ key, value })) : [{ key: '', value: '' }];
	form.url = obj.url;
	form.serverName = obj.serverName;
	form.serverPath = obj.serverPath;
	form.method = obj.method;
	form.param = result;
	form.busKeyStr = row.busKey.split(',');
	form.busKey = row.busKey;
	form.optName = row.optName;
	form.btnKey = row.btnKey;
	form.optTypeKey = row.optTypeKey;
};
const reset = () => {
	form.optName = '';
	form.optTypeKey = '';
	form.url = '';
	form.serverName = '';
	form.serverPath = '';
	form.method = '';
	form.param = [{ key: '', value: '' }];
	form.busKeyStr = [];
	form.busName = '';
	form.busKey = '';
	form.btnKey = '';
	form.injectPointKey = '';
	formRef.value.clearValidate();
};
// 取消操作
const cancel = () => {
	visible.value = false;
	reset();
};

// 表单提交
const submitForm = (formName) => {
	// 假设这里有一个表单验证的方法
	formRef.value?.validate((valid) => {
		if (valid) {
			let api = null;
			if (!btnId.value) {
				api = addProcessButton;
			} else {
				form.id = btnId.value;
				api = editProcessButton;
			}
			form.btnName = form.btnKey === 'PASS' ? '通过' : '拒绝';
			form.optTypeName = form.optTypeKey === 'URL' ? '跳转' : '服务调用';
			form.injectPointName = form.injectPointKey === 'ALL_USER_CUSTOM_NODE' ? '所有用户审核节点' : '最后一个用户审核节点';
			let result = '';
			if (form.optTypeKey === 'FEIGN') {
				result = form.param.reduce((obj, item) => {
					obj[item.key] = item.value;
					return obj;
				}, {});
			}
			const optData = {
				url: form.url,
				serverName: form.serverName,
				serverPath: form.serverPath,
				method: form.method,
				param: result,
			};
			form.optData = JSON.stringify(optData);
			api(form).then((res) => {
				if (res.code === 200) {
					useMessage().success(btnId.value ? '修改成功' : '添加成功');
					reset();
					visible.value = false;
					emit('btnCallback');
				}
			});
		}
	});
};

// 业务键值变化处理
const changeBusKey = (value) => {
	const buskNameList = [];
	if (value && value.length > 0) {
		value.forEach((k) => {
			busKeyList.value.forEach((item) => {
				if (item.businessKey === k) {
					buskNameList.push(item.businessName);
				}
			});
		});
		form.busName = buskNameList.join(',');
		form.busKey = value.join(',');
	}
};

// 行为类型变化处理
const changeOptType = () => {
	form.url = '';
	form.serverName = '';
	form.serverPath = '';
	form.method = '';
	form.param = [{ key: '', value: '' }];
};

// 添加参数项
const addItem = (index) => {
	form.param.splice(index + 1, 0, { key: '', value: '' });
};

// 移除参数项
const removeItem = (index) => {
	form.param.splice(index, 1);
};
// 打开弹窗
const openDialog = (type, row) => {
	if (type === 'edit') {
		title.value = '修改行为';
		btnId.value = row.id;
		edit(row);
	} else {
		title.value = '添加行为';
	}

	visible.value = true;
};
defineExpose({ openDialog });
</script>
<style scoped lang="scss">
.sheet-item {
	display: flex;
	.sheet-item-input {
		margin-right: 20px;
		margin-bottom: 10px;
	}
	.icon-item {
		font-size: 20px;
		.remove {
			color: #f56c6c;
			cursor: pointer;
		}
		.add {
			color: #409eff;
			margin-right: 10px;
			cursor: pointer;
		}
	}
}
</style>
