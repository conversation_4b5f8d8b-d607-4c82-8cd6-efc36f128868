export const tableHeader = [
	{ type: 'index', label: '序号', width: 60, align: 'center' },
	{
		prop: 'optName',
		label: '行为名称',
	},
	{
		prop: 'busKey',
		label: '业务定义key',
	},
	{
		prop: 'busName',
		label: '业务定义名称',
	},
	{
		prop: 'optDesc',
		label: '行为描述',
	},
];
export const searchConfig = {
	items: [
		{
			prop: 'optName',
			type: 'input',
			attrs: {
				placeholder: '请填写行为名称',
				label: '行为名称',
				style: 'width: 260px',
				clearable: true,
			},
		},
		{
			prop: 'optTypeKey',
			type: 'select',
			attrs: {
				placeholder: '请选择行为类型',
				label: '行为类型',
				style: 'width: 260px',
				clearable: true,
			},
			option: [
				{ label: '跳转', value: 'URL' },
				{ label: '服务调用', value: 'FEIGN' },
			],
		},
		{
			prop: 'busKey',
			type: 'input',
			attrs: {
				placeholder: '请填写业务Key',
				label: '业务key',
				style: 'width: 260px',
				clearable: true,
			},
		},
		{
			prop: 'busName',
			type: 'input',
			attrs: {
				placeholder: '请填写业务名称',
				label: '业务名称',
				style: 'width: 260px',
				clearable: true,
			},
		},
		{
			prop: 'btnKey',
			type: 'select',
			attrs: {
				placeholder: '请选择关联按钮',
				label: '关联按钮',
				style: 'width: 260px',
				clearable: true,
			},
			option: [
				{ label: '通过', value: 'PASS' },
				{ label: '拒绝', value: 'REFUSE' },
			],
		},
	],
};
export const formList = [
	{
		name: '支付方式',
		id: 'payType',
		type: 'input',
		dicKey: 'payMentConfigPayType',
		css: 'w-[50%]',
	},
	{ name: '支付类型名称', id: 'payTypeName', type: 'input', css: 'w-[50%]' },
	{ name: '显示名称', id: 'displayName', type: 'input', css: 'w-[50%]' },
	{ name: '应用ID', id: 'appId', type: 'input', css: 'w-[50%]' },
	{ name: '集团/代理商商户号', id: 'orgId', type: 'input', css: 'w-[50%]' },
	{ name: '商户号', id: 'cusId', type: 'input', css: 'w-[50%]' },
	{ name: '商户名', id: 'cusName', type: 'input', css: 'w-[50%]' },
	{ name: '商户公钥', id: 'publicKey', type: 'input', css: 'w-[50%]' },
	{ name: '商户私钥', id: 'privateKey', type: 'input', css: 'w-[50%]' },
	{ name: '支付平台公钥', id: 'payPlatformPublicKey', type: 'input', css: 'w-[50%]' },
	{
		name: '是否线上',
		id: 'ifOnline',
		type: 'switch',
		option: [
			{ activeValue: true, activeText: '线上' },
			{ inactiveValue: false, inactiveText: '线下' },
		],
		css: 'w-[50%]',
	},
	{
		name: '是否启用',
		id: 'ifOpen',
		type: 'switch',
		option: [
			{ activeValue: true, activeText: '开启' },
			{ inactiveValue: false, inactiveText: '关闭' },
		],
		css: 'w-[50%]',
	},
];
// 字典
export const dic = {
	// 发布状态
	// publishState: {
	// 	value: dictApi.tendermethod,
	// 	color: { 0: '#E6A23C', 1: '#67C23A' },
	// },
};
