import {reactive} from 'vue'
export const form = reactive({
  busKeyStr: [],
  param: [{ key: '', value: '' }],
  optName: '',
  btnKey: '',
  optTypeKey: '',
  url: '',
  serverName: '',
  serverPath: '',
  method: '',
  busName: '',
  busKey: '',
  injectPointKey: ''
})

export const rules = {
  optName: [{ required: true, message: '请填写行为名称', trigger: 'blur' }],
  busKeyStr: [{ required: true, message: '请选择关联业务', trigger: 'blur' }],
  btnKey: [{ required: true, message: '请选择关联按钮', trigger: 'blur' }],
  optTypeKey: [{ required: true, message: '请选择行为类型', trigger: 'blur' }],
  url: [{ required: true, message: '请填写跳转地址', trigger: 'blur' }],
  serverName: [{ required: true, message: '请填写服务名称', trigger: 'blur' }],
  serverPath: [{ required: true, message: '请填写服务路由', trigger: 'blur' }],
  method: [{ required: true, message: '请选择请求方式', trigger: 'blur' }]
}
export const btnList = [{ label: '通过', value: 'PASS' }, { label: '拒绝', value: 'REFUSE' }]
export const optTypeList = [{ label: '跳转', value: 'URL' }, { label: '服务调用', value: 'FEIGN' }]
export const methodList = [{ label: 'GET', value: 'GET' }, { label: 'POST', value: 'POST' }]
export const injectPointList = [{ label: '所有用户审核节点', value: 'ALL_USER_CUSTOM_NODE' },{ label: '最后一个用户审核节点', value: 'LAST_USER_CUSTOM_NODE' }]
