<template>
	<el-dialog title="流程图" :visible.sync="open" width="80%" :close-on-click-modal="false">
		<slot name="-" style="border: none; padding: 0px; margin: 0px">
			<!-- <vue-bpmn v-if="open" style="overflow: hidden; height: 550px; display: flex" product="flowable" :init-prcoess="initData" @processSave="processSave" @processDeploy="processDeploy" /> -->
		</slot>
	</el-dialog>
</template>
<script>
// import { addProcessAndDeploy, getProcessXml, activation, hang } from '@/api/system/workflow/BuiltInProcesses'
// import VueBpmn from '@/components/bpmn/VueBpmn'
// import 'bpmn-js/dist/assets/diagram-js.css'
// import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'
// import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'
// import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
export default {
	name: 'FormData',
	components: {
		VueBpmn,
	},
	data() {
		return {
			open: false,
			initData: {},
		};
	},
	mounted() {},
	methods: {
		// 添加
		add() {
			this.initData = {};
			this.open = true;
		},
		// 修改
		edit(data) {
			// this.initData = {}
			getProcessXml({ defineKey: data.key }).then((res) => {
				this.$nextTick(() => {
					this.initData = res.data;
					this.open = true;
				});
			});
		},
		// 组件回调
		processSave(data) {
			data.deploy = false;
		},
		// 保存发布
		processDeploy(data) {
			const loading = this.$loading({
				lock: true,
				text: '部署中...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.4)',
			});
			data.deploy = true;
			data.name = data.name + '.bpmn20.xml';
			addProcessAndDeploy(data)
				.then((res) => {
					this.$message.success('发布成功');
					this.open = false;
					loading.close();
					this.$emit('refresh');
				})
				.catch(() => {
					loading.close();
				});
		},
		// 激活
		activation(data) {
			this.$confirm('确定激活此流程吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				const loading = this.$loading({
					lock: true,
					text: '激活中...',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.4)',
				});
				try {
					activation({ key: data.key }).then((res) => {
						if (res.code !== 200) return this.$message.error(res.msg);
						this.$message.success(res.msg);
						this.$emit('refresh');
						loading.close();
					});
				} catch (error) {
					loading.close();
					this.$message.error(error);
				}
			});
		},
		// 挂起
		hang(data) {
			this.$confirm('确定挂起此流程吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				const loading = this.$loading({
					lock: true,
					text: '激活中...',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.4)',
				});
				try {
					hang({ key: data.key }).then((res) => {
						if (res.code !== 200) return this.$message.error(res.msg);
						this.$message.success(res.msg);
						this.$emit('refresh');
						loading.close();
					});
				} catch (error) {
					loading.close();
					this.$message.error(error);
				}
			});
		},
	},
};
</script>
