<template>
	<el-dialog class="synchronous" title="同步流程" v-model="synchronous" width="800px" top="10vh" :modal-append-to-body="false">
		<el-form ref="queryForm" :inline="true" size="small" :model="queryParams">
			<el-form-item prop="name">
				<el-input v-model="queryParams.orgName" clearable placeholder="请填写组织机构名称" />
			</el-form-item>
			<el-form-item prop="name">
				<el-input v-model="queryParams.orgCode" clearable placeholder="请填写组织机构代码" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="onSearch">搜索</el-button>
				<el-button plain @click="resetForm('queryForm')">重置</el-button>
			</el-form-item>
		</el-form>
		<el-table ref="multipleTable" class="synchronous-table" :data="tableData" size="small" row-key="orgId" tooltip-effect="dark" :max-height="400" @selection-change="handleSelectionChange">
			<el-table-column type="selection" width="55" reserve-selection />
			<el-table-column prop="orgName" align="left" label="组织机构" />
			<el-table-column prop="orgCode" align="left" label="代码" />
		</el-table>
		<div class="m-t-15 text-right">
			<el-pagination :current-page="pageParams.current" :page-sizes="pageSize" :page-size="pageParams.size" background layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
		</div>
		<template #footer>
			<el-button type="primary" @click="cancel">取消</el-button>
			<el-button type="primary" @click="submitForm('form')">确定</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useMessage , useMessageBox } from '/@/hooks/message';

import { getOrganizationPage, updateProcessDefinition } from '/@/api/flow/built-in-processes/index.ts'

const synchronous = ref(false)
const tableData = ref([])
const pageSize = ref([10, 20, 30, 50])
const total = ref(0)
const queryParams = reactive({
  orgName: '',
  orgCode: ''
})
const pageParams = reactive({
  current: 1,
  size: 20
})
const multipleSelection = ref([])
const defineKey = ref('')

const cancel = () => {
  synchronous.value = false
  multipleSelection.value = []
  defineKey.value = ''
}

const onSearch = () => {
  pageParams.current = 1
  getList()
}

const resetForm = (ref) => {
  ref.resetFields()
  queryParams.orgName = ''
  queryParams.orgCode = ''
  getList()
}

const getList = (title) => {
  getOrganizationPage({ ...queryParams, ...pageParams }).then(({ data }) => {
    tableData.value = data.records
    total.value = data.total
    synchronous.value = true
  })
}

const sync = (initData) => {
  if (initData) {
    defineKey.value = initData.key || initData.flowId
  }
  getList()
}

const submitForm = () => {
  useMessageBox().confirm(
    multipleSelection.value.length ? '是否同步选中的企业' : '是否同步到所有企业',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    updateProcess()
  }).catch(() => {})
}

const updateProcess = () => {
  const orgCodes = multipleSelection.value.map(item => item.orgCode).join(',')
  const formData = new FormData()
  formData.append('defineKey', defineKey.value)
  formData.append('orgCodes', orgCodes)
  updateProcessDefinition(formData).then(res => {
    if (res.code === 200) {
      useMessage().success('添加成功')
      synchronous.value = false
      multipleSelection.value = []
      defineKey.value = ''
      emit('refresh')
    }
  }).catch(() => {})
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

const toggleSelection = (rows) => {
  // 这里假设你有一个名为 multipleTable 的 ref
  const multipleTable = ref(null)
  multipleTable.value.clearSelection()
  if (rows) {
    rows.forEach(row => {
      multipleTable.value.toggleRowSelection(row)
    })
  } else {
    multipleTable.value.clearSelection()
  }
}

const handleCurrentChange = (current) => {
  pageParams.current = current
  getList()
}

const handleSizeChange = (size) => {
  pageParams.size = size
  getList()
}

const emit = defineEmits(['refresh'])
const openDialog = ()=>{
	synchronous.value = true
}
defineExpose({sync})

</script>
<style scoped></style>
