<template>
	<div>
		<el-dialog :title="title" v-model="visible" width="800" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="cancel">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
				<el-row :gutter="10" type="flex"  style="flex-wrap: wrap">
					<el-col :span="24" class="mb-5">
						<el-form-item label="选择身份" prop="identityCode">
							<el-select v-model="form.identityCode" placeholder="请选择身份" style="width: 100%" @change="identityChange">
								<el-option v-for="item in identityOptions" :key="item.code" :label="item.name" :value="item.code" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item :key="formKey" label="选择业务" prop="busProcshipIdList">
							<el-select v-model="form.busProcshipIdList" placeholder="请选择业务" multiple style="width: 100%">
								<el-option v-for="item in procshipList" :key="item.id" :label="item.businessName" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
      <el-button type="primary" @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
		</el-dialog>
	</div>
</template>
<script setup>
import { ref, reactive} from 'vue'
import { identityList, busProcshipList, listByIdentity, saveIdentityCodes } from '/@/api/flow/built-in-business/index.ts'
import { useMessage } from '/@/hooks/message';
const visible = ref(false)
const title = ref('身份绑定');
const emit = defineEmits(['identityCallback']);
const formRef = ref();
const form = ref({
  identityCode: '',
  busProcshipIdList: []
})
const rules = reactive({
  identityCode: [{ required: true, message: '请选择身份', trigger: ['blur'] }]
})
const identityOptions = ref([]) // 身份列表
const procshipList = ref([]) // 业务列表
const formKey = ref(Math.random())
// 获取身份列表
const getIdentityList = async () => {
  const { code, data } = await identityList()
  if (Number(code) === 200) {
    identityOptions.value = data || []
  }
}

// 获取业务列表
const getBusProcshipList = async () => {
  const { code, data } = await busProcshipList()
  if (Number(code) === 200) {
    procshipList.value = data || []
  }
}

// 身份值选中
const identityChange = async (val) => {
  formKey.value = Math.random()
  const params = { identityCode: val }
  const { code, data } = await listByIdentity(params)
  if (Number(code) === 200) {
    form.value.busProcshipIdList = (data || []).map(item => item.id)
  }
}

// 提交
const submitForm = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      saveIdentityCodes(form.value).then(res => {
        if (res.code === 200) {
        	useMessage().success('绑定成功');
          reset();
          visible.value = false;
          emit('identityCallback');
        }
      }).catch(err=>{
        useMessage().error(err.msg);
      })
    } else {
      console.log('error submit!!');
      return false;
    }
  });
};

// 更新
const reset = () => {
  form.value.identityCode = ''
  form.value.busProcshipIdList = []
	formRef.value.clearValidate()
}

const cancel = () => {
  visible.value = false
  reset()
}
// 打开弹窗
const openDialog =  () => {
		visible.value = true;
		getIdentityList()
  	getBusProcshipList()



	
};
defineExpose({ openDialog });
</script>
<style lang="scss" scoped></style>
