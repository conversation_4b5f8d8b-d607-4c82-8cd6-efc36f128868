<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view no-t-l-radius">
			<co-search ref="searchRef" inline :model="searchData" label-position="left" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table border ref="dsTableRef" :config="tableConfig" :header="tableHeader" single-mode="icon-hook" @loaded="onTableLoad" @operation="onOperation" align="left"> </co-table>
			<formData ref="addDialogRef" @flowCallback="onRefresh"></formData>
			<addIdentity ref="addIdentityRef" @identityCallback="onRefresh"></addIdentity>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';

const formData = defineAsyncComponent(() => import('./component/formData.vue'));
const addIdentity = defineAsyncComponent(() => import('./component/addStatus.vue'));
import { getBusProcshipPage, deletebusProcship } from '/@/api/flow/built-in-business/index.ts';
const searchConfig = reactive({
	items: [
		{
			prop: 'businessName',
			type: 'input',

			attrs: {
				placeholder: '请填写业务名称',
				label: '业务名称',
				clearable: true,
			},
		},
		{
			prop: 'businessKey',
			type: 'input',
			attrs: {
				placeholder: '请填写业务key',
				label: '业务key',
				clearable: true,
			},
		},
	],
});
const dicData = ref();
// 表格配置
const tableConfig = ref({
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getBusProcshipPage, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		params: {}, // - 可选，请求参数 如果和搜索字段相同，会覆盖搜索字段
	},
});
const tableHeader = [
	{
		prop: 'businessName',
		label: '业务名称',
	},
	{
		prop: 'businessKey',
		label: '业务key',
	},
	{
		prop: 'processName',
		label: '流程定义名称',
	},
	{
		prop: 'routeUrl',
		label: '详细路由地址',
	},
];
const addDialogRef = ref();
const addIdentityRef = ref();

const searchData = reactive({
	businessName: '',
	businessKey: '',
});
let onSearch = null;
// table加载完成回调
function onTableLoad({ getDataList }) {
	onSearch = getDataList;
}
function onRefresh() {
	onSearch();
}
const onSearchHandle = (data) => {
	onSearch({ params: data });
};

// 监听表格搜索操作
function onOperation({ row, field }) {
	switch (field) {
		case 'add':
			addDialogRef.value.openDialog('add', null);
			break;
		case 'Identity':
			addIdentityRef.value.openDialog();
			break;
		case 'edit':
			addDialogRef.value.openDialog('edit', row);
			break;
		case 'delete':
			useMessageBox()
				.confirm('是否确认删除此数据?')
				.then((res) => {
					deletebusProcship({ id: row.id })
						.then((res) => {
							onRefresh();
							useMessage().success(res.msg || '操作成功');
						})
						.catch((err) => {
							useMessage().error(err.msg);
						});
				});
			break;
	}
}
</script>

<style lang="scss" scoped>
.pagination {
	margin: 15px;
	text-align: right;
}
</style>
