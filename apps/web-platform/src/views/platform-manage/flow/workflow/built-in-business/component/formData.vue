<template>
  <el-dialog :title="title" v-model="visible" width="800" append-to-body :close-on-click-modal="false"
    :close-on-press-escape="false" @close="cancel">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-suffix="：">
      <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
        <el-col :span="24" class="mb-5">
          <el-form-item label="业务名称" prop="businessName">
            <el-input v-model="form.businessName" placeholder="请填写业务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mb-5">
          <el-form-item label="业务Key" prop="businessKey">
            <el-input v-model="form.businessKey" placeholder="请填写业务Key" />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mb-5">
          <el-form-item label="流程定义列表" prop="processDefinitionKey">
            <el-select v-model="form.processDefinitionKey" filterable placeholder="请选择流程定义列表">
              <el-option v-for="item in options" :key="item.key" :label="item.name" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mb-5">
          <el-form-item label="详情路由地址" prop="routeUrl">
            <el-input v-model="form.routeUrl" placeholder="请填写详情路由地址" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="审批人类型" prop="assigneeTypesStr">
            <el-select v-model="form.assigneeTypesStr" filterable multiple value-key="code" placeholder="请选择审批人类型"
              class="select-style">
              <el-option v-for="item in assigneeList" :key="item.code" :label="item.name" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button  @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>

  </el-dialog>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/message';
import { addBusProcship, editBusProcship, getactProcessList,getListAssigneeTypes } from '/@/api/flow/built-in-business/index.ts'

const visible = ref(false);
const title = ref('添加审批组');
const formRef = ref();
const flowId = ref('');

const form = ref({
  businessName: '',
  businessKey: '',
  routeUrl: '',
  processDefinitionKey: '',
  assigneeTypesStr: []
});
const rules = reactive({
  businessName: [{ required: true, message: '请填写业务名称', trigger: ['blur', ] }],
  businessKey: [{ required: true, message: '请填写业务Key', trigger: ['blur', ] }],
  routeUrl: [{ required: true, message: '请填写详情路由地址', trigger: ['blur', ] }],
  processDefinitionKey: [{ required: true, message: '请选择流程定义列表', trigger: ['blur',] }],
  assigneeTypesStr: [{ required: false, message: '请选择审批人类型', trigger: ['blur',] }]
});
const options = ref([]);
const assigneeList = ref([]);
const emit = defineEmits(['flowCallback']);
const actProcessList = () => {
  getactProcessList()
    .then(({ data }) => {
      options.value = data.map(item => ({
        name: item.name,
        key: item.key
      }));
    })
    .catch(() => {});
};

const getAssigneeList = () => {
  getListAssigneeTypes().then(res => {
    assigneeList.value = res.data;
  });
};

const add = () => {
  visible.value = true;
  title.value = '添加流程';
  actProcessList();
  getAssigneeList();
};

const edit = (data) => {
  form.value.businessName = data.businessName;
  form.value.businessKey = data.businessKey;
  form.value.routeUrl = data.routeUrl;
  form.value.processDefinitionKey = data.processDefinitionKey;
  form.value.assigneeTypesStr = data.assigneeTypes ? JSON.parse(data.assigneeTypes) : [];
  visible.value = true;
  title.value = '编辑流程';
  actProcessList();
  getAssigneeList();
};

const cancel = () => {
  visible.value = false;
  reset();
};

const reset = () => {
  form.value.businessName = '';
  form.value.businessKey = '';
  form.value.routeUrl = '';
  form.value.processDefinitionKey = '';
  form.value.assigneeTypesStr = [];
  formRef.value.clearValidate()

};

const submitForm = (formName) => {
  

  formRef.value?.validate(valid => {
    if (valid) {
      let api = null;
      if (!flowId.value) {
        api = addBusProcship;
      } else {
        form.value.id = flowId.value;
        api = editBusProcship;
      }
      

      form.value.assigneeTypes = JSON.stringify(form.value.assigneeTypesStr);
      api(form.value).then(res => {
        if (res.code === 200) {
        	useMessage().success(flowId.value ? '修改成功' : '添加成功');
          reset();
          visible.value = false;
          emit('flowCallback', { type: 'submit' });
        }
      }).catch(err=>{
        useMessage().error(err.msg);
      })
    } else {
      console.log('error submit!!');
      return false;
    }
  });
};

// 打开弹窗
const openDialog =  (type,row) => {
	if(type === 'edit'){
		title.value = '修改'
		flowId.value = row.id
		edit(row)
	}else{

		title.value = '添加流程'
     actProcessList();
     getAssigneeList();

	}

	visible.value = true;

	
};
defineExpose({ openDialog });

</script>
<style scoped>
.el-select {
  width: 100%;
}
</style>
