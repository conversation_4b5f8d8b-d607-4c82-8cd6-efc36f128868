<template>
  <el-dialog :title="reactiveData.title" v-model="reactiveData.visible" width="800" :close-on-click-modal="false" :close-on-press-escape="false"  destroy-on-close>
    <el-form ref="coFormRef" :model="formData" :rules="rules" label-width="150px">
      <el-form-item v-if="isParent" label="所属分类:" prop="parentId">
        <el-cascader 	v-model="formData.parentId" :disabled="reactiveData.disabled" :options="reactiveData.options"  clearable :props="{ checkStrictly: true }" 	placeholder="请选择所属分类"/>
      </el-form-item>
      <el-form-item label="分类名称：" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" :maxlength="50"  />
      </el-form-item>
      <el-form-item v-if="isCode" label="分类编号：" prop="code">
        <el-input v-model="formData.code" placeholder="请输入分类编号" :maxlength="50"  />
      </el-form-item>
      <el-form-item label="排序：" prop="sort">
        <el-input v-model.number="formData.sort" placeholder="请输入排序值，数字越小越靠前" :maxlength="50"  />
      </el-form-item>
      <el-form-item label="备注：" prop="remark">
        <el-input v-model="formData.remark" type="text" placeholder="请输入备注" :maxlength="50" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="reactiveData.visible = false">取消</el-button>
      <el-button type="primary" :loading="reactiveData.submitLoading" @click="onSubmit">确定</el-button>
  </template>
	</el-dialog>
</template>
<script setup lang="ts">
import { simpleTree, materialClassifyAdd } from '/@/api/platform-manage/supplier-level'
import { useMessage } from '/@/hooks/message';
const props = defineProps({
  getList: {
    type: Function,
    required: true
  } 
});
const reactiveData = reactive<{
  submitLoading: boolean;
  visible: boolean;
  setting: string;
  disabled: boolean,
  options?: any[]
  isCode?: Boolean,
  title?:string,
  }>({
  submitLoading: false, //按钮控制
  visible: false, //弹框控制
  setting: 'add', //弹框提示设置
  disabled: false,
  options: [],// 选择一级分
  isCode: true, //false显示分类true隐藏分类
  title:''
})
//表单配置
const formList = [
  { id: 'parentId', name: '所属分类:', type: 'input', required: true, slot: 'i-parentId', relation: { id: 'isParent', val: '1' },},
  { id: 'name', name: '分类名称:', type: 'input', required: true },
  { id: 'code', name: '分类编号:', type: 'input', required: true, relation: { id: 'isCode', val: '1' }, },
  { id: 'sort', name: '排序:', type: 'input', required: true},
  { id: 'remark', name: '备注:', type: 'textarea' },
]
//表单Data
const formData = ref<{
  parentId?: String|Number,
  name?:String,
  code?: string,
  sort?: any,
  remark?: string,
}>({
  parentId:'',
  name: '',
  code:'',
  sort: undefined,
  remark: '',
  
})
//表单校验
const rules =  {
  parentId:[{required: true,message: '请选择所属分类'}],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()\-_=+{};:,<.>]{0,20}$/, message: '输入格式为字母、数字、字符，长度不超过20个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编号', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()\-_=+{};:,<.>]{0,20}$/, message: '输入格式为字母、数字、字符，长度不超过20个字符', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值，数字越小越靠前', },
    { pattern:/^[1-9]\d*$/, message: '必须为正整数' }
  ]
};

//所属分类是否隐藏
const isParent =  computed(() => {
      return reactiveData.setting == 'addChild' || (reactiveData.setting == 'edit' && formData.value.parentId != 0)
});

//分类编号是否隐藏
const isCode = computed(() => {
      return reactiveData.setting == 'add' || (reactiveData.setting == 'edit' && formData.value.parentId == 0)
});

 //打开dialog
const openDialog = (type: string, row: any) => {
     
     reactiveData.setting = type
    if (type == 'add') {
       reactiveData.title = '新增一级分类'
       formData.value = {parentId:0  };
    } else if (type == 'edit') {
        reactiveData.title = '修改分类'
        if(row.parentId != 0)reactiveData.disabled = true
        formData.value = row
    } else if (type == 'addChild') {
        reactiveData.title = '新增子级专业'
        formData.value = {parentId:row.id}
    }
     coFormRef.value?.resetFields();
    reactiveData.visible = true
    treeList()
} 

onMounted(() => {
  
})
//获取所属分类
const treeList = () =>{
   simpleTree().then(res => {
    if (res.code == 200) {
      reactiveData.options = res.data
    }
  })
}
// 表单数据校验
const coFormRef = ref();
//表单提交
let onSubmit = async() => {
  const valid = await coFormRef.value.validate().catch(() => {});
  if (!valid) return false;
  reactiveData.submitLoading = true
  materialClassifyAdd(formData.value).then(res => {
    reactiveData.visible = false
    props.getList()
    reactiveData.submitLoading = false
    useMessage().success('操作成功');
  }).catch(error => {
    useMessage().error(error.msg);
    reactiveData.submitLoading = false
  })
}

defineExpose({openDialog})
   
</script>