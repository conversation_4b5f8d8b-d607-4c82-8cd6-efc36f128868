<template>
	<div class="layout-padding w100">
		<div class="layout-padding-auto layout-padding-view">
			<co-table :config="tableConfig" :header="tableHeader" @loaded="tableLoaded" @operation="onOperation" row-key="id" :tree-props="{ hasChildren: true }" align="left"> </co-table>
		</div>
		<editCom ref="editComRef" :get-list="pageList"></editCom>
	</div>
</template>

<script setup lang="ts">
import { supplierIndustryClassify, updateIzKako, supplierClassifyDel } from '/@/api/platform-manage/supplier-level';
import { useMessage, useMessageBox } from '/@/hooks/message';
const editCom = defineAsyncComponent(() => import('./edit.vue'));
const tableHeader = [
	{ label: '分类名称', prop: 'name' },
	{ label: '分类编号', prop: 'code' },
	{ label: '排序', prop: 'sort' },
	{ label: '备注', prop: 'remark' },
	{ label: '创建时间', prop: 'createTime' },
	{
		label: '是否末级节点',
		prop: 'izKako',
		type: 'switch',
		attrs: {
			'active-value': 1,
			'inactive-value': 0,
			'before-change': (row: any) => {
				return handleStatusChange(row);
			},
		},
	},
];

var onSearch: any = null;
const tableLoaded = ({ getDataList }: any) => {
	onSearch = getDataList;
};
const editComRef = ref();

//Table操作列回调
const onOperation = ({ field, row, prop }: any) => {
	const set = field ? field : prop;
	switch (set) {
		case 'add':
			editComRef.value.openDialog('add', {});
			break;
		case 'edit':
			editComRef.value.openDialog('edit', row);
			break;
		case 'addChild':
			editComRef.value.openDialog('addChild', row);
			break;
		case 'remove':
			supplierClassifyDelFu(row.id);
			break;
		case 'izKako':
			// handleStatusChange(row)
			break;
	}
};

const tableConfig = {
	request: {
		apiName: supplierIndustryClassify,
	},
	pagination: false,
	operation: {
		width: '350px',
	},
};

//获取列表
const pageList = () => {
	onSearch();
};
//删除列表
const supplierClassifyDelFu = async (id: string) => {
	await useMessageBox().confirm('此操作将永久删除, 是否继续?');
	supplierClassifyDel(id)
		.then((res) => {
			onSearch();
			useMessage().success('操作成功');
		})
		.catch((error) => {
			useMessage().error(error.msg);
		});
};
//判断是否是末节点
const handleStatusChange = async (row: any) => {
	const text = row.izKako == 1 ? '确认要改为末级节点吗？' : '确认要改为非末级节点吗？';
	const confirmRes = await useMessageBox().confirm(text);
	if (!confirmRes) return false;
	const { code, msg } = await updateIzKako({ id: row.id, izKako: row.izKako < 1 ? 1 : 0 }).catch((err) => err);
	if (code === 200) {
		onSearch();
	} else {
		useMessage().error(msg);
	}
	return code === 200;
};
</script>

<style></style>
