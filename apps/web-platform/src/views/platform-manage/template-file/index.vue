<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-table :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" @loaded="onTableLoad" @operation="onOperation" align="left">
				<template #izEnable="{ row }">
					<el-switch v-model="row.izEnable" :active-value="true" :inactive-value="false" @change="changeStatus(row)" />
				</template>
				<template #fileids="{ row }">
					<el-button text type="primary" icon="View" @click="preview(row.fileids)">预览</el-button>
					<el-button text type="primary" icon="Download" @click="download(row.fileids)">下载</el-button>
				</template>
			</co-table>
			<FormData ref="formRef" @refresh="getList()" />
			<!-- 预览 -->
			<co-preview v-if="previewUrl" v-model="previewUrl" width="80%" :layer="true" />
		</div>
	</div>
</template>

<script setup>
import { delTemplate, updateState } from '/@/api/platform-manage/operation/template-file';
import { getFiles, downLoadFile2 } from '/@/api/common/upload';
import FormData from './components/form-data.vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import datas from './index.data';

const pageConfig = reactive({
	tableConfig: datas.tableConfig,
	tableHeader: datas.tableHeader,
});

const previewUrl = ref('');
const formRef = ref(null);

let getList = null;

function onTableLoad({ getDataList }) {
	getList = getDataList;
}

function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	switch (field) {
		case 'add':
			handleAdd();
			break;
		case 'edit':
			edit(row);
			break;
		case 'del':
			del(row);
			break;
	}
}

function handleAdd() {
	formRef.value.add();
}
function edit(row) {
	formRef.value.edit(row);
}

function del(row) {
	useMessageBox()
		.confirm('此操作将永久删除, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {
			delTemplate([row.id])
				.then((res) => {
					if (res.code === 200) {
						useMessage().success('删除成功');
						getList();
					}
				})
				.catch((err) => {
					useMessage().error(err.msg || '删除失败');
				});
		})
		.catch(() => {
			useMessage().info('已取消删除');
		});
}

function changeStatus(row) {
	updateState(row)
		.then((res) => {
			if (res.code === 200) {
				getList();
				useMessage().success('状态更新成功');
			}
		})
		.catch(() => {
			useMessage().error('状态更新失败');
			row.izEnable = !row.izEnable;
		});
}
// 预览
function preview(fileids) {
	getFiles([fileids]).then((res) => {
		previewUrl.value = res.data[0].fileUrl;
	});
}
// 下载
function download(fileids) {
	downLoadFile2(fileids).catch(() => {
		useMessage().error('下载失败');
	});
}
</script>
<style lang="scss" scoped>
.pagination {
	margin: 15px;
	text-align: right;
}
</style>
