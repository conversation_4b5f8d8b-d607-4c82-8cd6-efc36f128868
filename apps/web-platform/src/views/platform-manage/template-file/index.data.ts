import { getTemplateFile } from '../../../api/platform-manage/operation/template-file';
export default {
	tableConfig: {
		request: {
			apiName: getTemplateFile, // 接口方法 一般从api文件中导入，或当前页面的某个方法
		},
		operation: {
			fixed: 'right',
			width: 320,
		},
	},
	tableHeader: [
		{ label: '序号', type: 'index', width: 60, align: 'center' },
		{ prop: 'templateDescription', label: '模板说明' },
		{ prop: 'izEnable', label: '状态' },
		{ prop: 'fileType', label: '文件格式' },
		{ prop: 'fileids', label: '附件' },
		{ prop: 'lastUpdateTime', label: '更新时间' },
	],
};
