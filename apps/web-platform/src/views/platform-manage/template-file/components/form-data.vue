<template>
	<el-dialog :title="pageConfig.title" v-model="pageConfig.open" width="600px" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body @close="cancel">
		<el-form ref="formRef" :model="formData" :rules="pageConfig.rules" label-width="100px">
			<el-row :gutter="10" type="flex" style="flex-wrap: wrap">
				<el-col :span="24">
					<el-form-item label="模板说明：" prop="templateDescription">
						<el-input v-model="formData.templateDescription" type="textarea" rows="4" placeholder="请填写模板说明" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="模板key：" prop="templateKey" class="mt-5">
						<el-input v-model="formData.templateKey" placeholder="请填写value值" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="模板状态：" prop="izEnable" class="mt-5">
						<el-switch v-model="formData.izEnable" :active-value="true" :inactive-value="false" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="附件：" prop="fileids" class="mt-5">
						<uploadFile v-model:value="formData.fileids" :data="{ pvcId: systemPvcid }" :limit="1" @change="fileChange" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template v-slot:footer>
			<div class="dialog-footer">
				<el-button size="small" @click="pageConfig.open = false">取 消</el-button>
				<el-button type="primary" size="small" @click="submitForm">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import { addTemplate, editTemplate } from '/@/api/platform-manage/operation/template-file';
import uploadFile from '/@/components/pro-upload/upload-file.vue';
import { useMessage } from '/@/hooks/message';
import { useEnvStore } from '/@/stores/env';
const formRef = ref(null);

const pageConfig = reactive({
	title: '添加模板',
	open: false,
	rules: {
		templateDescription: [{ required: true, message: '模板说明不能为空', trigger: 'blur' }],
		izEnable: [{ required: true, message: '请选择模板状态', trigger: 'change' }],
		fileids: [{ required: true, message: '请上传附件', trigger: 'input' }],
	},
	resIdsArr: [],
	showResource: false,
});

const formData = ref({
	templateDescription: '',
	templateKey: '',
	izEnable: true,
	fileids: '', // 传给后台的数据
	fileSize: '', // 文件大小
	fileType: '', // 文件类型
});
// 获取pvcid
const systemPvcid = useEnvStore().getEnv['VUE_APP_FILE_CLOUD_SERVE_SYSTEM_PVCID'];
function add() {
	pageConfig.open = true;
	pageConfig.title = '添加模板';
}
async function edit(row) {
	pageConfig.open = true;
	pageConfig.title = '编辑模板';
	nextTick(() => {
		formData.value = JSON.parse(JSON.stringify(row));
	});
}

defineExpose({
	add,
	edit,
});

function cancel() {
	reset();
	pageConfig.open = false;
}

function reset() {
	pageConfig.resIdsArr = [];
	pageConfig.showResource = false;
	formData.value = { templateDescription: '', templateKey: '', izEnable: true, fileids: '', fileSize: '', fileType: '' };
	formRef.value.resetFields();
}

const emit = defineEmits(['refresh']);

function submitForm() {
	formRef.value.validate((valid) => {
		if (valid) {
			let api = null;
			if (!formData.value.id) {
				api = addTemplate;
			} else {
				api = editTemplate;
			}
			api(formData.value)
				.then((res) => {
					if (res.code === 200) {
						useMessage().success(formData.value.id ? '修改成功' : '添加成功');
						pageConfig.open = false;
						emit('refresh');
					}
				})
				.catch(({ msg }) => {
					useMessage().error(msg);
				});
		} else {
			console.log('error submit!!');
			return false;
		}
	});
}

function fileChange(val) {
	formData.value.fileSize = val[0].size;
	formData.value.fileType = val[0].type;
}
</script>
