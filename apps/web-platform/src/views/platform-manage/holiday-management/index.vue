<template>
	<div class="layout-padding" id="holiday-management-index">
		<div class="layout-padding-auto layout-padding-view">
			<div class="color-danger">提示：自定义时间优先级高于工作日</div>
			<div class="pro-title">工作日设置</div>
			<el-form ref="form" :inline="true" :model="formInline">
				<el-form-item prop="year">
					<el-date-picker v-model="formInline.year" type="year" value-format="YYYY" format="YYYY" placeholder="选择年" @change="getWorkDay" />
				</el-form-item>
				<el-form-item prop="workDayRule">
					<el-checkbox-group v-model="formInline.workDayRule">
						<el-checkbox label="1">周一</el-checkbox>
						<el-checkbox label="2">周二</el-checkbox>
						<el-checkbox label="3">周三</el-checkbox>
						<el-checkbox label="4">周四</el-checkbox>
						<el-checkbox label="5">周五</el-checkbox>
						<el-checkbox label="6">周六</el-checkbox>
						<el-checkbox label="7">周日</el-checkbox>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label-width="50px" class="ml15">
					<el-button type="primary" @click="submitForm()">保存</el-button>
				</el-form-item>
			</el-form>
			<div class="pro-title">自定义时间</div>
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" @loaded="onTableLoad" @dicLoaded="onDicLoaded" @operation="onOperation" @selectionChange="onSelectionChange" align="left">
				<template #dayType="{ row }">
					<div>{{ row.dayType == 1 ? '休息日' : '工作日' }}</div>
				</template>
			</co-table>
		</div>
		<edit
			ref="editRef"
			:dialog="isDialog"
			:type="type"
			:currentRow="currentRow"
			@popupClose="
				isDialog = false;
				currentRowId = '';
			"
			@popupCallback="getList"
		></edit>
	</div>
</template>
<script setup>
import edit from './edit.vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getDtoByYear, saveWorkDayRule, getWorkDayZdy, delWorkDayZdy } from '/@/api/platform-manage/holiday-management';
// 获取列表接口变量
let getList = null;
// 待删除项集合
let delArr = [];
// 是否开启弹框
let isDialog = ref(false);
// 当前点击的id
let currentRow = ref({});
let type = ref(null);
const editRef = ref(null);
// ------------------工作日设置
// 工作日设置数据
const formInline = ref({
	id: null,
	year: '',
	workDayRule: [],
});
// 获取工作日
function getWorkDay(e) {
	if (!e) {
		formInline.value.workDayRule = [];
		return;
	}
	// 根据年份查询工作日
	getDtoByYear({ year: e }).then(({ data }) => {
		if (data) {
			formInline.value.id = data.id;
			formInline.value.year = data.year.toString();
			formInline.value.workDayRule = data.workDayRule.split(',');
		}
	});
}
// 保存工作日设置
function submitForm() {
	if (!formInline.value.year) {
		return useMessage().warning('请选择年份');
	}
	if (formInline.value.workDayRule.length <= 0) {
		return useMessage().warning('请选择工作日');
	}
	const data = {
		id: formInline.value.id,
		workDayRule: formInline.value.workDayRule.toString(),
		year: formInline.value.year,
	};
	saveWorkDayRule(data).then(() => {
		useMessage().success('保存成功');
	});
}
// ----------------自定义时间设置
// 表格配置数据
const tableConfig = ref({
	operation: {
		fixed: 'right',
		width: 240,
	},
	// pagination:{
	//   current:1,
	//   size:10,
	//   total:0
	// },
	request: {
		apiName: getWorkDayZdy, // 接口方法 一般从api文件中导入，或当前页面的某个方法
	},
});
// 表头配置
const tableHeader = ref([{ type: 'selection' }, { prop: 'dateDay', label: '日期' }, { prop: 'dayType', label: '状态' }]);
// 表格操作列及表单
function onOperation({ field, row, prop }) {
	field = field ? field : prop;
	switch (field) {
		case 'add':
			isDialog.value = true;
			type.value = 'add';
			break;
		case 'edit':
			// router.push({ path: '/platform-manage/venue-reservation/edit',query:{ id:row.id } });
			isDialog.value = true;
			currentRow.value = row;
			type.value = 'edit';
			break;
		case 'del': // 表格删除
			useMessageBox()
				.confirm('确认要删除此项数据吗？', '提示', {
					type: 'warning',
				})
				.then(() => {
					delWorkDayZdy([row.id]).then(() => {
						useMessage().success('操作成功');
						getList();
					});
				});
			break;
		case 'remove': // 批量删除
			if (delArr.length < 1) return useMessage().warning('请选择至少一项进行删除！');
			useMessageBox()
				.confirm('确认要删除选中的数据吗？', '提示', {
					type: 'warning',
				})
				.then(() => {
					delWorkDayZdy(delArr).then(() => {
						useMessage().success('操作成功');
						getList();
					});
				});
			break;
	}
}
// table加载完成回调
function onTableLoad({ getDataList }) {
	getList = getDataList;
}
// 监听多选框回调
function onSelectionChange(data) {
	delArr = data.map((r) => {
		return r.id;
	});
}
</script>
<style lang="scss">
#holiday-management-index {
	.pro-title {
		font-size: 18px;
		margin: 15px 0;
		padding: 5px 15px;
		border-bottom: 1px solid #eee;
		position: relative;
		font-weight: 800;
		&::before {
			content: '';
			display: inline-block;
			height: 15px;
			width: 4px;
			background-color: var(--el-color-primary);
			position: absolute;
			left: 0;
			top: 50%;
			margin-top: -9px;
			border-radius: 2px;
		}
	}
}
</style>
