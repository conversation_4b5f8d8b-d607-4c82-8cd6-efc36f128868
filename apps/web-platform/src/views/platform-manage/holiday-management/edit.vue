<template>
  <Popup ref="popupRef" :title="type == 'edit' ? '修改自定义时间':'新增自定义时间'" width="500" @close="close" @confirm="confirm" :async="true">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" label-width="100px">
			<el-row :gutter="10" type="flex" style="flex-wrap: wrap">
				<el-col v-if="formData.id" :span="24">
					<el-form-item label="选择日期：" prop="dateDay">
						<el-date-picker key="dateDay" v-model="formData.dateDay" type="date" value-format="YYYY-MM-DD" readonly />
					</el-form-item>
				</el-col>
				<el-col v-else :span="24">
					<el-form-item label="选择日期：" prop="dateArr">
						<el-date-picker key="dateArr" v-model="formData.dateArr" type="daterange" value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mt15">
					<el-form-item label="设置状态：" prop="dayType">
						<el-radio-group v-model="formData.dayType">
							<el-radio :label="1">休息日</el-radio>
							<el-radio :label="0">工作日</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
  </Popup>
</template>
<script setup>
  import Popup from '/@/components/Popup/index.vue';
  import { addWorkDayZdy, editWorkDayZdy } from '/@/api/platform-manage/holiday-management'
  import { useMessage,useMessageBox } from '/@/hooks/message'
  // 弹框组件实例
  const popupRef = ref(null)
  // 表单实例
  const ruleFormRef = ref(null)
  const emits = defineEmits(['popupClose','popupCallback']);
  const props = defineProps({
    dialog:{
      type:String,
      default:false
    },
    currentRow:{
      type:Object,
      default:{}
    },
    type:{
      type:String,
      default:''
    }
  })
  // 表单数据
	const formData = ref({
		dateDay:'',
    dateArr:[],
    dayType:1
	});
  //表单验证
  const rules = reactive({
    dateArr: [{ required: true, message: '请选择日期', trigger: 'change' }],
		dayType: [{ required: true, message: '请选则状态', trigger: 'change' }]
  })
  // 数据监听弹框显示隐藏
  watch(() => props.dialog,(val) => {
    if(val){
      // 获取已绑定的摄像头数据列表,并回显
      popupRef.value.open()
    }
  })
  // 数据监听弹框显示隐藏
  watch(() => props.currentRow,(val) => {
    if(val){
      formData.value = JSON.parse(JSON.stringify(val))
    }
  },{deep:true})
    // 弹框确定方法
   async function confirm(){
    const valid = await ruleFormRef.value.validate().catch(() => {});
    if(!valid)return
    if(formData.value.id){
      editWorkDayZdy(formData.value).then(() => {
        useMessage().success('保存成功')
        emits('popupCallback');
        popupRef.value.close()
      })
    }else{
      addWorkDayZdy({ dayType:formData.value.dayType,startDate:formData.value.dateArr[0],endDate:formData.value.dateArr[1] }).then(() => {
        useMessage().success('保存成功')
        emits('popupCallback');
        popupRef.value.close()
      })
    }
  }
  // 关闭弹框回调
  const close = () => {
    emits('popupClose', false);
    // 初始化数据
    formData.value = {
      dateDay:'',
      dateArr:[],
      dayType:1
    }
  }
</script>