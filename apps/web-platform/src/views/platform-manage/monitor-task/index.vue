<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view btn-align-center scorll">
			<co-search ref="searchRef" inline label-position="left" :dic="dicData" :config="searchConfig" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" @loaded="onTableLoad" @dicLoaded="onDicLoaded" single-mode="icon-hook" @operation="onOperation" align="left"> </co-table>
		</div>
	</div>
</template>
<script setup lang="ts">
import { tableHeader, searchConfig } from './data.js';
import { dictApi } from '/@/api/dict/index';
import { getList, getStart, getStop } from '/@/api/platform-manage/monitor-task';
import { useMessageBox } from '/@/hooks/message.js';
const router = useRouter();
// 表格配置
const tableConfig = ref({
	dic: {
		taskStatus: { value: dictApi.monitoringMent },
		taskType: { value: dictApi.monitoringMentType },
	},
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
	},
});
// 搜索组件中对应项所需要的字典数据，来源于表格组件字段数据加载完成后的回调
const dicData = ref({});

//table回调列表方法
var onSearch: any = null;
//表格操作
const onOperation = async ({ field, row }: any) => {
	switch (field) {
		case 'toView':
			router.push({ path: '/platform-manage/monitor-task/toView', query: { id: row.id } });
			break;
		case 'start':
			await useMessageBox().confirm('此操作将开始, 是否继续?');
			getStart(row.id).then(({ msg }) => {
				onSearch();
			});
			break;
		case 'stop':
			await useMessageBox().confirm('此操作将停止, 是否继续?');
			getStop(row.id).then(({ msg }) => {
				onSearch();
			});
			break;
	}
};

//搜索
const onSearchHandle = (data: Object) => {
	const params = { ...data };
	onSearch({ params });
};

// 字典加载完毕
const onDicLoaded = (data: any) => {
	dicData.value = data;
};
//table加载完
const onTableLoad = ({ getDataList }: any) => {
	onSearch = getDataList;
};
</script>
