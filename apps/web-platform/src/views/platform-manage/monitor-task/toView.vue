<template>
	<div class="layout-padding w100">
		<pro-back-pre title="监控任务详情" />
		<div class="layout-padding-auto layout-padding-view btn-align-center scorll">
			<div class="mb20">转播任务</div>
			<co-table :data="broadcastList" :config="{ pagination: false }" :header="tableHeader" @operation="onOperation" row-key="id" align="left">
				<template #statu="{ row }">
					{{ reactiveData.statusForm[row.status] }}
				</template>
			</co-table>
			<div class="mb20">录像任务</div>
			<co-table :data="recordList" id="recordTable" :config="{ pagination: false }" :header="recordTableHeader" @operation="onOperation" row-key="recordTable" align="left">
				<template #status="{ row }">
					{{ reactiveData.statusForm[row.status] }}
				</template>
			</co-table>
		</div>
		<Popup ref="popupRef" title="任务播放" width="900" :buttomBtn="false" @close="close">
			<videoPlay ref="videoPlayRef" :src="playUrl" />
		</Popup>
	</div>
</template>
<script setup>
import videoPlay from '/@/components/VideoPlayer/index.vue';
import Popup from '/@/components/Popup/index.vue';
import { getDetailList, reUpload } from '/@/api/platform-manage/monitor-task';
import { getDicTionaryAndValue } from '/@/api/common/dic_response.js';
import { dictApi } from '/@/api/dict/index';
import { getFiles } from '/@/api/common/upload';
import { useMessage, useMessageBox } from '/@/hooks/message';
let playUrl = ref();
const popupRef = ref();
const videoPlayRef = ref();
const route = useRoute();
const reactiveData = reactive({
	statusForm: {},
});
//转播任务
const tableHeader = [
	{ label: '播放地址', prop: '播放地址' },
	{ label: '状态', prop: 'status' },
	{ label: '监控点编号', prop: 'cameraId' },
	{ label: '监控点名称', prop: 'cameraName' },
];

//录像任务
const recordTableHeader = [
	{ label: '播放地址', prop: '播放地址' },
	{ label: '状态', prop: 'status' },
	{ label: '监控点编号', prop: 'cameraId' },
	{ label: '监控点名称', prop: 'cameraName' },
	{ prop: 'videoFormat', label: '视频格式' },
];

const broadcastList = ref([]);
const recordList = ref([]);
//获取详情
function getDetail() {
	getDetailList(route.query.id).then(async ({ data }) => {
		//broadcastList
		broadcastList.value = data.broadcastList;
		for (const item of data.recordList) {
			if (item.cloudId) {
				let res1 = await getFiles([item.cloudId]);
				item.playUrl = res1.data[0]?.fileUrl;
			}
		}
		recordList.value = data.recordList;
	});
}
onMounted(() => {
	//获取详情
	getDetail();
	//字典
	getDic();
});
//获取字典
const getDic = () => {
	//状态
	getDicTionaryAndValue(dictApi.monitoringMent).then((resDic) => {
		resDic.data.forEach((i) => {
			reactiveData.statusForm[i.value] = i.label;
		});
	});
};
//Table表格操作
const onOperation = ({ field, row }) => {
	switch (field) {
		case 'reUpload':
			reUploadFu(row.id);
			break;
		case 'play':
			if (row.playUrl) {
				playUrl.value = row.playUrl;
				popupRef.value.open();
			} else {
				useMessage().warning('没有要播放的地址');
			}
			break;
	}
};

//重新上传
const reUploadFu = async (id) => {
	await useMessageBox().confirm('确定重新上传吗？');
	reUpload(id).then((res) => {
		getDetail();
		useMessage().success('上传成功');
	});
};

// -------------弹框
function close() {
	videoPlayRef.value.pause();
}
</script>
