<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-search ref="searchRef" inline label-position="left" :model="searchData" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" @loaded="onTableLoad" @dicLoaded="onDicLoaded" align="left"></co-table>
		</div>
	</div>
</template>
<script setup>
import { getMsgCommRecordList } from '/@/api/platform-manage/message-record';
// 定义变量内容
var getList = null;
// ----------------搜索配置
// 待搜索项回显
const searchData = ref({});
// 待搜索项
const searchConfig = ref({
	items: [
		{ prop: 'businessId', type: 'input', attrs: { placeholder: '请输入业务ID', label: '业务ID' } },
		{ prop: 'miId', type: 'input', attrs: { placeholder: '请输入主题编号', label: '主题编号' } },
		{ prop: 'sendChannel', option: 'sendChannel', type: 'select', attrs: { placeholder: '请选择通知类型', label: '通知类型' } },
		{ prop: 'resultType', option: 'resultType', type: 'select', attrs: { placeholder: '请选择通知结果类型', label: '通知结果类型' } },
	],
});
const dicData = ref({});
function onSearchHandle(params) {
	getList({ params });
}
// ----------------表格配置
const tableConfig = ref({
	dic: {
		sendChannel: {
			data: [
				{ value: '1', label: '站内信通知' },
				{ value: '2', label: '短信通知' },
				{ value: '3', label: '邮件通知' },
				{ value: '4', label: '微信通知' },
				{ value: '5', label: '语音通知' },
			],
		},
		resultType: {
			data: [
				{ value: '-1', label: '处理中' },
				{ value: '500', label: '发送失败' },
				{ value: '200', label: '发送成功' },
			],
		},
		commResult: {
			data: [
				{ value: '500', label: '发送失败' },
				{ value: '200', label: '发送成功' },
			],
		},
	},
	page: {
		response: {
			records: 'records', // 数据集合
		},
		request: {
			current: 'pageNum',
			size: 'pageSize',
		},
	},
	request: {
		apiName: getMsgCommRecordList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
	},
});
const tableHeader = ref([
	{ prop: 'businessId', label: '业务ID', minWidth: 200 },
	{ prop: 'title', label: '消息标题', minWidth: 200 },
	{ prop: 'miText', label: '消息内容', minWidth: 150 },
	{ prop: 'sendChannel', label: '通知类型', minWidth: 100 },
	{ prop: 'commResult', label: '结果类型', minWidth: 100 },
	{ prop: 'mtId', label: '模板编号', minWidth: 180 },
	{ prop: 'msgDesc', label: '模板名称', minWidth: 180 },
	{ prop: 'insertTime', label: '发送时间', minWidth: 180 },
]);
// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}
// table加载完成回调
function onTableLoad({ getDataList }) {
	getList = getDataList;
}
</script>
