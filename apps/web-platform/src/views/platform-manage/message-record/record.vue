<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<co-search ref="searchRef" inline label-position="left" :model="searchData" :config="searchConfig" :dic="dicData" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" @loaded="onTableLoad" @dicLoaded="onDicLoaded" align="left">
				<template #userId="{ row }">
					<span v-if="row.sendChannel == 1">{{ row.userId }}</span>
					<span v-if="row.sendChannel == 2">{{ row.mobileNo }}</span>
					<span v-if="row.sendChannel == 3">{{ row.email }}</span>
					<span v-if="row.sendChannel == 4">{{ row.openid }}</span>
					<span v-if="row.sendChannel == 5">{{ row.mobileNo }}</span>
				</template>
			</co-table>
		</div>
	</div>
</template>
<script setup>
import { getMsgParticipateList } from '/@/api/platform-manage/message-record';
// 定义变量内容
var getList = null;
// ----------------搜索配置
// 待搜索项回显
const searchData = ref({});
// 待搜索项
const searchConfig = ref({
	items: [
		{ prop: 'businessId', type: 'input', attrs: { placeholder: '请输入业务ID', label: '业务ID' } },
		{ prop: 'sendChannel', option: 'sendChannel', type: 'select', attrs: { placeholder: '请选择通知类型', label: '通知类型' } },
		{ prop: 'resultType', option: 'resultType', type: 'select', attrs: { placeholder: '请选择结果类型', label: '结果类型' } },
	],
});
const dicData = ref({});
function onSearchHandle(params) {
	getList({ params });
}
// ----------------表格配置
const tableConfig = ref({
	dic: {
		sendChannel: {
			data: [
				{ value: '1', label: '站内信通知' },
				{ value: '2', label: '短信通知' },
				{ value: '3', label: '邮件通知' },
				{ value: '4', label: '微信通知' },
				{ value: '5', label: '语音通知' },
			],
		},
		mpType: {
			data: [
				{ value: '1', label: '发送人' },
				{ value: '2', label: '接收人' },
				{ value: '3', label: '抄送人' },
			],
		},
		isRead: {
			data: [
				{ value: '0', label: '未读' },
				{ value: '1', label: '已读' },
			],
		},
		resultType: {
			data: [
				{ value: '-1', label: '处理中' },
				{ value: '500', label: '发送失败' },
				{ value: '200', label: '发送成功' },
			],
		},
	},
	page: {
		response: {
			records: 'records', // 数据集合
		},
		request: {
			current: 'pageNum',
			size: 'pageSize',
		},
	},
	request: {
		apiName: getMsgParticipateList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
	},
});
const tableHeader = ref([
	{ prop: 'businessId', label: '业务ID', width: 200 },
	{ prop: 'title', label: '消息标题', width: 200 },
	{ prop: 'miText', label: '消息内容', width: 150 },
	{ prop: 'sendChannel', label: '通知类型', width: 100 },
	{ prop: 'resultType', label: '结果类型', width: 100 },
	{ prop: 'responseTxt', label: '响应报文', width: 180 },
	{ prop: 'userId', label: '用户', width: 180 },
	{ prop: 'mpType', label: '用户身份', width: 100 },
	{ prop: 'isRead', label: '是否已读', width: 100 },
	{ prop: 'sendTime', label: '发送时间', width: 180 },
	{ prop: 'readTime', label: '已读时间', width: 180 },
]);
// 字典加载完毕
function onDicLoaded(data) {
	dicData.value = data;
}
// table加载完成回调
function onTableLoad({ getDataList }) {
	getList = getDataList;
}
</script>
