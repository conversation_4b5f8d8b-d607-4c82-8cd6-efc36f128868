<template>
  <div>
   <el-dialog :title="reactiveData.setting == 'add' ? '新增文件':'修改文件'" v-model="reactiveData.visible" width="1200" :close-on-click-modal="false" append-to-body
		destroy-on-close  >
    <el-form ref="coFormRef" :model="form" :rules="rules" label-width="150px">
			<el-form-item label="Icon:" prop="iconId">
				<uploadImg  v-model:id="form.iconId"  :limit="1" @change="fileChange" fileSize="10">
					<template #tip>
						<div>支持格式：.png,.jpg,.jpeg，单个文件不能超过10MB</div>
					</template>
				</uploadImg>
			</el-form-item>
			<el-form-item label="文件上传:" prop="fileId">
				<upl
          :key="uploadKey"
					:hasMultiple="false"
					:isShowFileList="false"
					:isHiddenTable="false"
					:isOperation="true"
					:file-ids="form.fileId"
					:maxFiles="1"
					:oper="{ remove: true }"
					@breakUpload="breakUploadBook"
				></upl>
			</el-form-item>
      <el-form-item label="文件名称:" prop="name">
				<el-input v-model="form.name" type="input" />
			</el-form-item>
      <el-form-item label="备注:" prop="remark">
				<el-input v-model="form.remark" type="textarea" />
			</el-form-item>
		</el-form>
    <template #footer>
        <el-button @click="reactiveData.visible = false">取消</el-button>
        <el-button type="primary" :loading="reactiveData.submitLoading" @click="onSubmit">确定</el-button>
		</template>
   </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { defineEmits, defineExpose } from 'vue';
import { add, edit  } from '/@/api/platform-manage/tool-download'
import { useMessage } from '/@/hooks/message';
const upl = defineAsyncComponent(() => import('/@/components/Upload/BigFile.vue'));
   //表单定义变量
   const form = ref<{
       iconId?: string;
       name?: string;
       remark?: string;
       fileId?: string;
       id?: string
    }>({
       iconId:'',
       name: '',
       remark: '',
      fileId: '',
      
  });
   //表单校验
  const rules = {
     name: [{ required: true, message: '文件名称不能为空', trigger: 'blur' }],
     fileId: [{ required: true, message: '请上传文件',trigger:'change' }],
     iconId: [{ required: true, message: '请上传Icon', }],
  };
  const uploadKey = ref(); 
  const reactiveData = reactive<{
       submitLoading: boolean;
       visible: boolean;
       setting: string;
   }>({
     submitLoading: false, //按钮控制
     visible: false, //弹框控制
     setting:'add', //弹框提示设置
  })
   // 表单数据校验
  const coFormRef = ref();
   //传递父组件方法
  const emit = defineEmits([ "parentEvent" ]);
   //打开dialog
  const openDialog = (type: string, row: any) => {
     reactiveData.visible = true
     reactiveData.setting = type
     uploadKey.value =  Math.random();
    if (type == 'add') {
      coFormRef.value?.resetFields();
      form.value = { name: '',  fileId: '', iconId:'' , };
     } else {
       form.value = row
     }
  }  
  
 //上传文件回调
  const breakUploadBook = (data:{ field:any, fsId:string }) => {
    form.value.fileId = data.fsId
    coFormRef.value.validateField('fileId').catch(() => {});
  }
  //上传Icon回调
  const fileChange = (res: any) => {
     form.value.iconId = res.id;
  };
   //表单提交
  const onSubmit = async () => {
    const valid = await coFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    const api = reactiveData.setting === 'edit' ? edit : add
    reactiveData.submitLoading = true
    api(form.value).then(res => {
      useMessage().success('提交成功');
      reactiveData.visible = false
      reactiveData.submitLoading = false;
      emit('parentEvent')
    }).catch(error => {
      useMessage().error(error.msg);
    })   
  }
   //暴露出方法
  defineExpose({ openDialog })
  
</script>