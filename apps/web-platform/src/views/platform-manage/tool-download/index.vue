<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view btn-align-center scorll">
			<co-search ref="searchRef" inline label-position="left" :config="searchConfig" @search="onSearchHandle" />
			<co-table ref="dsTableRef" :config="tableConfig" :header="tableHeader" @loaded="onTableLoad" single-mode="icon-hook" @operation="onOperation" align="left"> </co-table>
		</div>
		<editCom ref="editComRef" @parentEvent="onSearch()"></editCom>
	</div>
</template>
<script setup lang="ts">
import { getList, downloadDel } from '/@/api/platform-manage/tool-download';
import { useMessage, useMessageBox } from '/@/hooks/message';
const editCom = defineAsyncComponent(() => import('./edit.vue'));
//组件变量
const editComRef = ref();
var onSearch: any = null;
//搜索框配置
const searchConfig = {
	styles: { display: 'flex', alignItem: 'center' },
	inline: 'true',
	size: 'mini',
	labelPosition: 'left',
	items: [
		{
			prop: 'name',
			type: 'input',
			attrs: { placeholder: '请输入文件名称', clearable: true, label: '文件名称' },
		},
	],
};
//Table头部配置
const tableHeader = [{ prop: 'name', label: '文件名称' }];
// 表格配置
const tableConfig = ref({
	operation: {
		fixed: 'right',
		width: 220,
	},
	request: {
		apiName: getList, // 接口方法 一般从api文件中导入，或当前页面的某个方法
	},
});

//搜索框
const onSearchHandle = (data: any) => {
	onSearch({ params: data });
};
//Table 操作列回调
const onOperation = async ({ field, row }: any) => {
	switch (field) {
		case 'add':
			editComRef.value.openDialog('add');
			break;
		case 'edit':
			editComRef.value.openDialog('edit', row);
			break;
		case 'remove':
			await useMessageBox().confirm('此操作将永久删除, 是否继续?');
			downloadDel(row.id).then((res) => {
				useMessage().success('操作成功');
				onSearch();
			});
			break;
	}
};

//Table加载完回调
const onTableLoad = ({ getDataList }: any) => {
	onSearch = getDataList;
};
</script>
