作者：杨永康

时间：2025.02.16

版本：v0.1.0

说明：

1. 此组件已全局注册，在页面中直接使用即可，即可展示。

### 组件使用

```html
<ProParPreview ref="ProParPreviewRef"></ProParPreview>
```

在你需要调用票据预览页面，写入上述代码即可

完整实例页面：

```vue
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<ProParPreview ref="ProParPreviewRef"></ProParPreview>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
/**
 * 票据预览弹框
 */
const ProParPreviewRef = ref();
const receiptPreview = () => {
	/**
	 * 需要传入票据id
	 */
	ProParPreviewRef.value.initShow(id);
};
</script>
```

备注：其他未完成或问题会根据情况持续更新本文档！
